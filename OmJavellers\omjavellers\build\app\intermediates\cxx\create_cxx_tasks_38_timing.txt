# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 127ms
    [gap of 24ms]
    create-variant-model 17ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 16ms
    create-X86_64-model 12ms
    create-module-model 16ms
    create-variant-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 18ms
    create-X86_64-model 13ms
    create-module-model 27ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 407ms
  [gap of 94ms]
create_cxx_tasks completed in 511ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 21ms
    [gap of 20ms]
    create-ARM64_V8A-model 10ms
    create-X86-model 14ms
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-X86-model 12ms
    create-module-model 15ms
    [gap of 25ms]
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 211ms
  [gap of 15ms]
create_cxx_tasks completed in 226ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 18ms
    create-X86_64-model 11ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 12ms
    [gap of 23ms]
    create-module-model 17ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 13ms
    create-X86-model 10ms
    [gap of 10ms]
  create-initial-cxx-model completed in 250ms
create_cxx_tasks completed in 257ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 95ms]
  create-initial-cxx-model completed in 105ms
  [gap of 23ms]
create_cxx_tasks completed in 128ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 10ms]
    create-ARMEABI_V7A-model 12ms
    [gap of 55ms]
    create-module-model 13ms
    [gap of 35ms]
  create-initial-cxx-model completed in 125ms
  [gap of 34ms]
create_cxx_tasks completed in 159ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 37ms]
    create-module-model 11ms
    [gap of 62ms]
  create-initial-cxx-model completed in 110ms
create_cxx_tasks completed in 112ms

