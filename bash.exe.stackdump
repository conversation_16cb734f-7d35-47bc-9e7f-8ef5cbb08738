Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FE8E
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210286019, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA170  000210068E24 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA450  00021006A225 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF16CA0000 ntdll.dll
7FFF15BA0000 KERNEL32.DLL
7FFF13DF0000 KERNELBASE.dll
7FFF15FD0000 USER32.dll
7FFF143F0000 win32u.dll
7FFF16A70000 GDI32.dll
7FFF144C0000 gdi32full.dll
7FFF14600000 msvcp_win.dll
7FFF146B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF15C70000 advapi32.dll
7FFF16BB0000 msvcrt.dll
7FFF15280000 sechost.dll
7FFF15A80000 RPCRT4.dll
7FFF133F0000 CRYPTBASE.DLL
7FFF14420000 bcryptPrimitives.dll
7FFF15F90000 IMM32.DLL
