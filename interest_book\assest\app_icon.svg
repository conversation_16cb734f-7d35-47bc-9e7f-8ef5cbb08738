<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#455A64;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#37474F;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0277BD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0288D1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rounded rectangle -->
  <rect x="0" y="0" width="512" height="512" rx="102" ry="102" fill="url(#bgGradient)"/>
  
  <!-- Main book icon -->
  <g transform="translate(256,256)">
    <!-- Book base -->
    <rect x="-80" y="-60" width="160" height="120" rx="8" ry="8" fill="white" opacity="0.95"/>
    
    <!-- Book spine -->
    <rect x="-80" y="-60" width="20" height="120" rx="8" ry="8" fill="white" opacity="0.8"/>
    
    <!-- Book pages lines -->
    <line x1="-50" y1="-35" x2="60" y2="-35" stroke="#455A64" stroke-width="3" opacity="0.6"/>
    <line x1="-50" y1="-15" x2="60" y2="-15" stroke="#455A64" stroke-width="3" opacity="0.6"/>
    <line x1="-50" y1="5" x2="60" y2="5" stroke="#455A64" stroke-width="3" opacity="0.6"/>
    <line x1="-50" y1="25" x2="40" y2="25" stroke="#455A64" stroke-width="3" opacity="0.6"/>
    
    <!-- Currency symbol overlay circle -->
    <circle cx="50" cy="35" r="35" fill="url(#accentGradient)"/>
    <circle cx="50" cy="35" r="35" fill="none" stroke="white" stroke-width="4"/>
    
    <!-- Rupee symbol -->
    <g transform="translate(50,35)">
      <!-- Rupee symbol paths -->
      <path d="M-12,-8 L8,-8 M-12,-2 L4,-2 M-8,-8 L-8,12 M-8,2 L8,12" 
            stroke="white" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
  </g>
  
  <!-- Subtle shadow effect -->
  <ellipse cx="256" cy="480" rx="200" ry="20" fill="black" opacity="0.1"/>
</svg>
