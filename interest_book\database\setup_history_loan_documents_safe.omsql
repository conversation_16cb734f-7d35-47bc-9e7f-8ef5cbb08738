-- Setup History Loan Documents System (Safe Version)
-- This script creates the history loan documents system without assuming image field exists
-- Created: 2025-07-04

-- Step 1: Create history_loan_documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS `history_loan_documents` (
  `documentId` int(11) NOT NULL AUTO_INCREMENT,
  `loanId` int(5) NOT NULL COMMENT 'Original loan ID from historyloan table',
  `documentPath` varchar(255) NOT NULL,
  `archivedDate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the document was moved to history',
  PRIMARY KEY (`documentId`),
  KEY `idx_history_loan_id` (`loanId`),
  KEY `idx_archived_date` (`archivedDate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Step 2: Create procedure to safely delete loan documents after archiving
-- Drop the procedure if it already exists
DROP PROCEDURE IF EXISTS `CleanupLoanDocuments`;

DELIMITER $$
CREATE PROCEDURE `CleanupLoanDocuments`(IN loan_id INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- First, archive the documents
    INSERT INTO `history_loan_documents` (`loanId`, `documentPath`, `archivedDate`)
    SELECT loan_id, `documentPath`, NOW()
    FROM `loan_documents` 
    WHERE `loanId` = loan_id;
    
    -- Then delete the original documents
    DELETE FROM `loan_documents` WHERE `loanId` = loan_id;
    
    COMMIT;
END$$
DELIMITER ;

-- Step 3: Update the loan_documents foreign key to allow manual cleanup
-- Remove the existing foreign key constraint if it exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'loan_documents'
     AND CONSTRAINT_NAME = 'fk_loan_documents_loan') > 0,
    'ALTER TABLE `loan_documents` DROP FOREIGN KEY `fk_loan_documents_loan`',
    'SELECT "Foreign key fk_loan_documents_loan does not exist" AS message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add a new foreign key constraint without CASCADE (we'll handle it manually)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'loan_documents'
     AND CONSTRAINT_NAME = 'fk_loan_documents_loan') = 0,
    'ALTER TABLE `loan_documents` 
     ADD CONSTRAINT `fk_loan_documents_loan` 
     FOREIGN KEY (`loanId`) REFERENCES `loan` (`loanId`) 
     ON DELETE RESTRICT ON UPDATE CASCADE',
    'SELECT "Foreign key fk_loan_documents_loan already exists" AS message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 4: Update the backupedLoan trigger to use the cleanup procedure
DROP TRIGGER IF EXISTS `backupedLoan`;

DELIMITER $$
CREATE TRIGGER `backupedLoan` AFTER DELETE ON `loan` FOR EACH ROW 
BEGIN
    DECLARE customer_name VARCHAR(100) DEFAULT 'Unknown Customer';
    
    -- Get customer name
    SELECT custName INTO customer_name 
    FROM customer 
    WHERE custId = OLD.custId 
    LIMIT 1;
    
    -- If not found, try historycustomer table
    IF customer_name IS NULL OR customer_name = '' THEN
        SELECT custName INTO customer_name
        FROM historycustomer 
        WHERE custId = OLD.custId
        LIMIT 1;
    END IF;
    
    -- If still not found, use default
    IF customer_name IS NULL OR customer_name = '' THEN
        SET customer_name = 'Unknown Customer';
    END IF;
    
    -- Insert into historyloan without image field
    INSERT INTO historyloan (
        loanId, amount, rate, startDate, endDate, note, 
        updatedAmount, type, userId, custId, custName, paymentMode
    )
    VALUES (
        OLD.loanId, OLD.amount, OLD.rate, OLD.startDate, OLD.endDate, OLD.note,
        OLD.updatedAmount, OLD.type, OLD.userId, OLD.custId, customer_name, 
        COALESCE(OLD.paymentMode, 'cash')
    );
    
    -- Clean up loan documents (archive and delete)
    CALL CleanupLoanDocuments(OLD.loanId);
END$$
DELIMITER ;

-- Step 5: Ensure historyloan table has required fields
-- Add custName field if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'historyloan'
     AND COLUMN_NAME = 'custName') = 0,
    'ALTER TABLE `historyloan` ADD COLUMN `custName` VARCHAR(100) DEFAULT "Unknown Customer" COMMENT "Customer name preserved from customer table"',
    'SELECT "custName column already exists in historyloan table" AS message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add paymentMode field if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'historyloan'
     AND COLUMN_NAME = 'paymentMode') = 0,
    'ALTER TABLE `historyloan` ADD COLUMN `paymentMode` VARCHAR(10) NOT NULL DEFAULT "cash" COMMENT "Payment method: cash or online"',
    'SELECT "paymentMode column already exists in historyloan table" AS message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 6: Show current status
SELECT 'History Loan Documents Setup (Safe Version)' as Status;

SELECT 
    'Current History Documents' as Report,
    COUNT(*) as TotalHistoryDocuments,
    COUNT(DISTINCT loanId) as HistoryLoansWithDocuments
FROM `history_loan_documents`;

SELECT 
    'Current Active Documents' as Report,
    COUNT(*) as TotalActiveDocuments,
    COUNT(DISTINCT loanId) as ActiveLoansWithDocuments
FROM `loan_documents`;

SELECT 
    'History Loans' as Report,
    COUNT(*) as TotalHistoryLoans
FROM `historyloan`;

-- Step 7: Show sample data if any exists
SELECT 
    'Sample History Documents' as Report,
    hld.documentId,
    hld.loanId,
    hld.documentPath,
    hld.archivedDate
FROM `history_loan_documents` hld
ORDER BY hld.archivedDate DESC
LIMIT 5;

-- Step 8: Show table structures
DESCRIBE `history_loan_documents`;
DESCRIBE `historyloan`;

-- Final message
SELECT 'History loan documents system setup complete (safe version)!' as Message;
