[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "16", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "17", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "18", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "19", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "22", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "23", "D:\\Project\\salon-management-system\\src\\components\\Inventory.js": "24", "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js": "25", "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js": "26", "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js": "27", "D:\\Project\\salon-management-system\\src\\components\\Billing.js": "28", "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js": "29", "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js": "30", "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js": "31", "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js": "32"}, {"size": 535, "mtime": 1752673505339, "results": "33", "hashOfConfig": "34"}, {"size": 362, "mtime": 1752673505478, "results": "35", "hashOfConfig": "34"}, {"size": 4644, "mtime": 1752761346100, "results": "36", "hashOfConfig": "34"}, {"size": 15569, "mtime": 1752713162738, "results": "37", "hashOfConfig": "34"}, {"size": 7309, "mtime": 1752761358916, "results": "38", "hashOfConfig": "34"}, {"size": 14688, "mtime": 1752679633695, "results": "39", "hashOfConfig": "34"}, {"size": 15856, "mtime": 1752674665958, "results": "40", "hashOfConfig": "34"}, {"size": 15811, "mtime": 1752674110853, "results": "41", "hashOfConfig": "34"}, {"size": 45486, "mtime": 1752687579924, "results": "42", "hashOfConfig": "34"}, {"size": 42458, "mtime": 1752687289811, "results": "43", "hashOfConfig": "34"}, {"size": 4536, "mtime": 1752676872090, "results": "44", "hashOfConfig": "34"}, {"size": 9380, "mtime": 1752676890351, "results": "45", "hashOfConfig": "34"}, {"size": 9119, "mtime": 1752676328242, "results": "46", "hashOfConfig": "34"}, {"size": 6463, "mtime": 1752676861107, "results": "47", "hashOfConfig": "34"}, {"size": 5167, "mtime": 1752677065353, "results": "48", "hashOfConfig": "34"}, {"size": 10537, "mtime": 1752678651305, "results": "49", "hashOfConfig": "34"}, {"size": 2694, "mtime": 1752677885241, "results": "50", "hashOfConfig": "34"}, {"size": 6927, "mtime": 1752677912052, "results": "51", "hashOfConfig": "34"}, {"size": 9242, "mtime": 1752679654193, "results": "52", "hashOfConfig": "34"}, {"size": 9467, "mtime": 1752680244669, "results": "53", "hashOfConfig": "34"}, {"size": 9216, "mtime": 1752680272094, "results": "54", "hashOfConfig": "34"}, {"size": 9468, "mtime": 1752680301517, "results": "55", "hashOfConfig": "34"}, {"size": 15996, "mtime": 1752684454391, "results": "56", "hashOfConfig": "34"}, {"size": 31275, "mtime": 1752714827713, "results": "57", "hashOfConfig": "34"}, {"size": 9331, "mtime": 1752714575774, "results": "58", "hashOfConfig": "34"}, {"size": 11375, "mtime": 1752714307959, "results": "59", "hashOfConfig": "34"}, {"size": 13232, "mtime": 1752712848341, "results": "60", "hashOfConfig": "34"}, {"size": 26969, "mtime": 1752761763591, "results": "61", "hashOfConfig": "34"}, {"size": 22119, "mtime": 1752761143920, "results": "62", "hashOfConfig": "34"}, {"size": 13725, "mtime": 1752760940359, "results": "63", "hashOfConfig": "34"}, {"size": 11704, "mtime": 1752761211193, "results": "64", "hashOfConfig": "34"}, {"size": 15763, "mtime": 1752761280109, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", ["162"], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["163", "164", "165"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["166"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", ["167", "168", "169", "170"], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", ["171", "172"], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["173"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["174", "175", "176", "177", "178"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["179"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["180", "181", "182", "183"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["184", "185"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["186", "187", "188", "189"], [], "D:\\Project\\salon-management-system\\src\\components\\Inventory.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js", ["190", "191"], [], "D:\\Project\\salon-management-system\\src\\components\\Billing.js", ["192", "193", "194", "195", "196"], [], "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js", ["197", "198", "199", "200", "201", "202"], [], "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js", [], [], {"ruleId": "203", "severity": 1, "message": "204", "line": 39, "column": 5, "nodeType": "205", "messageId": "206", "endLine": 39, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "207", "line": 35, "column": 25, "nodeType": "205", "messageId": "206", "endLine": 35, "endColumn": 32}, {"ruleId": "203", "severity": 1, "message": "208", "line": 35, "column": 34, "nodeType": "205", "messageId": "206", "endLine": 35, "endColumn": 41}, {"ruleId": "203", "severity": 1, "message": "209", "line": 35, "column": 43, "nodeType": "205", "messageId": "206", "endLine": 35, "endColumn": 53}, {"ruleId": "203", "severity": 1, "message": "210", "line": 26, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 26, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "211", "line": 31, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 31, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "212", "line": 59, "column": 20, "nodeType": "205", "messageId": "206", "endLine": 59, "endColumn": 37}, {"ruleId": "203", "severity": 1, "message": "213", "line": 60, "column": 15, "nodeType": "205", "messageId": "206", "endLine": 60, "endColumn": 27}, {"ruleId": "203", "severity": 1, "message": "214", "line": 61, "column": 18, "nodeType": "205", "messageId": "206", "endLine": 61, "endColumn": 27}, {"ruleId": "203", "severity": 1, "message": "215", "line": 190, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 190, "endColumn": 23}, {"ruleId": "203", "severity": 1, "message": "216", "line": 190, "column": 25, "nodeType": "205", "messageId": "206", "endLine": 190, "endColumn": 41}, {"ruleId": "203", "severity": 1, "message": "217", "line": 18, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 18, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "218", "line": 2, "column": 30, "nodeType": "205", "messageId": "206", "endLine": 2, "endColumn": 37}, {"ruleId": "203", "severity": 1, "message": "219", "line": 2, "column": 39, "nodeType": "205", "messageId": "206", "endLine": 2, "endColumn": 47}, {"ruleId": "203", "severity": 1, "message": "220", "line": 2, "column": 49, "nodeType": "205", "messageId": "206", "endLine": 2, "endColumn": 58}, {"ruleId": "203", "severity": 1, "message": "221", "line": 2, "column": 60, "nodeType": "205", "messageId": "206", "endLine": 2, "endColumn": 68}, {"ruleId": "222", "severity": 1, "message": "223", "line": 264, "column": 47, "nodeType": "224", "messageId": "225", "endLine": 277, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "226", "line": 26, "column": 14, "nodeType": "205", "messageId": "206", "endLine": 26, "endColumn": 23}, {"ruleId": "203", "severity": 1, "message": "227", "line": 13, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 13, "endColumn": 7}, {"ruleId": "203", "severity": 1, "message": "228", "line": 14, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 14, "endColumn": 11}, {"ruleId": "203", "severity": 1, "message": "229", "line": 15, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 15, "endColumn": 15}, {"ruleId": "203", "severity": 1, "message": "230", "line": 21, "column": 11, "nodeType": "205", "messageId": "206", "endLine": 21, "endColumn": 19}, {"ruleId": "203", "severity": 1, "message": "231", "line": 9, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 9, "endColumn": 7}, {"ruleId": "203", "severity": 1, "message": "211", "line": 13, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 13, "endColumn": 10}, {"ruleId": "203", "severity": 1, "message": "232", "line": 6, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 6, "endColumn": 7}, {"ruleId": "203", "severity": 1, "message": "233", "line": 7, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 7, "endColumn": 14}, {"ruleId": "203", "severity": 1, "message": "231", "line": 14, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 14, "endColumn": 7}, {"ruleId": "203", "severity": 1, "message": "214", "line": 25, "column": 18, "nodeType": "205", "messageId": "206", "endLine": 25, "endColumn": 27}, {"ruleId": "203", "severity": 1, "message": "210", "line": 16, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 16, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "234", "line": 46, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 46, "endColumn": 18}, {"ruleId": "203", "severity": 1, "message": "210", "line": 32, "column": 3, "nodeType": "205", "messageId": "206", "endLine": 32, "endColumn": 8}, {"ruleId": "203", "severity": 1, "message": "235", "line": 42, "column": 14, "nodeType": "205", "messageId": "206", "endLine": 42, "endColumn": 25}, {"ruleId": "203", "severity": 1, "message": "236", "line": 46, "column": 17, "nodeType": "205", "messageId": "206", "endLine": 46, "endColumn": 29}, {"ruleId": "203", "severity": 1, "message": "237", "line": 60, "column": 5, "nodeType": "205", "messageId": "206", "endLine": 60, "endColumn": 19}, {"ruleId": "203", "severity": 1, "message": "238", "line": 117, "column": 9, "nodeType": "205", "messageId": "206", "endLine": 117, "endColumn": 29}, {"ruleId": "203", "severity": 1, "message": "239", "line": 28, "column": 13, "nodeType": "205", "messageId": "206", "endLine": 28, "endColumn": 23}, {"ruleId": "203", "severity": 1, "message": "240", "line": 31, "column": 17, "nodeType": "205", "messageId": "206", "endLine": 31, "endColumn": 27}, {"ruleId": "203", "severity": 1, "message": "241", "line": 47, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 47, "endColumn": 23}, {"ruleId": "203", "severity": 1, "message": "242", "line": 47, "column": 25, "nodeType": "205", "messageId": "206", "endLine": 47, "endColumn": 41}, {"ruleId": "203", "severity": 1, "message": "243", "line": 48, "column": 10, "nodeType": "205", "messageId": "206", "endLine": 48, "endColumn": 22}, {"ruleId": "203", "severity": 1, "message": "244", "line": 48, "column": 24, "nodeType": "205", "messageId": "206", "endLine": 48, "endColumn": 39}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'isAdmin' is assigned a value but never used.", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'packageErrors' is assigned a value but never used.", "'setPackageErrors' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'suppliers' is assigned a value but never used.", "'ReceiptIcon' is defined but never used.", "'DiscountIcon' is defined but never used.", "'processPayment' is assigned a value but never used.", "'handleProcessPayment' is assigned a value but never used.", "'PeopleIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'selectedMonth' is assigned a value but never used.", "'setSelectedMonth' is assigned a value but never used.", "'selectedYear' is assigned a value but never used.", "'setSelectedYear' is assigned a value but never used."]