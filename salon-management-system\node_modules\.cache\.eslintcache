[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "16", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "17", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "18", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "19", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "22", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "23", "D:\\Project\\salon-management-system\\src\\components\\Inventory.js": "24", "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js": "25", "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js": "26", "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js": "27", "D:\\Project\\salon-management-system\\src\\components\\Billing.js": "28", "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js": "29", "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js": "30", "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js": "31", "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js": "32", "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js": "33", "D:\\Project\\salon-management-system\\src\\utils\\pdfGenerator.js": "34"}, {"size": 535, "mtime": 1752673505339, "results": "35", "hashOfConfig": "36"}, {"size": 362, "mtime": 1752673505478, "results": "37", "hashOfConfig": "36"}, {"size": 4644, "mtime": 1752761346100, "results": "38", "hashOfConfig": "36"}, {"size": 15569, "mtime": 1752713162738, "results": "39", "hashOfConfig": "36"}, {"size": 7309, "mtime": 1752761358916, "results": "40", "hashOfConfig": "36"}, {"size": 14688, "mtime": 1752679633695, "results": "41", "hashOfConfig": "36"}, {"size": 15856, "mtime": 1752674665958, "results": "42", "hashOfConfig": "36"}, {"size": 15811, "mtime": 1752674110853, "results": "43", "hashOfConfig": "36"}, {"size": 45486, "mtime": 1752687579924, "results": "44", "hashOfConfig": "36"}, {"size": 42458, "mtime": 1752687289811, "results": "45", "hashOfConfig": "36"}, {"size": 4536, "mtime": 1752676872090, "results": "46", "hashOfConfig": "36"}, {"size": 9380, "mtime": 1752676890351, "results": "47", "hashOfConfig": "36"}, {"size": 9119, "mtime": 1752676328242, "results": "48", "hashOfConfig": "36"}, {"size": 6463, "mtime": 1752676861107, "results": "49", "hashOfConfig": "36"}, {"size": 5167, "mtime": 1752677065353, "results": "50", "hashOfConfig": "36"}, {"size": 10537, "mtime": 1752678651305, "results": "51", "hashOfConfig": "36"}, {"size": 2694, "mtime": 1752677885241, "results": "52", "hashOfConfig": "36"}, {"size": 6927, "mtime": 1752677912052, "results": "53", "hashOfConfig": "36"}, {"size": 9242, "mtime": 1752679654193, "results": "54", "hashOfConfig": "36"}, {"size": 9467, "mtime": 1752680244669, "results": "55", "hashOfConfig": "36"}, {"size": 9216, "mtime": 1752680272094, "results": "56", "hashOfConfig": "36"}, {"size": 9468, "mtime": 1752680301517, "results": "57", "hashOfConfig": "36"}, {"size": 15996, "mtime": 1752684454391, "results": "58", "hashOfConfig": "36"}, {"size": 31275, "mtime": 1752714827713, "results": "59", "hashOfConfig": "36"}, {"size": 9331, "mtime": 1752714575774, "results": "60", "hashOfConfig": "36"}, {"size": 11375, "mtime": 1752714307959, "results": "61", "hashOfConfig": "36"}, {"size": 13232, "mtime": 1752712848341, "results": "62", "hashOfConfig": "36"}, {"size": 30131, "mtime": 1752762225830, "results": "63", "hashOfConfig": "36"}, {"size": 22119, "mtime": 1752761143920, "results": "64", "hashOfConfig": "36"}, {"size": 13567, "mtime": 1752762775610, "results": "65", "hashOfConfig": "36"}, {"size": 10988, "mtime": 1752762633252, "results": "66", "hashOfConfig": "36"}, {"size": 15763, "mtime": 1752761280109, "results": "67", "hashOfConfig": "36"}, {"size": 15918, "mtime": 1752762540255, "results": "68", "hashOfConfig": "36"}, {"size": 5330, "mtime": 1752762699370, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", ["172"], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["173", "174", "175"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["176"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", ["177", "178", "179", "180"], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", ["181", "182"], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["183"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["184", "185", "186", "187", "188"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["189"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["190", "191", "192", "193"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["194", "195"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["196", "197", "198", "199"], [], "D:\\Project\\salon-management-system\\src\\components\\Inventory.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js", ["200", "201"], [], "D:\\Project\\salon-management-system\\src\\components\\Billing.js", ["202", "203", "204", "205", "206"], [], "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js", ["207", "208", "209", "210", "211", "212"], [], "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js", ["213", "214", "215", "216", "217"], [], "D:\\Project\\salon-management-system\\src\\utils\\pdfGenerator.js", [], [], {"ruleId": "218", "severity": 1, "message": "219", "line": 39, "column": 5, "nodeType": "220", "messageId": "221", "endLine": 39, "endColumn": 13}, {"ruleId": "218", "severity": 1, "message": "222", "line": 35, "column": 25, "nodeType": "220", "messageId": "221", "endLine": 35, "endColumn": 32}, {"ruleId": "218", "severity": 1, "message": "223", "line": 35, "column": 34, "nodeType": "220", "messageId": "221", "endLine": 35, "endColumn": 41}, {"ruleId": "218", "severity": 1, "message": "224", "line": 35, "column": 43, "nodeType": "220", "messageId": "221", "endLine": 35, "endColumn": 53}, {"ruleId": "218", "severity": 1, "message": "225", "line": 26, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 26, "endColumn": 8}, {"ruleId": "218", "severity": 1, "message": "226", "line": 31, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 31, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "227", "line": 59, "column": 20, "nodeType": "220", "messageId": "221", "endLine": 59, "endColumn": 37}, {"ruleId": "218", "severity": 1, "message": "228", "line": 60, "column": 15, "nodeType": "220", "messageId": "221", "endLine": 60, "endColumn": 27}, {"ruleId": "218", "severity": 1, "message": "229", "line": 61, "column": 18, "nodeType": "220", "messageId": "221", "endLine": 61, "endColumn": 27}, {"ruleId": "218", "severity": 1, "message": "230", "line": 190, "column": 10, "nodeType": "220", "messageId": "221", "endLine": 190, "endColumn": 23}, {"ruleId": "218", "severity": 1, "message": "231", "line": 190, "column": 25, "nodeType": "220", "messageId": "221", "endLine": 190, "endColumn": 41}, {"ruleId": "218", "severity": 1, "message": "232", "line": 18, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 18, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "233", "line": 2, "column": 30, "nodeType": "220", "messageId": "221", "endLine": 2, "endColumn": 37}, {"ruleId": "218", "severity": 1, "message": "234", "line": 2, "column": 39, "nodeType": "220", "messageId": "221", "endLine": 2, "endColumn": 47}, {"ruleId": "218", "severity": 1, "message": "235", "line": 2, "column": 49, "nodeType": "220", "messageId": "221", "endLine": 2, "endColumn": 58}, {"ruleId": "218", "severity": 1, "message": "236", "line": 2, "column": 60, "nodeType": "220", "messageId": "221", "endLine": 2, "endColumn": 68}, {"ruleId": "237", "severity": 1, "message": "238", "line": 264, "column": 47, "nodeType": "239", "messageId": "240", "endLine": 277, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "241", "line": 26, "column": 14, "nodeType": "220", "messageId": "221", "endLine": 26, "endColumn": 23}, {"ruleId": "218", "severity": 1, "message": "242", "line": 13, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 13, "endColumn": 7}, {"ruleId": "218", "severity": 1, "message": "243", "line": 14, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 14, "endColumn": 11}, {"ruleId": "218", "severity": 1, "message": "244", "line": 15, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 15, "endColumn": 15}, {"ruleId": "218", "severity": 1, "message": "245", "line": 21, "column": 11, "nodeType": "220", "messageId": "221", "endLine": 21, "endColumn": 19}, {"ruleId": "218", "severity": 1, "message": "246", "line": 9, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 9, "endColumn": 7}, {"ruleId": "218", "severity": 1, "message": "226", "line": 13, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 13, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "247", "line": 6, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 6, "endColumn": 7}, {"ruleId": "218", "severity": 1, "message": "248", "line": 7, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 7, "endColumn": 14}, {"ruleId": "218", "severity": 1, "message": "246", "line": 14, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 14, "endColumn": 7}, {"ruleId": "218", "severity": 1, "message": "229", "line": 25, "column": 18, "nodeType": "220", "messageId": "221", "endLine": 25, "endColumn": 27}, {"ruleId": "218", "severity": 1, "message": "225", "line": 16, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 16, "endColumn": 8}, {"ruleId": "218", "severity": 1, "message": "249", "line": 46, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 46, "endColumn": 18}, {"ruleId": "218", "severity": 1, "message": "225", "line": 32, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 32, "endColumn": 8}, {"ruleId": "218", "severity": 1, "message": "250", "line": 42, "column": 14, "nodeType": "220", "messageId": "221", "endLine": 42, "endColumn": 25}, {"ruleId": "218", "severity": 1, "message": "251", "line": 46, "column": 17, "nodeType": "220", "messageId": "221", "endLine": 46, "endColumn": 29}, {"ruleId": "218", "severity": 1, "message": "252", "line": 62, "column": 5, "nodeType": "220", "messageId": "221", "endLine": 62, "endColumn": 19}, {"ruleId": "218", "severity": 1, "message": "253", "line": 122, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 122, "endColumn": 29}, {"ruleId": "218", "severity": 1, "message": "254", "line": 28, "column": 13, "nodeType": "220", "messageId": "221", "endLine": 28, "endColumn": 23}, {"ruleId": "218", "severity": 1, "message": "255", "line": 31, "column": 17, "nodeType": "220", "messageId": "221", "endLine": 31, "endColumn": 27}, {"ruleId": "218", "severity": 1, "message": "256", "line": 47, "column": 10, "nodeType": "220", "messageId": "221", "endLine": 47, "endColumn": 23}, {"ruleId": "218", "severity": 1, "message": "257", "line": 47, "column": 25, "nodeType": "220", "messageId": "221", "endLine": 47, "endColumn": 41}, {"ruleId": "218", "severity": 1, "message": "258", "line": 48, "column": 10, "nodeType": "220", "messageId": "221", "endLine": 48, "endColumn": 22}, {"ruleId": "218", "severity": 1, "message": "259", "line": 48, "column": 24, "nodeType": "220", "messageId": "221", "endLine": 48, "endColumn": 39}, {"ruleId": "218", "severity": 1, "message": "260", "line": 10, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 10, "endColumn": 14}, {"ruleId": "218", "severity": 1, "message": "261", "line": 11, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 11, "endColumn": 13}, {"ruleId": "218", "severity": 1, "message": "262", "line": 12, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 12, "endColumn": 9}, {"ruleId": "218", "severity": 1, "message": "263", "line": 13, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 13, "endColumn": 11}, {"ruleId": "218", "severity": 1, "message": "264", "line": 37, "column": 41, "nodeType": "220", "messageId": "221", "endLine": 37, "endColumn": 57}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'isAdmin' is assigned a value but never used.", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'packageErrors' is assigned a value but never used.", "'setPackageErrors' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'suppliers' is assigned a value but never used.", "'ReceiptIcon' is defined but never used.", "'DiscountIcon' is defined but never used.", "'processPayment' is assigned a value but never used.", "'handleProcessPayment' is assigned a value but never used.", "'PeopleIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'selectedMonth' is assigned a value but never used.", "'setSelectedMonth' is assigned a value but never used.", "'selectedYear' is assigned a value but never used.", "'setSelectedYear' is assigned a value but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'validateDiscount' is assigned a value but never used."]