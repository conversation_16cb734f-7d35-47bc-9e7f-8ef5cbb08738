[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "16", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "17", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "18", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "19", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "22", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "23", "D:\\Project\\salon-management-system\\src\\components\\Inventory.js": "24", "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js": "25", "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js": "26", "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js": "27"}, {"size": 535, "mtime": 1752673505339, "results": "28", "hashOfConfig": "29"}, {"size": 362, "mtime": 1752673505478, "results": "30", "hashOfConfig": "29"}, {"size": 3947, "mtime": 1752713031631, "results": "31", "hashOfConfig": "29"}, {"size": 15569, "mtime": 1752713162738, "results": "32", "hashOfConfig": "29"}, {"size": 7158, "mtime": 1752713053651, "results": "33", "hashOfConfig": "29"}, {"size": 14688, "mtime": 1752679633695, "results": "34", "hashOfConfig": "29"}, {"size": 15856, "mtime": 1752674665958, "results": "35", "hashOfConfig": "29"}, {"size": 15811, "mtime": 1752674110853, "results": "36", "hashOfConfig": "29"}, {"size": 45486, "mtime": 1752687579924, "results": "37", "hashOfConfig": "29"}, {"size": 42458, "mtime": 1752687289811, "results": "38", "hashOfConfig": "29"}, {"size": 4536, "mtime": 1752676872090, "results": "39", "hashOfConfig": "29"}, {"size": 9380, "mtime": 1752676890351, "results": "40", "hashOfConfig": "29"}, {"size": 9119, "mtime": 1752676328242, "results": "41", "hashOfConfig": "29"}, {"size": 6463, "mtime": 1752676861107, "results": "42", "hashOfConfig": "29"}, {"size": 5167, "mtime": 1752677065353, "results": "43", "hashOfConfig": "29"}, {"size": 10537, "mtime": 1752678651305, "results": "44", "hashOfConfig": "29"}, {"size": 2694, "mtime": 1752677885241, "results": "45", "hashOfConfig": "29"}, {"size": 6927, "mtime": 1752677912052, "results": "46", "hashOfConfig": "29"}, {"size": 9242, "mtime": 1752679654193, "results": "47", "hashOfConfig": "29"}, {"size": 9467, "mtime": 1752680244669, "results": "48", "hashOfConfig": "29"}, {"size": 9216, "mtime": 1752680272094, "results": "49", "hashOfConfig": "29"}, {"size": 9468, "mtime": 1752680301517, "results": "50", "hashOfConfig": "29"}, {"size": 15996, "mtime": 1752684454391, "results": "51", "hashOfConfig": "29"}, {"size": 31223, "mtime": 1752714710661, "results": "52", "hashOfConfig": "29"}, {"size": 9331, "mtime": 1752714575774, "results": "53", "hashOfConfig": "29"}, {"size": 11375, "mtime": 1752714307959, "results": "54", "hashOfConfig": "29"}, {"size": 13232, "mtime": 1752712848341, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", ["137"], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["138", "139", "140"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["141"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", ["142", "143", "144", "145"], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", ["146", "147"], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["148"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["149", "150", "151", "152", "153"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["154"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["155", "156", "157", "158"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["159", "160"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["161", "162", "163", "164"], [], "D:\\Project\\salon-management-system\\src\\components\\Inventory.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js", ["165", "166"], [], {"ruleId": "167", "severity": 1, "message": "168", "line": 39, "column": 5, "nodeType": "169", "messageId": "170", "endLine": 39, "endColumn": 13}, {"ruleId": "167", "severity": 1, "message": "171", "line": 35, "column": 25, "nodeType": "169", "messageId": "170", "endLine": 35, "endColumn": 32}, {"ruleId": "167", "severity": 1, "message": "172", "line": 35, "column": 34, "nodeType": "169", "messageId": "170", "endLine": 35, "endColumn": 41}, {"ruleId": "167", "severity": 1, "message": "173", "line": 35, "column": 43, "nodeType": "169", "messageId": "170", "endLine": 35, "endColumn": 53}, {"ruleId": "167", "severity": 1, "message": "174", "line": 26, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 26, "endColumn": 8}, {"ruleId": "167", "severity": 1, "message": "175", "line": 31, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 31, "endColumn": 10}, {"ruleId": "167", "severity": 1, "message": "176", "line": 59, "column": 20, "nodeType": "169", "messageId": "170", "endLine": 59, "endColumn": 37}, {"ruleId": "167", "severity": 1, "message": "177", "line": 60, "column": 15, "nodeType": "169", "messageId": "170", "endLine": 60, "endColumn": 27}, {"ruleId": "167", "severity": 1, "message": "178", "line": 61, "column": 18, "nodeType": "169", "messageId": "170", "endLine": 61, "endColumn": 27}, {"ruleId": "167", "severity": 1, "message": "179", "line": 190, "column": 10, "nodeType": "169", "messageId": "170", "endLine": 190, "endColumn": 23}, {"ruleId": "167", "severity": 1, "message": "180", "line": 190, "column": 25, "nodeType": "169", "messageId": "170", "endLine": 190, "endColumn": 41}, {"ruleId": "167", "severity": 1, "message": "181", "line": 18, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 18, "endColumn": 10}, {"ruleId": "167", "severity": 1, "message": "182", "line": 2, "column": 30, "nodeType": "169", "messageId": "170", "endLine": 2, "endColumn": 37}, {"ruleId": "167", "severity": 1, "message": "183", "line": 2, "column": 39, "nodeType": "169", "messageId": "170", "endLine": 2, "endColumn": 47}, {"ruleId": "167", "severity": 1, "message": "184", "line": 2, "column": 49, "nodeType": "169", "messageId": "170", "endLine": 2, "endColumn": 58}, {"ruleId": "167", "severity": 1, "message": "185", "line": 2, "column": 60, "nodeType": "169", "messageId": "170", "endLine": 2, "endColumn": 68}, {"ruleId": "186", "severity": 1, "message": "187", "line": 264, "column": 47, "nodeType": "188", "messageId": "189", "endLine": 277, "endColumn": 10}, {"ruleId": "167", "severity": 1, "message": "190", "line": 26, "column": 14, "nodeType": "169", "messageId": "170", "endLine": 26, "endColumn": 23}, {"ruleId": "167", "severity": 1, "message": "191", "line": 13, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 13, "endColumn": 7}, {"ruleId": "167", "severity": 1, "message": "192", "line": 14, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 14, "endColumn": 11}, {"ruleId": "167", "severity": 1, "message": "193", "line": 15, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 15, "endColumn": 15}, {"ruleId": "167", "severity": 1, "message": "194", "line": 21, "column": 11, "nodeType": "169", "messageId": "170", "endLine": 21, "endColumn": 19}, {"ruleId": "167", "severity": 1, "message": "195", "line": 9, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 9, "endColumn": 7}, {"ruleId": "167", "severity": 1, "message": "175", "line": 13, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 13, "endColumn": 10}, {"ruleId": "167", "severity": 1, "message": "196", "line": 6, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 6, "endColumn": 7}, {"ruleId": "167", "severity": 1, "message": "197", "line": 7, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 7, "endColumn": 14}, {"ruleId": "167", "severity": 1, "message": "195", "line": 14, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 14, "endColumn": 7}, {"ruleId": "167", "severity": 1, "message": "178", "line": 25, "column": 18, "nodeType": "169", "messageId": "170", "endLine": 25, "endColumn": 27}, {"ruleId": "167", "severity": 1, "message": "174", "line": 16, "column": 3, "nodeType": "169", "messageId": "170", "endLine": 16, "endColumn": 8}, {"ruleId": "167", "severity": 1, "message": "198", "line": 46, "column": 9, "nodeType": "169", "messageId": "170", "endLine": 46, "endColumn": 18}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'isAdmin' is assigned a value but never used.", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'packageErrors' is assigned a value but never used.", "'setPackageErrors' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'suppliers' is assigned a value but never used."]