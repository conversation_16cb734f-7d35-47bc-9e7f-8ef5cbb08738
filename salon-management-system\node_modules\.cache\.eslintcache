[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "16", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "17", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "18", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "19", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "22", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "23", "D:\\Project\\salon-management-system\\src\\components\\Inventory.js": "24", "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js": "25", "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js": "26", "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js": "27", "D:\\Project\\salon-management-system\\src\\components\\Billing.js": "28", "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js": "29", "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js": "30", "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js": "31", "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js": "32", "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js": "33"}, {"size": 535, "mtime": 1752673505339, "results": "34", "hashOfConfig": "35"}, {"size": 362, "mtime": 1752673505478, "results": "36", "hashOfConfig": "35"}, {"size": 4644, "mtime": 1752761346100, "results": "37", "hashOfConfig": "35"}, {"size": 15569, "mtime": 1752713162738, "results": "38", "hashOfConfig": "35"}, {"size": 7309, "mtime": 1752761358916, "results": "39", "hashOfConfig": "35"}, {"size": 14688, "mtime": 1752679633695, "results": "40", "hashOfConfig": "35"}, {"size": 15856, "mtime": 1752674665958, "results": "41", "hashOfConfig": "35"}, {"size": 15811, "mtime": 1752674110853, "results": "42", "hashOfConfig": "35"}, {"size": 45486, "mtime": 1752687579924, "results": "43", "hashOfConfig": "35"}, {"size": 42458, "mtime": 1752687289811, "results": "44", "hashOfConfig": "35"}, {"size": 4536, "mtime": 1752676872090, "results": "45", "hashOfConfig": "35"}, {"size": 9380, "mtime": 1752676890351, "results": "46", "hashOfConfig": "35"}, {"size": 9119, "mtime": 1752676328242, "results": "47", "hashOfConfig": "35"}, {"size": 6463, "mtime": 1752676861107, "results": "48", "hashOfConfig": "35"}, {"size": 5167, "mtime": 1752677065353, "results": "49", "hashOfConfig": "35"}, {"size": 10537, "mtime": 1752678651305, "results": "50", "hashOfConfig": "35"}, {"size": 2694, "mtime": 1752677885241, "results": "51", "hashOfConfig": "35"}, {"size": 6927, "mtime": 1752677912052, "results": "52", "hashOfConfig": "35"}, {"size": 9242, "mtime": 1752679654193, "results": "53", "hashOfConfig": "35"}, {"size": 9467, "mtime": 1752680244669, "results": "54", "hashOfConfig": "35"}, {"size": 9216, "mtime": 1752680272094, "results": "55", "hashOfConfig": "35"}, {"size": 9468, "mtime": 1752680301517, "results": "56", "hashOfConfig": "35"}, {"size": 15996, "mtime": 1752684454391, "results": "57", "hashOfConfig": "35"}, {"size": 31275, "mtime": 1752714827713, "results": "58", "hashOfConfig": "35"}, {"size": 9331, "mtime": 1752714575774, "results": "59", "hashOfConfig": "35"}, {"size": 11375, "mtime": 1752714307959, "results": "60", "hashOfConfig": "35"}, {"size": 13232, "mtime": 1752712848341, "results": "61", "hashOfConfig": "35"}, {"size": 32341, "mtime": 1752761992862, "results": "62", "hashOfConfig": "35"}, {"size": 22119, "mtime": 1752761143920, "results": "63", "hashOfConfig": "35"}, {"size": 13725, "mtime": 1752760940359, "results": "64", "hashOfConfig": "35"}, {"size": 11704, "mtime": 1752761211193, "results": "65", "hashOfConfig": "35"}, {"size": 15763, "mtime": 1752761280109, "results": "66", "hashOfConfig": "35"}, {"size": 17099, "mtime": 1752761858244, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", ["167"], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["168", "169", "170"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["171"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", ["172", "173", "174", "175"], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", ["176", "177"], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["178"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["179", "180", "181", "182", "183"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["184"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["185", "186", "187", "188"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["189", "190"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["191", "192", "193", "194"], [], "D:\\Project\\salon-management-system\\src\\components\\Inventory.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js", ["195", "196"], [], "D:\\Project\\salon-management-system\\src\\components\\Billing.js", ["197", "198", "199", "200", "201"], [], "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js", ["202", "203", "204", "205", "206", "207"], [], "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js", ["208", "209", "210", "211", "212"], [], {"ruleId": "213", "severity": 1, "message": "214", "line": 39, "column": 5, "nodeType": "215", "messageId": "216", "endLine": 39, "endColumn": 13}, {"ruleId": "213", "severity": 1, "message": "217", "line": 35, "column": 25, "nodeType": "215", "messageId": "216", "endLine": 35, "endColumn": 32}, {"ruleId": "213", "severity": 1, "message": "218", "line": 35, "column": 34, "nodeType": "215", "messageId": "216", "endLine": 35, "endColumn": 41}, {"ruleId": "213", "severity": 1, "message": "219", "line": 35, "column": 43, "nodeType": "215", "messageId": "216", "endLine": 35, "endColumn": 53}, {"ruleId": "213", "severity": 1, "message": "220", "line": 26, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 26, "endColumn": 8}, {"ruleId": "213", "severity": 1, "message": "221", "line": 31, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 31, "endColumn": 10}, {"ruleId": "213", "severity": 1, "message": "222", "line": 59, "column": 20, "nodeType": "215", "messageId": "216", "endLine": 59, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "223", "line": 60, "column": 15, "nodeType": "215", "messageId": "216", "endLine": 60, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "224", "line": 61, "column": 18, "nodeType": "215", "messageId": "216", "endLine": 61, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "225", "line": 190, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 190, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "226", "line": 190, "column": 25, "nodeType": "215", "messageId": "216", "endLine": 190, "endColumn": 41}, {"ruleId": "213", "severity": 1, "message": "227", "line": 18, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 18, "endColumn": 10}, {"ruleId": "213", "severity": 1, "message": "228", "line": 2, "column": 30, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "229", "line": 2, "column": 39, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 47}, {"ruleId": "213", "severity": 1, "message": "230", "line": 2, "column": 49, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 58}, {"ruleId": "213", "severity": 1, "message": "231", "line": 2, "column": 60, "nodeType": "215", "messageId": "216", "endLine": 2, "endColumn": 68}, {"ruleId": "232", "severity": 1, "message": "233", "line": 264, "column": 47, "nodeType": "234", "messageId": "235", "endLine": 277, "endColumn": 10}, {"ruleId": "213", "severity": 1, "message": "236", "line": 26, "column": 14, "nodeType": "215", "messageId": "216", "endLine": 26, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "237", "line": 13, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 13, "endColumn": 7}, {"ruleId": "213", "severity": 1, "message": "238", "line": 14, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 14, "endColumn": 11}, {"ruleId": "213", "severity": 1, "message": "239", "line": 15, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 15, "endColumn": 15}, {"ruleId": "213", "severity": 1, "message": "240", "line": 21, "column": 11, "nodeType": "215", "messageId": "216", "endLine": 21, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "241", "line": 9, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 9, "endColumn": 7}, {"ruleId": "213", "severity": 1, "message": "221", "line": 13, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 13, "endColumn": 10}, {"ruleId": "213", "severity": 1, "message": "242", "line": 6, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 6, "endColumn": 7}, {"ruleId": "213", "severity": 1, "message": "243", "line": 7, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 7, "endColumn": 14}, {"ruleId": "213", "severity": 1, "message": "241", "line": 14, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 14, "endColumn": 7}, {"ruleId": "213", "severity": 1, "message": "224", "line": 25, "column": 18, "nodeType": "215", "messageId": "216", "endLine": 25, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "220", "line": 16, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 16, "endColumn": 8}, {"ruleId": "213", "severity": 1, "message": "244", "line": 46, "column": 9, "nodeType": "215", "messageId": "216", "endLine": 46, "endColumn": 18}, {"ruleId": "213", "severity": 1, "message": "220", "line": 32, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 32, "endColumn": 8}, {"ruleId": "213", "severity": 1, "message": "245", "line": 42, "column": 14, "nodeType": "215", "messageId": "216", "endLine": 42, "endColumn": 25}, {"ruleId": "213", "severity": 1, "message": "246", "line": 46, "column": 17, "nodeType": "215", "messageId": "216", "endLine": 46, "endColumn": 29}, {"ruleId": "213", "severity": 1, "message": "247", "line": 61, "column": 5, "nodeType": "215", "messageId": "216", "endLine": 61, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "248", "line": 121, "column": 9, "nodeType": "215", "messageId": "216", "endLine": 121, "endColumn": 29}, {"ruleId": "213", "severity": 1, "message": "249", "line": 28, "column": 13, "nodeType": "215", "messageId": "216", "endLine": 28, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "250", "line": 31, "column": 17, "nodeType": "215", "messageId": "216", "endLine": 31, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "251", "line": 47, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 47, "endColumn": 23}, {"ruleId": "213", "severity": 1, "message": "252", "line": 47, "column": 25, "nodeType": "215", "messageId": "216", "endLine": 47, "endColumn": 41}, {"ruleId": "213", "severity": 1, "message": "253", "line": 48, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 48, "endColumn": 22}, {"ruleId": "213", "severity": 1, "message": "254", "line": 48, "column": 24, "nodeType": "215", "messageId": "216", "endLine": 48, "endColumn": 39}, {"ruleId": "213", "severity": 1, "message": "255", "line": 10, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 10, "endColumn": 14}, {"ruleId": "213", "severity": 1, "message": "256", "line": 11, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 11, "endColumn": 13}, {"ruleId": "213", "severity": 1, "message": "257", "line": 12, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 12, "endColumn": 9}, {"ruleId": "213", "severity": 1, "message": "258", "line": 13, "column": 3, "nodeType": "215", "messageId": "216", "endLine": 13, "endColumn": 11}, {"ruleId": "213", "severity": 1, "message": "259", "line": 37, "column": 41, "nodeType": "215", "messageId": "216", "endLine": 37, "endColumn": 57}, "no-unused-vars", "'products' is assigned a value but never used.", "Identifier", "unusedVar", "'isAdmin' is assigned a value but never used.", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used.", "'Alert' is defined but never used.", "'Divider' is defined but never used.", "'NotificationsIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'MoneyIcon' is defined but never used.", "'packageErrors' is assigned a value but never used.", "'setPackageErrors' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'suppliers' is assigned a value but never used.", "'ReceiptIcon' is defined but never used.", "'DiscountIcon' is defined but never used.", "'processPayment' is assigned a value but never used.", "'handleProcessPayment' is assigned a value but never used.", "'PeopleIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'selectedMonth' is assigned a value but never used.", "'setSelectedMonth' is assigned a value but never used.", "'selectedYear' is assigned a value but never used.", "'setSelectedYear' is assigned a value but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'validateDiscount' is assigned a value but never used."]