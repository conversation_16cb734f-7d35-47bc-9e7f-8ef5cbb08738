{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, Typography, Grid, Card, CardContent, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, Button, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tooltip, Alert, Badge, Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions, LinearProgress } from '@mui/material';\nimport { Search as SearchIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Warning as WarningIcon, Inventory as InventoryIcon, TrendingDown as TrendingDownIcon, AttachMoney as MoneyIcon, Category as CategoryIcon, FilterList as FilterIcon, Refresh as RefreshIcon, GetApp as ExportIcon } from '@mui/icons-material';\nimport { useInventory } from '../contexts/InventoryContext';\nimport ProductForm from './ProductForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const {\n    products,\n    stockMovements,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getExpiringProducts,\n    getTotalInventoryValue,\n    getCategories,\n    deleteProduct,\n    addStockMovement,\n    getProductById\n  } = useInventory();\n  const [currentTab, setCurrentTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [productFormOpen, setProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formMode, setFormMode] = useState('add');\n  const [restockDialogOpen, setRestockDialogOpen] = useState(false);\n  const [selectedProductForRestock, setSelectedProductForRestock] = useState(null);\n  const [restockQuantity, setRestockQuantity] = useState('');\n  const [restockReason, setRestockReason] = useState('Regular restock');\n\n  // Get filtered products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.sku.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;\n    const matchesStatus = statusFilter === 'all' || statusFilter === 'low' && product.currentStock <= product.minStockLevel || statusFilter === 'out' && product.currentStock === 0 || statusFilter === 'normal' && product.currentStock > product.minStockLevel;\n    return matchesSearch && matchesCategory && matchesStatus;\n  });\n\n  // Calculate statistics\n  const lowStockProducts = getLowStockProducts();\n  const outOfStockProducts = getOutOfStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const totalValue = getTotalInventoryValue();\n  const categories = getCategories();\n  const getStockStatus = product => {\n    if (product.currentStock === 0) return {\n      status: 'Out of Stock',\n      color: 'error'\n    };\n    if (product.currentStock <= product.minStockLevel) return {\n      status: 'Low Stock',\n      color: 'warning'\n    };\n    return {\n      status: 'In Stock',\n      color: 'success'\n    };\n  };\n  const getStockPercentage = product => {\n    return Math.min(product.currentStock / product.maxStockLevel * 100, 100);\n  };\n  const handleDeleteProduct = product => {\n    setProductToDelete(product);\n    setDeleteDialogOpen(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setFormMode('edit');\n    setProductFormOpen(true);\n  };\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setFormMode('add');\n    setProductFormOpen(true);\n  };\n  const handleCloseProductForm = () => {\n    setProductFormOpen(false);\n    setEditingProduct(null);\n  };\n  const confirmDelete = () => {\n    if (productToDelete) {\n      deleteProduct(productToDelete.id);\n      setDeleteDialogOpen(false);\n      setProductToDelete(null);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"Inventory Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 24\n          }, this),\n          onClick: () => window.location.reload(),\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 24\n          }, this),\n          children: \"Export\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 24\n          }, this),\n          sx: {\n            bgcolor: 'primary.main'\n          },\n          onClick: handleAddProduct,\n          children: \"Add Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: products.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(InventoryIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Low Stock Items\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"warning.main\",\n                  children: lowStockProducts.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: lowStockProducts.length,\n                color: \"warning\",\n                children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                  sx: {\n                    fontSize: 40,\n                    color: 'warning.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Out of Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"error.main\",\n                  children: outOfStockProducts.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'error.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: formatCurrency(totalValue)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MoneyIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'success.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), lowStockProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [lowStockProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this), \" are running low on stock and need restocking.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this), outOfStockProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [outOfStockProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), \" are out of stock.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this), expiringProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [expiringProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this), \" are expiring within 30 days.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: currentTab,\n        onChange: (e, newValue) => setCurrentTab(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Low Stock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Expiring Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Stock Movements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search products...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: categoryFilter,\n                onChange: e => setCategoryFilter(e.target.value),\n                label: \"Category\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stock Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: statusFilter,\n                onChange: e => setStatusFilter(e.target.value),\n                label: \"Stock Status\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"normal\",\n                  children: \"In Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"low\",\n                  children: \"Low Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"out\",\n                  children: \"Out of Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 28\n              }, this),\n              onClick: () => {\n                setSearchTerm('');\n                setCategoryFilter('all');\n                setStatusFilter('all');\n              },\n              children: \"Clear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"SKU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stock Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Unit Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Total Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredProducts.map(product => {\n              const stockStatus = getStockStatus(product);\n              const stockPercentage = getStockPercentage(product);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: product.brand\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.sku\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: product.category,\n                    size: \"small\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [product.currentStock, \" / \", product.maxStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: stockPercentage,\n                    sx: {\n                      mt: 1,\n                      height: 6,\n                      borderRadius: 3\n                    },\n                    color: stockStatus.color\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [\"Min: \", product.minStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [\"Max: \", product.maxStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatCurrency(product.unitPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatCurrency(product.currentStock * product.unitPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: stockStatus.status,\n                    color: stockStatus.color,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Edit Product\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        onClick: () => handleEditProduct(product),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Delete Product\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"error\",\n                        onClick: () => handleDeleteProduct(product),\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 450,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Min Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Shortage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Last Restocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: lowStockProducts.map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: \"bold\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [product.brand, \" - \", product.sku]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: product.currentStock,\n                  color: \"warning\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: product.minStockLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"error.main\",\n                  fontWeight: \"bold\",\n                  children: product.minStockLevel - product.currentStock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: product.lastRestocked\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  size: \"small\",\n                  color: \"warning\",\n                  children: \"Restock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 2,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Expiry Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Days Until Expiry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: expiringProducts.map(product => {\n              const daysUntilExpiry = Math.ceil((new Date(product.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [product.brand, \" - \", product.sku]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.currentStock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.expiryDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${daysUntilExpiry} days`,\n                    color: daysUntilExpiry <= 7 ? 'error' : 'warning',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    color: \"primary\",\n                    children: \"Mark as Used\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Recent Stock Movements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"textSecondary\",\n          children: \"Stock movements functionality will be implemented in the next phase.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", productToDelete === null || productToDelete === void 0 ? void 0 : productToDelete.name, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: confirmDelete,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductForm, {\n      open: productFormOpen,\n      onClose: handleCloseProductForm,\n      product: editingProduct,\n      mode: formMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"xN92E4Z3V7RtXn8mEO6IsfLnEK8=\", false, function () {\n  return [useInventory];\n});\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "LinearProgress", "Search", "SearchIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "Inventory", "InventoryIcon", "TrendingDown", "TrendingDownIcon", "AttachMoney", "MoneyIcon", "Category", "CategoryIcon", "FilterList", "FilterIcon", "Refresh", "RefreshIcon", "GetApp", "ExportIcon", "useInventory", "ProductForm", "jsxDEV", "_jsxDEV", "_s", "products", "stockMovements", "getLowStockProducts", "getOutOfStockProducts", "getExpiringProducts", "getTotalInventoryValue", "getCategories", "deleteProduct", "addStockMovement", "getProductById", "currentTab", "setCurrentTab", "searchTerm", "setSearchTerm", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "deleteDialogOpen", "setDeleteDialogOpen", "productToDelete", "setProductToDelete", "productFormOpen", "setProductFormOpen", "editingProduct", "setEditingProduct", "formMode", "setFormMode", "restockDialogOpen", "setRestockDialogOpen", "selectedProductForRestock", "setSelectedProductForRestock", "restockQuantity", "setRestockQuantity", "restockReason", "setRestockReason", "filteredProducts", "filter", "product", "matchesSearch", "name", "toLowerCase", "includes", "sku", "brand", "matchesCategory", "category", "matchesStatus", "currentStock", "minStockLevel", "lowStockProducts", "outOfStockProducts", "expiringProducts", "totalValue", "categories", "getStockStatus", "status", "color", "getStockPercentage", "Math", "min", "maxStockLevel", "handleDeleteProduct", "handleEditProduct", "handleAddProduct", "handleCloseProductForm", "confirmDelete", "id", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "TabPanel", "children", "value", "index", "hidden", "sx", "pt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "mb", "display", "justifyContent", "alignItems", "variant", "component", "fontWeight", "gap", "startIcon", "onClick", "window", "location", "reload", "bgcolor", "container", "spacing", "item", "xs", "sm", "md", "gutterBottom", "length", "fontSize", "badgeContent", "severity", "onChange", "e", "newValue", "label", "fullWidth", "placeholder", "target", "InputProps", "startAdornment", "position", "map", "stockStatus", "stockPercentage", "size", "mt", "height", "borderRadius", "unitPrice", "title", "lastRestocked", "daysUntilExpiry", "ceil", "Date", "expiryDate", "open", "onClose", "mode", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Inventory.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  TextField,\n  InputAdornment,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Button,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tooltip,\n  Alert,\n  Badge,\n  Tabs,\n  Tab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  LinearProgress\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  Inventory as InventoryIcon,\n  TrendingDown as TrendingDownIcon,\n  AttachMoney as MoneyIcon,\n  Category as CategoryIcon,\n  FilterList as FilterIcon,\n  Refresh as RefreshIcon,\n  GetApp as ExportIcon\n} from '@mui/icons-material';\nimport { useInventory } from '../contexts/InventoryContext';\nimport ProductForm from './ProductForm';\n\nconst Inventory = () => {\n  const {\n    products,\n    stockMovements,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getExpiringProducts,\n    getTotalInventoryValue,\n    getCategories,\n    deleteProduct,\n    addStockMovement,\n    getProductById\n  } = useInventory();\n\n  const [currentTab, setCurrentTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [productFormOpen, setProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formMode, setFormMode] = useState('add');\n  const [restockDialogOpen, setRestockDialogOpen] = useState(false);\n  const [selectedProductForRestock, setSelectedProductForRestock] = useState(null);\n  const [restockQuantity, setRestockQuantity] = useState('');\n  const [restockReason, setRestockReason] = useState('Regular restock');\n\n  // Get filtered products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;\n    const matchesStatus = statusFilter === 'all' || \n                         (statusFilter === 'low' && product.currentStock <= product.minStockLevel) ||\n                         (statusFilter === 'out' && product.currentStock === 0) ||\n                         (statusFilter === 'normal' && product.currentStock > product.minStockLevel);\n    \n    return matchesSearch && matchesCategory && matchesStatus;\n  });\n\n  // Calculate statistics\n  const lowStockProducts = getLowStockProducts();\n  const outOfStockProducts = getOutOfStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const totalValue = getTotalInventoryValue();\n  const categories = getCategories();\n\n  const getStockStatus = (product) => {\n    if (product.currentStock === 0) return { status: 'Out of Stock', color: 'error' };\n    if (product.currentStock <= product.minStockLevel) return { status: 'Low Stock', color: 'warning' };\n    return { status: 'In Stock', color: 'success' };\n  };\n\n  const getStockPercentage = (product) => {\n    return Math.min((product.currentStock / product.maxStockLevel) * 100, 100);\n  };\n\n  const handleDeleteProduct = (product) => {\n    setProductToDelete(product);\n    setDeleteDialogOpen(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setFormMode('edit');\n    setProductFormOpen(true);\n  };\n\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setFormMode('add');\n    setProductFormOpen(true);\n  };\n\n  const handleCloseProductForm = () => {\n    setProductFormOpen(false);\n    setEditingProduct(null);\n  };\n\n  const confirmDelete = () => {\n    if (productToDelete) {\n      deleteProduct(productToDelete.id);\n      setDeleteDialogOpen(false);\n      setProductToDelete(null);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 'bold' }}>\n          Inventory Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => window.location.reload()}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n          >\n            Export\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            sx={{ bgcolor: 'primary.main' }}\n            onClick={handleAddProduct}\n          >\n            Add Product\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Products\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {products.length}\n                  </Typography>\n                </Box>\n                <InventoryIcon sx={{ fontSize: 40, color: 'primary.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Low Stock Items\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"warning.main\">\n                    {lowStockProducts.length}\n                  </Typography>\n                </Box>\n                <Badge badgeContent={lowStockProducts.length} color=\"warning\">\n                  <WarningIcon sx={{ fontSize: 40, color: 'warning.main' }} />\n                </Badge>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Out of Stock\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"error.main\">\n                    {outOfStockProducts.length}\n                  </Typography>\n                </Box>\n                <TrendingDownIcon sx={{ fontSize: 40, color: 'error.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Value\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {formatCurrency(totalValue)}\n                  </Typography>\n                </Box>\n                <MoneyIcon sx={{ fontSize: 40, color: 'success.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Alerts */}\n      {lowStockProducts.length > 0 && (\n        <Alert severity=\"warning\" sx={{ mb: 2 }}>\n          <strong>{lowStockProducts.length} products</strong> are running low on stock and need restocking.\n        </Alert>\n      )}\n      \n      {outOfStockProducts.length > 0 && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          <strong>{outOfStockProducts.length} products</strong> are out of stock.\n        </Alert>\n      )}\n      \n      {expiringProducts.length > 0 && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          <strong>{expiringProducts.length} products</strong> are expiring within 30 days.\n        </Alert>\n      )}\n\n      {/* Tabs */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>\n          <Tab label=\"All Products\" />\n          <Tab label=\"Low Stock\" />\n          <Tab label=\"Expiring Soon\" />\n          <Tab label=\"Stock Movements\" />\n        </Tabs>\n      </Paper>\n\n      {/* Search and Filters */}\n      <TabPanel value={currentTab} index={0}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={categoryFilter}\n                  onChange={(e) => setCategoryFilter(e.target.value)}\n                  label=\"Category\"\n                >\n                  <MenuItem value=\"all\">All Categories</MenuItem>\n                  {categories.map((category) => (\n                    <MenuItem key={category} value={category}>\n                      {category}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Stock Status</InputLabel>\n                <Select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  label=\"Stock Status\"\n                >\n                  <MenuItem value=\"all\">All Status</MenuItem>\n                  <MenuItem value=\"normal\">In Stock</MenuItem>\n                  <MenuItem value=\"low\">Low Stock</MenuItem>\n                  <MenuItem value=\"out\">Out of Stock</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<FilterIcon />}\n                onClick={() => {\n                  setSearchTerm('');\n                  setCategoryFilter('all');\n                  setStatusFilter('all');\n                }}\n              >\n                Clear\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n      </TabPanel>\n\n      {/* Products Table */}\n      <TabPanel value={currentTab} index={0}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>SKU</TableCell>\n                <TableCell>Category</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Stock Level</TableCell>\n                <TableCell>Unit Price</TableCell>\n                <TableCell>Total Value</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {filteredProducts.map((product) => {\n                const stockStatus = getStockStatus(product);\n                const stockPercentage = getStockPercentage(product);\n\n                return (\n                  <TableRow key={product.id}>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {product.name}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {product.brand}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{product.sku}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={product.category}\n                        size=\"small\"\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {product.currentStock} / {product.maxStockLevel}\n                      </Typography>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={stockPercentage}\n                        sx={{ mt: 1, height: 6, borderRadius: 3 }}\n                        color={stockStatus.color}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        Min: {product.minStockLevel}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        Max: {product.maxStockLevel}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{formatCurrency(product.unitPrice)}</TableCell>\n                    <TableCell>\n                      {formatCurrency(product.currentStock * product.unitPrice)}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={stockStatus.status}\n                        color={stockStatus.color}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', gap: 1 }}>\n                        <Tooltip title=\"Edit Product\">\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleEditProduct(product)}\n                          >\n                            <EditIcon />\n                          </IconButton>\n                        </Tooltip>\n                        <Tooltip title=\"Delete Product\">\n                          <IconButton\n                            size=\"small\"\n                            color=\"error\"\n                            onClick={() => handleDeleteProduct(product)}\n                          >\n                            <DeleteIcon />\n                          </IconButton>\n                        </Tooltip>\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Low Stock Tab */}\n      <TabPanel value={currentTab} index={1}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Min Level</TableCell>\n                <TableCell>Shortage</TableCell>\n                <TableCell>Last Restocked</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {lowStockProducts.map((product) => (\n                <TableRow key={product.id}>\n                  <TableCell>\n                    <Box>\n                      <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                        {product.name}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        {product.brand} - {product.sku}\n                      </Typography>\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={product.currentStock}\n                      color=\"warning\"\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{product.minStockLevel}</TableCell>\n                  <TableCell>\n                    <Typography color=\"error.main\" fontWeight=\"bold\">\n                      {product.minStockLevel - product.currentStock}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{product.lastRestocked}</TableCell>\n                  <TableCell>\n                    <Button\n                      variant=\"contained\"\n                      size=\"small\"\n                      color=\"warning\"\n                    >\n                      Restock\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Expiring Products Tab */}\n      <TabPanel value={currentTab} index={2}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Expiry Date</TableCell>\n                <TableCell>Days Until Expiry</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {expiringProducts.map((product) => {\n                const daysUntilExpiry = Math.ceil(\n                  (new Date(product.expiryDate) - new Date()) / (1000 * 60 * 60 * 24)\n                );\n\n                return (\n                  <TableRow key={product.id}>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {product.name}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {product.brand} - {product.sku}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{product.currentStock}</TableCell>\n                    <TableCell>{product.expiryDate}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={`${daysUntilExpiry} days`}\n                        color={daysUntilExpiry <= 7 ? 'error' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        color=\"primary\"\n                      >\n                        Mark as Used\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Stock Movements Tab */}\n      <TabPanel value={currentTab} index={3}>\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>\n          Recent Stock Movements\n        </Typography>\n        <Paper sx={{ p: 2 }}>\n          <Typography color=\"textSecondary\">\n            Stock movements functionality will be implemented in the next phase.\n          </Typography>\n        </Paper>\n      </TabPanel>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Confirm Delete</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{productToDelete?.name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={confirmDelete} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Product Form Dialog */}\n      <ProductForm\n        open={productFormOpen}\n        onClose={handleCloseProductForm}\n        product={editingProduct}\n        mode={formMode}\n      />\n    </Box>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,WAAW,IAAIC,SAAS,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMjB,SAAS,GAAGA,CAAA,KAAM;EAAAkB,EAAA;EACtB,MAAM;IACJC,QAAQ;IACRC,cAAc;IACdC,mBAAmB;IACnBC,qBAAqB;IACrBC,mBAAmB;IACnBC,sBAAsB;IACtBC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC;EACF,CAAC,GAAGd,YAAY,CAAC,CAAC;EAElB,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuF,QAAQ,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2F,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAChF,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,iBAAiB,CAAC;;EAErE;EACA,MAAMiG,gBAAgB,GAAGpC,QAAQ,CAACqC,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CAAC,IAC9DH,OAAO,CAACK,GAAG,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CAAC,IAC5DH,OAAO,CAACM,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,UAAU,CAAC6B,WAAW,CAAC,CAAC,CAAC;IACnF,MAAMI,eAAe,GAAG/B,cAAc,KAAK,KAAK,IAAIwB,OAAO,CAACQ,QAAQ,KAAKhC,cAAc;IACvF,MAAMiC,aAAa,GAAG/B,YAAY,KAAK,KAAK,IACtBA,YAAY,KAAK,KAAK,IAAIsB,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACW,aAAc,IACxEjC,YAAY,KAAK,KAAK,IAAIsB,OAAO,CAACU,YAAY,KAAK,CAAE,IACrDhC,YAAY,KAAK,QAAQ,IAAIsB,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACW,aAAc;IAEhG,OAAOV,aAAa,IAAIM,eAAe,IAAIE,aAAa;EAC1D,CAAC,CAAC;;EAEF;EACA,MAAMG,gBAAgB,GAAGhD,mBAAmB,CAAC,CAAC;EAC9C,MAAMiD,kBAAkB,GAAGhD,qBAAqB,CAAC,CAAC;EAClD,MAAMiD,gBAAgB,GAAGhD,mBAAmB,CAAC,CAAC;EAC9C,MAAMiD,UAAU,GAAGhD,sBAAsB,CAAC,CAAC;EAC3C,MAAMiD,UAAU,GAAGhD,aAAa,CAAC,CAAC;EAElC,MAAMiD,cAAc,GAAIjB,OAAO,IAAK;IAClC,IAAIA,OAAO,CAACU,YAAY,KAAK,CAAC,EAAE,OAAO;MAAEQ,MAAM,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACjF,IAAInB,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACW,aAAa,EAAE,OAAO;MAAEO,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAC;IACnG,OAAO;MAAED,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAC;EACjD,CAAC;EAED,MAAMC,kBAAkB,GAAIpB,OAAO,IAAK;IACtC,OAAOqB,IAAI,CAACC,GAAG,CAAEtB,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACuB,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;EAC5E,CAAC;EAED,MAAMC,mBAAmB,GAAIxB,OAAO,IAAK;IACvCjB,kBAAkB,CAACiB,OAAO,CAAC;IAC3BnB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM4C,iBAAiB,GAAIzB,OAAO,IAAK;IACrCb,iBAAiB,CAACa,OAAO,CAAC;IAC1BX,WAAW,CAAC,MAAM,CAAC;IACnBJ,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvC,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC,KAAK,CAAC;IAClBJ,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0C,sBAAsB,GAAGA,CAAA,KAAM;IACnC1C,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI9C,eAAe,EAAE;MACnBb,aAAa,CAACa,eAAe,CAAC+C,EAAE,CAAC;MACjChD,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM+C,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1ChF,OAAA;IAAKiF,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIhF,OAAA,CAAC1D,GAAG;MAAC4I,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,oBACEvF,OAAA,CAAC1D,GAAG;IAAC4I,EAAE,EAAE;MAAEM,CAAC,EAAE;IAAE,CAAE;IAAAV,QAAA,gBAEhB9E,OAAA,CAAC1D,GAAG;MAAC4I,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAd,QAAA,gBACzF9E,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACZ,EAAE,EAAE;UAAEa,UAAU,EAAE;QAAO,CAAE;QAAAjB,QAAA,EAAC;MAEpE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAAC1D,GAAG;QAAC4I,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEM,GAAG,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACnC9E,OAAA,CAAC9C,MAAM;UACL2I,OAAO,EAAC,UAAU;UAClBI,SAAS,eAAEjG,OAAA,CAACN,WAAW;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAvB,QAAA,EACzC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvF,OAAA,CAAC9C,MAAM;UACL2I,OAAO,EAAC,UAAU;UAClBI,SAAS,eAAEjG,OAAA,CAACJ,UAAU;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,EAC3B;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvF,OAAA,CAAC9C,MAAM;UACL2I,OAAO,EAAC,WAAW;UACnBI,SAAS,eAAEjG,OAAA,CAACxB,OAAO;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBL,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAe,CAAE;UAChCJ,OAAO,EAAEhC,gBAAiB;UAAAY,QAAA,EAC3B;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvF,OAAA,CAACvD,IAAI;MAAC8J,SAAS;MAACC,OAAO,EAAE,CAAE;MAACtB,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACxC9E,OAAA,CAACvD,IAAI;QAACgK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B9E,OAAA,CAACtD,IAAI;UAAAoI,QAAA,eACH9E,OAAA,CAACrD,WAAW;YAAAmI,QAAA,eACV9E,OAAA,CAAC1D,GAAG;cAAC4I,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClF9E,OAAA,CAAC1D,GAAG;gBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;kBAACmH,KAAK,EAAC,eAAe;kBAACkD,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAAClC,KAAK,EAAC,cAAc;kBAAAmB,QAAA,EAC1C5E,QAAQ,CAAC4G;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAAChB,aAAa;gBAACkG,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAEpD,KAAK,EAAE;gBAAe;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPvF,OAAA,CAACvD,IAAI;QAACgK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B9E,OAAA,CAACtD,IAAI;UAAAoI,QAAA,eACH9E,OAAA,CAACrD,WAAW;YAAAmI,QAAA,eACV9E,OAAA,CAAC1D,GAAG;cAAC4I,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClF9E,OAAA,CAAC1D,GAAG;gBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;kBAACmH,KAAK,EAAC,eAAe;kBAACkD,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAAClC,KAAK,EAAC,cAAc;kBAAAmB,QAAA,EAC1C1B,gBAAgB,CAAC0D;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACnC,KAAK;gBAACmJ,YAAY,EAAE5D,gBAAgB,CAAC0D,MAAO;gBAACnD,KAAK,EAAC,SAAS;gBAAAmB,QAAA,eAC3D9E,OAAA,CAAClB,WAAW;kBAACoG,EAAE,EAAE;oBAAE6B,QAAQ,EAAE,EAAE;oBAAEpD,KAAK,EAAE;kBAAe;gBAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPvF,OAAA,CAACvD,IAAI;QAACgK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B9E,OAAA,CAACtD,IAAI;UAAAoI,QAAA,eACH9E,OAAA,CAACrD,WAAW;YAAAmI,QAAA,eACV9E,OAAA,CAAC1D,GAAG;cAAC4I,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClF9E,OAAA,CAAC1D,GAAG;gBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;kBAACmH,KAAK,EAAC,eAAe;kBAACkD,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAAClC,KAAK,EAAC,YAAY;kBAAAmB,QAAA,EACxCzB,kBAAkB,CAACyD;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACd,gBAAgB;gBAACgG,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAEpD,KAAK,EAAE;gBAAa;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPvF,OAAA,CAACvD,IAAI;QAACgK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9B9E,OAAA,CAACtD,IAAI;UAAAoI,QAAA,eACH9E,OAAA,CAACrD,WAAW;YAAAmI,QAAA,eACV9E,OAAA,CAAC1D,GAAG;cAAC4I,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClF9E,OAAA,CAAC1D,GAAG;gBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;kBAACmH,KAAK,EAAC,eAAe;kBAACkD,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAAClC,KAAK,EAAC,cAAc;kBAAAmB,QAAA,EAC1CR,cAAc,CAACf,UAAU;gBAAC;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACZ,SAAS;gBAAC8F,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAEpD,KAAK,EAAE;gBAAe;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNnC,gBAAgB,CAAC0D,MAAM,GAAG,CAAC,iBAC1B9G,OAAA,CAACpC,KAAK;MAACqJ,QAAQ,EAAC,SAAS;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACtC9E,OAAA;QAAA8E,QAAA,GAAS1B,gBAAgB,CAAC0D,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,kDACrD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEAlC,kBAAkB,CAACyD,MAAM,GAAG,CAAC,iBAC5B9G,OAAA,CAACpC,KAAK;MAACqJ,QAAQ,EAAC,OAAO;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACpC9E,OAAA;QAAA8E,QAAA,GAASzB,kBAAkB,CAACyD,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,sBACvD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEAjC,gBAAgB,CAACwD,MAAM,GAAG,CAAC,iBAC1B9G,OAAA,CAACpC,KAAK;MAACqJ,QAAQ,EAAC,MAAM;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACnC9E,OAAA;QAAA8E,QAAA,GAASxB,gBAAgB,CAACwD,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,iCACrD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAGDvF,OAAA,CAACzD,KAAK;MAAC2I,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACnB9E,OAAA,CAAClC,IAAI;QAACiH,KAAK,EAAEnE,UAAW;QAACsG,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAKvG,aAAa,CAACuG,QAAQ,CAAE;QAAAtC,QAAA,gBAC1E9E,OAAA,CAACjC,GAAG;UAACsJ,KAAK,EAAC;QAAc;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BvF,OAAA,CAACjC,GAAG;UAACsJ,KAAK,EAAC;QAAW;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzBvF,OAAA,CAACjC,GAAG;UAACsJ,KAAK,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BvF,OAAA,CAACjC,GAAG;UAACsJ,KAAK,EAAC;QAAiB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRvF,OAAA,CAAC6E,QAAQ;MAACE,KAAK,EAAEnE,UAAW;MAACoE,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpC9E,OAAA,CAACzD,KAAK;QAAC2I,EAAE,EAAE;UAAEM,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,eACzB9E,OAAA,CAACvD,IAAI;UAAC8J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACZ,UAAU,EAAC,QAAQ;UAAAd,QAAA,gBAC7C9E,OAAA,CAACvD,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB9E,OAAA,CAACpD,SAAS;cACR0K,SAAS;cACTC,WAAW,EAAC,oBAAoB;cAChCxC,KAAK,EAAEjE,UAAW;cAClBoG,QAAQ,EAAGC,CAAC,IAAKpG,aAAa,CAACoG,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;cAC/C0C,UAAU,EAAE;gBACVC,cAAc,eACZ1H,OAAA,CAACnD,cAAc;kBAAC8K,QAAQ,EAAC,OAAO;kBAAA7C,QAAA,eAC9B9E,OAAA,CAAC1B,UAAU;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAEpB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPvF,OAAA,CAACvD,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB9E,OAAA,CAAClD,WAAW;cAACwK,SAAS;cAAAxC,QAAA,gBACpB9E,OAAA,CAACjD,UAAU;gBAAA+H,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCvF,OAAA,CAAChD,MAAM;gBACL+H,KAAK,EAAE/D,cAAe;gBACtBkG,QAAQ,EAAGC,CAAC,IAAKlG,iBAAiB,CAACkG,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;gBACnDsC,KAAK,EAAC,UAAU;gBAAAvC,QAAA,gBAEhB9E,OAAA,CAAC/C,QAAQ;kBAAC8H,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC9C/B,UAAU,CAACoE,GAAG,CAAE5E,QAAQ,iBACvBhD,OAAA,CAAC/C,QAAQ;kBAAgB8H,KAAK,EAAE/B,QAAS;kBAAA8B,QAAA,EACtC9B;gBAAQ,GADIA,QAAQ;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPvF,OAAA,CAACvD,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB9E,OAAA,CAAClD,WAAW;cAACwK,SAAS;cAAAxC,QAAA,gBACpB9E,OAAA,CAACjD,UAAU;gBAAA+H,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCvF,OAAA,CAAChD,MAAM;gBACL+H,KAAK,EAAE7D,YAAa;gBACpBgG,QAAQ,EAAGC,CAAC,IAAKhG,eAAe,CAACgG,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;gBACjDsC,KAAK,EAAC,cAAc;gBAAAvC,QAAA,gBAEpB9E,OAAA,CAAC/C,QAAQ;kBAAC8H,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC3CvF,OAAA,CAAC/C,QAAQ;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC5CvF,OAAA,CAAC/C,QAAQ;kBAAC8H,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1CvF,OAAA,CAAC/C,QAAQ;kBAAC8H,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPvF,OAAA,CAACvD,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB9E,OAAA,CAAC9C,MAAM;cACLoK,SAAS;cACTzB,OAAO,EAAC,UAAU;cAClBI,SAAS,eAAEjG,OAAA,CAACR,UAAU;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BW,OAAO,EAAEA,CAAA,KAAM;gBACbnF,aAAa,CAAC,EAAE,CAAC;gBACjBE,iBAAiB,CAAC,KAAK,CAAC;gBACxBE,eAAe,CAAC,KAAK,CAAC;cACxB,CAAE;cAAA2D,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXvF,OAAA,CAAC6E,QAAQ;MAACE,KAAK,EAAEnE,UAAW;MAACoE,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpC9E,OAAA,CAACzC,cAAc;QAACuI,SAAS,EAAEvJ,KAAM;QAAAuI,QAAA,eAC/B9E,OAAA,CAAC5C,KAAK;UAAA0H,QAAA,gBACJ9E,OAAA,CAACxC,SAAS;YAAAsH,QAAA,eACR9E,OAAA,CAACvC,QAAQ;cAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvF,OAAA,CAAC3C,SAAS;YAAAyH,QAAA,EACPxC,gBAAgB,CAACsF,GAAG,CAAEpF,OAAO,IAAK;cACjC,MAAMqF,WAAW,GAAGpE,cAAc,CAACjB,OAAO,CAAC;cAC3C,MAAMsF,eAAe,GAAGlE,kBAAkB,CAACpB,OAAO,CAAC;cAEnD,oBACExC,OAAA,CAACvC,QAAQ;gBAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC1D,GAAG;oBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAjB,QAAA,EAC9CtC,OAAO,CAACE;oBAAI;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACbvF,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,eAAe;sBAAAmB,QAAA,EAC9CtC,OAAO,CAACM;oBAAK;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,EAAEtC,OAAO,CAACK;gBAAG;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC7C,IAAI;oBACHkK,KAAK,EAAE7E,OAAO,CAACQ,QAAS;oBACxB+E,IAAI,EAAC,OAAO;oBACZlC,OAAO,EAAC;kBAAU;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,gBACR9E,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAAAf,QAAA,GACxBtC,OAAO,CAACU,YAAY,EAAC,KAAG,EAACV,OAAO,CAACuB,aAAa;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACbvF,OAAA,CAAC5B,cAAc;oBACbyH,OAAO,EAAC,aAAa;oBACrBd,KAAK,EAAE+C,eAAgB;oBACvB5C,EAAE,EAAE;sBAAE8C,EAAE,EAAE,CAAC;sBAAEC,MAAM,EAAE,CAAC;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAC1CvE,KAAK,EAAEkE,WAAW,CAAClE;kBAAM;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,gBACR9E,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAAClC,KAAK,EAAC,eAAe;oBAAAmB,QAAA,GAAC,OAC3C,EAACtC,OAAO,CAACW,aAAa;kBAAA;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACbvF,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAAClC,KAAK,EAAC,eAAe;oBAAAmB,QAAA,GAAC,OAC3C,EAACtC,OAAO,CAACuB,aAAa;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,EAAER,cAAc,CAAC9B,OAAO,CAAC2F,SAAS;gBAAC;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1DvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,EACPR,cAAc,CAAC9B,OAAO,CAACU,YAAY,GAAGV,OAAO,CAAC2F,SAAS;gBAAC;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC7C,IAAI;oBACHkK,KAAK,EAAEQ,WAAW,CAACnE,MAAO;oBAC1BC,KAAK,EAAEkE,WAAW,CAAClE,KAAM;oBACzBoE,IAAI,EAAC;kBAAO;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC1D,GAAG;oBAAC4I,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEM,GAAG,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBACnC9E,OAAA,CAACrC,OAAO;sBAACyK,KAAK,EAAC,cAAc;sBAAAtD,QAAA,eAC3B9E,OAAA,CAACtC,UAAU;wBACTqK,IAAI,EAAC,OAAO;wBACZpE,KAAK,EAAC,SAAS;wBACfuC,OAAO,EAAEA,CAAA,KAAMjC,iBAAiB,CAACzB,OAAO,CAAE;wBAAAsC,QAAA,eAE1C9E,OAAA,CAACtB,QAAQ;0BAAA0G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACVvF,OAAA,CAACrC,OAAO;sBAACyK,KAAK,EAAC,gBAAgB;sBAAAtD,QAAA,eAC7B9E,OAAA,CAACtC,UAAU;wBACTqK,IAAI,EAAC,OAAO;wBACZpE,KAAK,EAAC,OAAO;wBACbuC,OAAO,EAAEA,CAAA,KAAMlC,mBAAmB,CAACxB,OAAO,CAAE;wBAAAsC,QAAA,eAE5C9E,OAAA,CAACpB,UAAU;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAtEC/C,OAAO,CAAC6B,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuEf,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGXvF,OAAA,CAAC6E,QAAQ;MAACE,KAAK,EAAEnE,UAAW;MAACoE,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpC9E,OAAA,CAACzC,cAAc;QAACuI,SAAS,EAAEvJ,KAAM;QAAAuI,QAAA,eAC/B9E,OAAA,CAAC5C,KAAK;UAAA0H,QAAA,gBACJ9E,OAAA,CAACxC,SAAS;YAAAsH,QAAA,eACR9E,OAAA,CAACvC,QAAQ;cAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvF,OAAA,CAAC3C,SAAS;YAAAyH,QAAA,EACP1B,gBAAgB,CAACwE,GAAG,CAAEpF,OAAO,iBAC5BxC,OAAA,CAACvC,QAAQ;cAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,eACR9E,OAAA,CAAC1D,GAAG;kBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,WAAW;oBAACE,UAAU,EAAC,MAAM;oBAAAjB,QAAA,EAC9CtC,OAAO,CAACE;kBAAI;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACbvF,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAAClC,KAAK,EAAC,eAAe;oBAAAmB,QAAA,GAC9CtC,OAAO,CAACM,KAAK,EAAC,KAAG,EAACN,OAAO,CAACK,GAAG;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,eACR9E,OAAA,CAAC7C,IAAI;kBACHkK,KAAK,EAAE7E,OAAO,CAACU,YAAa;kBAC5BS,KAAK,EAAC,SAAS;kBACfoE,IAAI,EAAC;gBAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAEtC,OAAO,CAACW;cAAa;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,eACR9E,OAAA,CAACxD,UAAU;kBAACmH,KAAK,EAAC,YAAY;kBAACoC,UAAU,EAAC,MAAM;kBAAAjB,QAAA,EAC7CtC,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACU;gBAAY;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAEtC,OAAO,CAAC6F;cAAa;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,eACR9E,OAAA,CAAC9C,MAAM;kBACL2I,OAAO,EAAC,WAAW;kBACnBkC,IAAI,EAAC,OAAO;kBACZpE,KAAK,EAAC,SAAS;kBAAAmB,QAAA,EAChB;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAjCC/C,OAAO,CAAC6B,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGXvF,OAAA,CAAC6E,QAAQ;MAACE,KAAK,EAAEnE,UAAW;MAACoE,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpC9E,OAAA,CAACzC,cAAc;QAACuI,SAAS,EAAEvJ,KAAM;QAAAuI,QAAA,eAC/B9E,OAAA,CAAC5C,KAAK;UAAA0H,QAAA,gBACJ9E,OAAA,CAACxC,SAAS;YAAAsH,QAAA,eACR9E,OAAA,CAACvC,QAAQ;cAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxCvF,OAAA,CAAC1C,SAAS;gBAAAwH,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvF,OAAA,CAAC3C,SAAS;YAAAyH,QAAA,EACPxB,gBAAgB,CAACsE,GAAG,CAAEpF,OAAO,IAAK;cACjC,MAAM8F,eAAe,GAAGzE,IAAI,CAAC0E,IAAI,CAC/B,CAAC,IAAIC,IAAI,CAAChG,OAAO,CAACiG,UAAU,CAAC,GAAG,IAAID,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACpE,CAAC;cAED,oBACExI,OAAA,CAACvC,QAAQ;gBAAAqH,QAAA,gBACP9E,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC1D,GAAG;oBAAAwI,QAAA,gBACF9E,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAjB,QAAA,EAC9CtC,OAAO,CAACE;oBAAI;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACbvF,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAAClC,KAAK,EAAC,eAAe;sBAAAmB,QAAA,GAC9CtC,OAAO,CAACM,KAAK,EAAC,KAAG,EAACN,OAAO,CAACK,GAAG;oBAAA;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,EAAEtC,OAAO,CAACU;gBAAY;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7CvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,EAAEtC,OAAO,CAACiG;gBAAU;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3CvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC7C,IAAI;oBACHkK,KAAK,EAAE,GAAGiB,eAAe,OAAQ;oBACjC3E,KAAK,EAAE2E,eAAe,IAAI,CAAC,GAAG,OAAO,GAAG,SAAU;oBAClDP,IAAI,EAAC;kBAAO;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvF,OAAA,CAAC1C,SAAS;kBAAAwH,QAAA,eACR9E,OAAA,CAAC9C,MAAM;oBACL2I,OAAO,EAAC,UAAU;oBAClBkC,IAAI,EAAC,OAAO;oBACZpE,KAAK,EAAC,SAAS;oBAAAmB,QAAA,EAChB;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5BC/C,OAAO,CAAC6B,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6Bf,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGXvF,OAAA,CAAC6E,QAAQ;MAACE,KAAK,EAAEnE,UAAW;MAACoE,KAAK,EAAE,CAAE;MAAAF,QAAA,gBACpC9E,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EAAC;MAExC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAACzD,KAAK;QAAC2I,EAAE,EAAE;UAAEM,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,eAClB9E,OAAA,CAACxD,UAAU;UAACmH,KAAK,EAAC,eAAe;UAAAmB,QAAA,EAAC;QAElC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXvF,OAAA,CAAChC,MAAM;MAAC0K,IAAI,EAAEtH,gBAAiB;MAACuH,OAAO,EAAEA,CAAA,KAAMtH,mBAAmB,CAAC,KAAK,CAAE;MAAAyD,QAAA,gBACxE9E,OAAA,CAAC/B,WAAW;QAAA6G,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzCvF,OAAA,CAAC9B,aAAa;QAAA4G,QAAA,eACZ9E,OAAA,CAACxD,UAAU;UAAAsI,QAAA,GAAC,oCACuB,EAACxD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,IAAI,EAAC,mCAC1D;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBvF,OAAA,CAAC7B,aAAa;QAAA2G,QAAA,gBACZ9E,OAAA,CAAC9C,MAAM;UAACgJ,OAAO,EAAEA,CAAA,KAAM7E,mBAAmB,CAAC,KAAK,CAAE;UAAAyD,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEvF,OAAA,CAAC9C,MAAM;UAACgJ,OAAO,EAAE9B,aAAc;UAACT,KAAK,EAAC,OAAO;UAACkC,OAAO,EAAC,WAAW;UAAAf,QAAA,EAAC;QAElE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvF,OAAA,CAACF,WAAW;MACV4I,IAAI,EAAElH,eAAgB;MACtBmH,OAAO,EAAExE,sBAAuB;MAChC3B,OAAO,EAAEd,cAAe;MACxBkH,IAAI,EAAEhH;IAAS;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtF,EAAA,CAljBIlB,SAAS;EAAA,QAYTc,YAAY;AAAA;AAAAgJ,EAAA,GAZZ9J,SAAS;AAojBf,eAAeA,SAAS;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}