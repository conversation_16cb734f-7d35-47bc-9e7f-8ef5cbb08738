{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, Typography, Grid, Card, CardContent, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, Button, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tooltip, Alert, Badge, Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions, LinearProgress } from '@mui/material';\nimport { Search as SearchIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Warning as WarningIcon, Inventory as InventoryIcon, TrendingDown as TrendingDownIcon, AttachMoney as MoneyIcon, FilterList as FilterIcon, Refresh as RefreshIcon, GetApp as ExportIcon } from '@mui/icons-material';\nimport { useInventory } from '../contexts/InventoryContext';\nimport ProductForm from './ProductForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const {\n    products,\n    stockMovements,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getExpiringProducts,\n    getTotalInventoryValue,\n    getCategories,\n    deleteProduct,\n    addStockMovement,\n    getProductById\n  } = useInventory();\n  const [currentTab, setCurrentTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [expiryFilter, setExpiryFilter] = useState('all');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [productFormOpen, setProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formMode, setFormMode] = useState('add');\n  const [restockDialogOpen, setRestockDialogOpen] = useState(false);\n  const [selectedProductForRestock, setSelectedProductForRestock] = useState(null);\n  const [restockQuantity, setRestockQuantity] = useState('');\n  const [restockReason, setRestockReason] = useState('Regular restock');\n\n  // Get filtered products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.sku.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;\n    const matchesStatus = statusFilter === 'all' || statusFilter === 'low' && product.currentStock <= product.minStockLevel || statusFilter === 'out' && product.currentStock === 0 || statusFilter === 'normal' && product.currentStock > product.minStockLevel;\n    const expiryStatus = getExpiryStatus(product);\n    const matchesExpiry = expiryFilter === 'all' || expiryFilter === 'expiring' && expiryStatus && (expiryStatus.color === 'warning' || expiryStatus.color === 'error') || expiryFilter === 'expired' && expiryStatus && expiryStatus.color === 'error' && expiryStatus.days < 0 || expiryFilter === 'good' && (!expiryStatus || expiryStatus.color === 'success');\n    return matchesSearch && matchesCategory && matchesStatus && matchesExpiry;\n  });\n\n  // Calculate statistics\n  const lowStockProducts = getLowStockProducts();\n  const outOfStockProducts = getOutOfStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const totalValue = getTotalInventoryValue();\n  const categories = getCategories();\n  const getStockStatus = product => {\n    if (product.currentStock === 0) return {\n      status: 'Out of Stock',\n      color: 'error'\n    };\n    if (product.currentStock <= product.minStockLevel) return {\n      status: 'Low Stock',\n      color: 'warning'\n    };\n    return {\n      status: 'In Stock',\n      color: 'success'\n    };\n  };\n  const getStockPercentage = product => {\n    return Math.min(product.currentStock / product.maxStockLevel * 100, 100);\n  };\n  const getExpiryStatus = product => {\n    if (!product.expiryDate) return null;\n    const today = new Date();\n    const expiryDate = new Date(product.expiryDate);\n    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));\n    if (daysUntilExpiry < 0) {\n      return {\n        status: 'Expired',\n        color: 'error',\n        days: Math.abs(daysUntilExpiry)\n      };\n    } else if (daysUntilExpiry <= 7) {\n      return {\n        status: 'Expires Soon',\n        color: 'error',\n        days: daysUntilExpiry\n      };\n    } else if (daysUntilExpiry <= 30) {\n      return {\n        status: 'Expiring',\n        color: 'warning',\n        days: daysUntilExpiry\n      };\n    }\n    return {\n      status: 'Good',\n      color: 'success',\n      days: daysUntilExpiry\n    };\n  };\n  const handleDeleteProduct = product => {\n    setProductToDelete(product);\n    setDeleteDialogOpen(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setFormMode('edit');\n    setProductFormOpen(true);\n  };\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setFormMode('add');\n    setProductFormOpen(true);\n  };\n  const handleCloseProductForm = () => {\n    setProductFormOpen(false);\n    setEditingProduct(null);\n  };\n  const handleRestockClick = product => {\n    setSelectedProductForRestock(product);\n    setRestockQuantity(Math.max(product.maxStockLevel - product.currentStock, 1).toString());\n    setRestockDialogOpen(true);\n  };\n  const handleRestockSubmit = () => {\n    if (selectedProductForRestock && restockQuantity) {\n      const quantity = parseInt(restockQuantity);\n\n      // Add stock movement\n      addStockMovement({\n        productId: selectedProductForRestock.id,\n        type: 'restock',\n        quantity: quantity,\n        reason: restockReason,\n        performedBy: 'Admin',\n        // In a real app, this would be the current user\n        notes: `Restocked ${quantity} units`\n      });\n      setRestockDialogOpen(false);\n      setSelectedProductForRestock(null);\n      setRestockQuantity('');\n      setRestockReason('Regular restock');\n    }\n  };\n  const handleMarkAsUsed = product => {\n    // Mark 1 unit as used\n    addStockMovement({\n      productId: product.id,\n      type: 'usage',\n      quantity: -1,\n      reason: 'Marked as used',\n      performedBy: 'Admin',\n      notes: 'Marked as used from expiring products'\n    });\n  };\n  const confirmDelete = () => {\n    if (productToDelete) {\n      deleteProduct(productToDelete.id);\n      setDeleteDialogOpen(false);\n      setProductToDelete(null);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"Inventory Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 24\n          }, this),\n          onClick: () => window.location.reload(),\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 24\n          }, this),\n          children: \"Export\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 24\n          }, this),\n          sx: {\n            bgcolor: 'primary.main'\n          },\n          onClick: handleAddProduct,\n          children: \"Add Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: products.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(InventoryIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Low Stock Items\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"warning.main\",\n                  children: lowStockProducts.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: lowStockProducts.length,\n                color: \"warning\",\n                children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                  sx: {\n                    fontSize: 40,\n                    color: 'warning.main'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Out of Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"error.main\",\n                  children: outOfStockProducts.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TrendingDownIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'error.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Total Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: formatCurrency(totalValue)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MoneyIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'success.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), lowStockProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [lowStockProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), \" are running low on stock and need restocking.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this), outOfStockProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [outOfStockProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this), \" are out of stock.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this), expiringProducts.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: [expiringProducts.length, \" products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), \" are expiring within 30 days.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: currentTab,\n        onChange: (e, newValue) => setCurrentTab(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Low Stock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Expiring Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Stock Movements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search products...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: categoryFilter,\n                onChange: e => setCategoryFilter(e.target.value),\n                label: \"Category\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stock Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: statusFilter,\n                onChange: e => setStatusFilter(e.target.value),\n                label: \"Stock Status\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"normal\",\n                  children: \"In Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"low\",\n                  children: \"Low Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"out\",\n                  children: \"Out of Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 28\n              }, this),\n              onClick: () => {\n                setSearchTerm('');\n                setCategoryFilter('all');\n                setStatusFilter('all');\n              },\n              children: \"Clear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"SKU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stock Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Unit Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Total Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Expiry Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredProducts.map(product => {\n              const stockStatus = getStockStatus(product);\n              const stockPercentage = getStockPercentage(product);\n              const expiryStatus = getExpiryStatus(product);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  backgroundColor: expiryStatus && (expiryStatus.color === 'error' || expiryStatus.color === 'warning') ? `${expiryStatus.color === 'error' ? 'error' : 'warning'}.50` : 'inherit',\n                  '&:hover': {\n                    backgroundColor: expiryStatus && (expiryStatus.color === 'error' || expiryStatus.color === 'warning') ? `${expiryStatus.color === 'error' ? 'error' : 'warning'}.100` : 'action.hover'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: product.brand\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.sku\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: product.category,\n                    size: \"small\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [product.currentStock, \" / \", product.maxStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                    variant: \"determinate\",\n                    value: stockPercentage,\n                    sx: {\n                      mt: 1,\n                      height: 6,\n                      borderRadius: 3\n                    },\n                    color: stockStatus.color\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [\"Min: \", product.minStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [\"Max: \", product.maxStockLevel]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatCurrency(product.unitPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: formatCurrency(product.currentStock * product.unitPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: expiryStatus ? /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Chip, {\n                      label: expiryStatus.status,\n                      color: expiryStatus.color,\n                      size: \"small\",\n                      sx: {\n                        mb: 0.5\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      color: \"textSecondary\",\n                      children: expiryStatus.days > 0 ? `${expiryStatus.days} days` : `${expiryStatus.days} days ago`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 27\n                    }, this), product.expiryDate && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      color: \"textSecondary\",\n                      children: product.expiryDate\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: \"No expiry date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: stockStatus.status,\n                    color: stockStatus.color,\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Edit Product\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        onClick: () => handleEditProduct(product),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 542,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Delete Product\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"error\",\n                        onClick: () => handleDeleteProduct(product),\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 551,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 546,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Min Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Shortage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Last Restocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: lowStockProducts.map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: \"bold\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [product.brand, \" - \", product.sku]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: product.currentStock,\n                  color: \"warning\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: product.minStockLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"error.main\",\n                  fontWeight: \"bold\",\n                  children: product.minStockLevel - product.currentStock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: product.lastRestocked\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  size: \"small\",\n                  color: \"warning\",\n                  onClick: () => handleRestockClick(product),\n                  children: \"Restock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 2,\n      children: /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Expiry Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Days Until Expiry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: expiringProducts.map(product => {\n              const daysUntilExpiry = Math.ceil((new Date(product.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [product.brand, \" - \", product.sku]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.currentStock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: product.expiryDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${daysUntilExpiry} days`,\n                    color: daysUntilExpiry <= 7 ? 'error' : 'warning',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    color: \"primary\",\n                    onClick: () => handleMarkAsUsed(product),\n                    children: \"Mark as Used\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Recent Stock Movements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Quantity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Reason\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Performed By\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Notes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: [stockMovements.sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 50) // Show last 50 movements\n            .map(movement => {\n              const product = getProductById(movement.productId);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: movement.date\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: (product === null || product === void 0 ? void 0 : product.name) || 'Unknown Product'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: (product === null || product === void 0 ? void 0 : product.sku) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: movement.type === 'restock' ? 'Restock' : 'Usage',\n                    color: movement.type === 'restock' ? 'success' : 'warning',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    color: movement.quantity > 0 ? 'success.main' : 'error.main',\n                    fontWeight: \"bold\",\n                    children: [movement.quantity > 0 ? '+' : '', movement.quantity]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: movement.reason\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: movement.performedBy\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: movement.notes\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 23\n                }, this)]\n              }, movement.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 21\n              }, this);\n            }), stockMovements.length === 0 && /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: 7,\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  children: \"No stock movements recorded yet.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", productToDelete === null || productToDelete === void 0 ? void 0 : productToDelete.name, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: confirmDelete,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProductForm, {\n      open: productFormOpen,\n      onClose: handleCloseProductForm,\n      product: editingProduct,\n      mode: formMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 773,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: restockDialogOpen,\n      onClose: () => setRestockDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Restock Product\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedProductForRestock && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: selectedProductForRestock.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            gutterBottom: true,\n            children: [\"Current Stock: \", selectedProductForRestock.currentStock, \" \\u2022 Min Level: \", selectedProductForRestock.minStockLevel]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Restock Quantity\",\n            type: \"number\",\n            value: restockQuantity,\n            onChange: e => setRestockQuantity(e.target.value),\n            sx: {\n              mt: 2,\n              mb: 2\n            },\n            inputProps: {\n              min: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Reason\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: restockReason,\n              onChange: e => setRestockReason(e.target.value),\n              label: \"Reason\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Regular restock\",\n                children: \"Regular restock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Emergency restock\",\n                children: \"Emergency restock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Bulk purchase\",\n                children: \"Bulk purchase\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Supplier delivery\",\n                children: \"Supplier delivery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setRestockDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 820,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleRestockSubmit,\n          variant: \"contained\",\n          children: \"Restock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"Jo7J6oHBXNiyQg/8VuQU1UAr0pQ=\", false, function () {\n  return [useInventory];\n});\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Tabs", "Tab", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "LinearProgress", "Search", "SearchIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "Inventory", "InventoryIcon", "TrendingDown", "TrendingDownIcon", "AttachMoney", "MoneyIcon", "FilterList", "FilterIcon", "Refresh", "RefreshIcon", "GetApp", "ExportIcon", "useInventory", "ProductForm", "jsxDEV", "_jsxDEV", "_s", "products", "stockMovements", "getLowStockProducts", "getOutOfStockProducts", "getExpiringProducts", "getTotalInventoryValue", "getCategories", "deleteProduct", "addStockMovement", "getProductById", "currentTab", "setCurrentTab", "searchTerm", "setSearchTerm", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusFilter", "setStatus<PERSON>ilter", "expiryFilter", "setEx<PERSON><PERSON><PERSON><PERSON>er", "deleteDialogOpen", "setDeleteDialogOpen", "productToDelete", "setProductToDelete", "productFormOpen", "setProductFormOpen", "editingProduct", "setEditingProduct", "formMode", "setFormMode", "restockDialogOpen", "setRestockDialogOpen", "selectedProductForRestock", "setSelectedProductForRestock", "restockQuantity", "setRestockQuantity", "restockReason", "setRestockReason", "filteredProducts", "filter", "product", "matchesSearch", "name", "toLowerCase", "includes", "sku", "brand", "matchesCategory", "category", "matchesStatus", "currentStock", "minStockLevel", "expiry<PERSON>tatus", "getExpiryStatus", "matchesExpiry", "color", "days", "lowStockProducts", "outOfStockProducts", "expiringProducts", "totalValue", "categories", "getStockStatus", "status", "getStockPercentage", "Math", "min", "maxStockLevel", "expiryDate", "today", "Date", "daysUntilExpiry", "ceil", "abs", "handleDeleteProduct", "handleEditProduct", "handleAddProduct", "handleCloseProductForm", "handleRestockClick", "max", "toString", "handleRestockSubmit", "quantity", "parseInt", "productId", "id", "type", "reason", "performed<PERSON><PERSON>", "notes", "handleMarkAsUsed", "confirmDelete", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "TabPanel", "children", "value", "index", "hidden", "sx", "pt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "mb", "display", "justifyContent", "alignItems", "variant", "component", "fontWeight", "gap", "startIcon", "onClick", "window", "location", "reload", "bgcolor", "container", "spacing", "item", "xs", "sm", "md", "gutterBottom", "length", "fontSize", "badgeContent", "severity", "onChange", "e", "newValue", "label", "fullWidth", "placeholder", "target", "InputProps", "startAdornment", "position", "map", "stockStatus", "stockPercentage", "backgroundColor", "size", "mt", "height", "borderRadius", "unitPrice", "title", "lastRestocked", "sort", "a", "b", "date", "slice", "movement", "colSpan", "align", "open", "onClose", "mode", "inputProps", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Inventory.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  TextField,\n  InputAdornment,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Button,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tooltip,\n  Alert,\n  Badge,\n  Tabs,\n  Tab,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  LinearProgress\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  Inventory as InventoryIcon,\n  TrendingDown as TrendingDownIcon,\n  AttachMoney as MoneyIcon,\n  FilterList as FilterIcon,\n  Refresh as RefreshIcon,\n  GetApp as ExportIcon\n} from '@mui/icons-material';\nimport { useInventory } from '../contexts/InventoryContext';\nimport ProductForm from './ProductForm';\n\nconst Inventory = () => {\n  const {\n    products,\n    stockMovements,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getExpiringProducts,\n    getTotalInventoryValue,\n    getCategories,\n    deleteProduct,\n    addStockMovement,\n    getProductById\n  } = useInventory();\n\n  const [currentTab, setCurrentTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [expiryFilter, setExpiryFilter] = useState('all');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const [productFormOpen, setProductFormOpen] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formMode, setFormMode] = useState('add');\n  const [restockDialogOpen, setRestockDialogOpen] = useState(false);\n  const [selectedProductForRestock, setSelectedProductForRestock] = useState(null);\n  const [restockQuantity, setRestockQuantity] = useState('');\n  const [restockReason, setRestockReason] = useState('Regular restock');\n\n  // Get filtered products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;\n    const matchesStatus = statusFilter === 'all' ||\n                         (statusFilter === 'low' && product.currentStock <= product.minStockLevel) ||\n                         (statusFilter === 'out' && product.currentStock === 0) ||\n                         (statusFilter === 'normal' && product.currentStock > product.minStockLevel);\n\n    const expiryStatus = getExpiryStatus(product);\n    const matchesExpiry = expiryFilter === 'all' ||\n                         (expiryFilter === 'expiring' && expiryStatus && (expiryStatus.color === 'warning' || expiryStatus.color === 'error')) ||\n                         (expiryFilter === 'expired' && expiryStatus && expiryStatus.color === 'error' && expiryStatus.days < 0) ||\n                         (expiryFilter === 'good' && (!expiryStatus || expiryStatus.color === 'success'));\n\n    return matchesSearch && matchesCategory && matchesStatus && matchesExpiry;\n  });\n\n  // Calculate statistics\n  const lowStockProducts = getLowStockProducts();\n  const outOfStockProducts = getOutOfStockProducts();\n  const expiringProducts = getExpiringProducts();\n  const totalValue = getTotalInventoryValue();\n  const categories = getCategories();\n\n  const getStockStatus = (product) => {\n    if (product.currentStock === 0) return { status: 'Out of Stock', color: 'error' };\n    if (product.currentStock <= product.minStockLevel) return { status: 'Low Stock', color: 'warning' };\n    return { status: 'In Stock', color: 'success' };\n  };\n\n  const getStockPercentage = (product) => {\n    return Math.min((product.currentStock / product.maxStockLevel) * 100, 100);\n  };\n\n  const getExpiryStatus = (product) => {\n    if (!product.expiryDate) return null;\n\n    const today = new Date();\n    const expiryDate = new Date(product.expiryDate);\n    const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));\n\n    if (daysUntilExpiry < 0) {\n      return { status: 'Expired', color: 'error', days: Math.abs(daysUntilExpiry) };\n    } else if (daysUntilExpiry <= 7) {\n      return { status: 'Expires Soon', color: 'error', days: daysUntilExpiry };\n    } else if (daysUntilExpiry <= 30) {\n      return { status: 'Expiring', color: 'warning', days: daysUntilExpiry };\n    }\n\n    return { status: 'Good', color: 'success', days: daysUntilExpiry };\n  };\n\n  const handleDeleteProduct = (product) => {\n    setProductToDelete(product);\n    setDeleteDialogOpen(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setFormMode('edit');\n    setProductFormOpen(true);\n  };\n\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setFormMode('add');\n    setProductFormOpen(true);\n  };\n\n  const handleCloseProductForm = () => {\n    setProductFormOpen(false);\n    setEditingProduct(null);\n  };\n\n  const handleRestockClick = (product) => {\n    setSelectedProductForRestock(product);\n    setRestockQuantity(Math.max(product.maxStockLevel - product.currentStock, 1).toString());\n    setRestockDialogOpen(true);\n  };\n\n  const handleRestockSubmit = () => {\n    if (selectedProductForRestock && restockQuantity) {\n      const quantity = parseInt(restockQuantity);\n\n      // Add stock movement\n      addStockMovement({\n        productId: selectedProductForRestock.id,\n        type: 'restock',\n        quantity: quantity,\n        reason: restockReason,\n        performedBy: 'Admin', // In a real app, this would be the current user\n        notes: `Restocked ${quantity} units`\n      });\n\n      setRestockDialogOpen(false);\n      setSelectedProductForRestock(null);\n      setRestockQuantity('');\n      setRestockReason('Regular restock');\n    }\n  };\n\n  const handleMarkAsUsed = (product) => {\n    // Mark 1 unit as used\n    addStockMovement({\n      productId: product.id,\n      type: 'usage',\n      quantity: -1,\n      reason: 'Marked as used',\n      performedBy: 'Admin',\n      notes: 'Marked as used from expiring products'\n    });\n  };\n\n  const confirmDelete = () => {\n    if (productToDelete) {\n      deleteProduct(productToDelete.id);\n      setDeleteDialogOpen(false);\n      setProductToDelete(null);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 'bold' }}>\n          Inventory Management\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => window.location.reload()}\n          >\n            Refresh\n          </Button>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n          >\n            Export\n          </Button>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            sx={{ bgcolor: 'primary.main' }}\n            onClick={handleAddProduct}\n          >\n            Add Product\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Statistics Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Products\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {products.length}\n                  </Typography>\n                </Box>\n                <InventoryIcon sx={{ fontSize: 40, color: 'primary.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Low Stock Items\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"warning.main\">\n                    {lowStockProducts.length}\n                  </Typography>\n                </Box>\n                <Badge badgeContent={lowStockProducts.length} color=\"warning\">\n                  <WarningIcon sx={{ fontSize: 40, color: 'warning.main' }} />\n                </Badge>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Out of Stock\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"error.main\">\n                    {outOfStockProducts.length}\n                  </Typography>\n                </Box>\n                <TrendingDownIcon sx={{ fontSize: 40, color: 'error.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Total Value\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {formatCurrency(totalValue)}\n                  </Typography>\n                </Box>\n                <MoneyIcon sx={{ fontSize: 40, color: 'success.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Alerts */}\n      {lowStockProducts.length > 0 && (\n        <Alert severity=\"warning\" sx={{ mb: 2 }}>\n          <strong>{lowStockProducts.length} products</strong> are running low on stock and need restocking.\n        </Alert>\n      )}\n      \n      {outOfStockProducts.length > 0 && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          <strong>{outOfStockProducts.length} products</strong> are out of stock.\n        </Alert>\n      )}\n      \n      {expiringProducts.length > 0 && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          <strong>{expiringProducts.length} products</strong> are expiring within 30 days.\n        </Alert>\n      )}\n\n      {/* Tabs */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>\n          <Tab label=\"All Products\" />\n          <Tab label=\"Low Stock\" />\n          <Tab label=\"Expiring Soon\" />\n          <Tab label=\"Stock Movements\" />\n        </Tabs>\n      </Paper>\n\n      {/* Search and Filters */}\n      <TabPanel value={currentTab} index={0}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon />\n                    </InputAdornment>\n                  ),\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={categoryFilter}\n                  onChange={(e) => setCategoryFilter(e.target.value)}\n                  label=\"Category\"\n                >\n                  <MenuItem value=\"all\">All Categories</MenuItem>\n                  {categories.map((category) => (\n                    <MenuItem key={category} value={category}>\n                      {category}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Stock Status</InputLabel>\n                <Select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  label=\"Stock Status\"\n                >\n                  <MenuItem value=\"all\">All Status</MenuItem>\n                  <MenuItem value=\"normal\">In Stock</MenuItem>\n                  <MenuItem value=\"low\">Low Stock</MenuItem>\n                  <MenuItem value=\"out\">Out of Stock</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<FilterIcon />}\n                onClick={() => {\n                  setSearchTerm('');\n                  setCategoryFilter('all');\n                  setStatusFilter('all');\n                }}\n              >\n                Clear\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n      </TabPanel>\n\n      {/* Products Table */}\n      <TabPanel value={currentTab} index={0}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>SKU</TableCell>\n                <TableCell>Category</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Stock Level</TableCell>\n                <TableCell>Unit Price</TableCell>\n                <TableCell>Total Value</TableCell>\n                <TableCell>Expiry Status</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {filteredProducts.map((product) => {\n                const stockStatus = getStockStatus(product);\n                const stockPercentage = getStockPercentage(product);\n                const expiryStatus = getExpiryStatus(product);\n\n                return (\n                  <TableRow\n                    key={product.id}\n                    sx={{\n                      backgroundColor: expiryStatus && (expiryStatus.color === 'error' || expiryStatus.color === 'warning')\n                        ? `${expiryStatus.color === 'error' ? 'error' : 'warning'}.50`\n                        : 'inherit',\n                      '&:hover': {\n                        backgroundColor: expiryStatus && (expiryStatus.color === 'error' || expiryStatus.color === 'warning')\n                          ? `${expiryStatus.color === 'error' ? 'error' : 'warning'}.100`\n                          : 'action.hover'\n                      }\n                    }}\n                  >\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {product.name}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {product.brand}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{product.sku}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={product.category}\n                        size=\"small\"\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {product.currentStock} / {product.maxStockLevel}\n                      </Typography>\n                      <LinearProgress\n                        variant=\"determinate\"\n                        value={stockPercentage}\n                        sx={{ mt: 1, height: 6, borderRadius: 3 }}\n                        color={stockStatus.color}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        Min: {product.minStockLevel}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        Max: {product.maxStockLevel}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{formatCurrency(product.unitPrice)}</TableCell>\n                    <TableCell>\n                      {formatCurrency(product.currentStock * product.unitPrice)}\n                    </TableCell>\n                    <TableCell>\n                      {expiryStatus ? (\n                        <Box>\n                          <Chip\n                            label={expiryStatus.status}\n                            color={expiryStatus.color}\n                            size=\"small\"\n                            sx={{ mb: 0.5 }}\n                          />\n                          <Typography variant=\"caption\" display=\"block\" color=\"textSecondary\">\n                            {expiryStatus.days > 0 ? `${expiryStatus.days} days` : `${expiryStatus.days} days ago`}\n                          </Typography>\n                          {product.expiryDate && (\n                            <Typography variant=\"caption\" display=\"block\" color=\"textSecondary\">\n                              {product.expiryDate}\n                            </Typography>\n                          )}\n                        </Box>\n                      ) : (\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          No expiry date\n                        </Typography>\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={stockStatus.status}\n                        color={stockStatus.color}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ display: 'flex', gap: 1 }}>\n                        <Tooltip title=\"Edit Product\">\n                          <IconButton\n                            size=\"small\"\n                            color=\"primary\"\n                            onClick={() => handleEditProduct(product)}\n                          >\n                            <EditIcon />\n                          </IconButton>\n                        </Tooltip>\n                        <Tooltip title=\"Delete Product\">\n                          <IconButton\n                            size=\"small\"\n                            color=\"error\"\n                            onClick={() => handleDeleteProduct(product)}\n                          >\n                            <DeleteIcon />\n                          </IconButton>\n                        </Tooltip>\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Low Stock Tab */}\n      <TabPanel value={currentTab} index={1}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Min Level</TableCell>\n                <TableCell>Shortage</TableCell>\n                <TableCell>Last Restocked</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {lowStockProducts.map((product) => (\n                <TableRow key={product.id}>\n                  <TableCell>\n                    <Box>\n                      <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                        {product.name}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        {product.brand} - {product.sku}\n                      </Typography>\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={product.currentStock}\n                      color=\"warning\"\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{product.minStockLevel}</TableCell>\n                  <TableCell>\n                    <Typography color=\"error.main\" fontWeight=\"bold\">\n                      {product.minStockLevel - product.currentStock}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{product.lastRestocked}</TableCell>\n                  <TableCell>\n                    <Button\n                      variant=\"contained\"\n                      size=\"small\"\n                      color=\"warning\"\n                      onClick={() => handleRestockClick(product)}\n                    >\n                      Restock\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Expiring Products Tab */}\n      <TabPanel value={currentTab} index={2}>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Product</TableCell>\n                <TableCell>Current Stock</TableCell>\n                <TableCell>Expiry Date</TableCell>\n                <TableCell>Days Until Expiry</TableCell>\n                <TableCell>Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {expiringProducts.map((product) => {\n                const daysUntilExpiry = Math.ceil(\n                  (new Date(product.expiryDate) - new Date()) / (1000 * 60 * 60 * 24)\n                );\n\n                return (\n                  <TableRow key={product.id}>\n                    <TableCell>\n                      <Box>\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {product.name}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {product.brand} - {product.sku}\n                        </Typography>\n                      </Box>\n                    </TableCell>\n                    <TableCell>{product.currentStock}</TableCell>\n                    <TableCell>{product.expiryDate}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={`${daysUntilExpiry} days`}\n                        color={daysUntilExpiry <= 7 ? 'error' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        color=\"primary\"\n                        onClick={() => handleMarkAsUsed(product)}\n                      >\n                        Mark as Used\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Stock Movements Tab */}\n      <TabPanel value={currentTab} index={3}>\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>\n          Recent Stock Movements\n        </Typography>\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Date</TableCell>\n                <TableCell>Product</TableCell>\n                <TableCell>Type</TableCell>\n                <TableCell>Quantity</TableCell>\n                <TableCell>Reason</TableCell>\n                <TableCell>Performed By</TableCell>\n                <TableCell>Notes</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {stockMovements\n                .sort((a, b) => new Date(b.date) - new Date(a.date))\n                .slice(0, 50) // Show last 50 movements\n                .map((movement) => {\n                  const product = getProductById(movement.productId);\n                  return (\n                    <TableRow key={movement.id}>\n                      <TableCell>{movement.date}</TableCell>\n                      <TableCell>\n                        <Box>\n                          <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                            {product?.name || 'Unknown Product'}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"textSecondary\">\n                            {product?.sku || 'N/A'}\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={movement.type === 'restock' ? 'Restock' : 'Usage'}\n                          color={movement.type === 'restock' ? 'success' : 'warning'}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography\n                          color={movement.quantity > 0 ? 'success.main' : 'error.main'}\n                          fontWeight=\"bold\"\n                        >\n                          {movement.quantity > 0 ? '+' : ''}{movement.quantity}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>{movement.reason}</TableCell>\n                      <TableCell>{movement.performedBy}</TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {movement.notes}\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              {stockMovements.length === 0 && (\n                <TableRow>\n                  <TableCell colSpan={7} align=\"center\">\n                    <Typography color=\"textSecondary\">\n                      No stock movements recorded yet.\n                    </Typography>\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </TabPanel>\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Confirm Delete</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{productToDelete?.name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={confirmDelete} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Product Form Dialog */}\n      <ProductForm\n        open={productFormOpen}\n        onClose={handleCloseProductForm}\n        product={editingProduct}\n        mode={formMode}\n      />\n\n      {/* Restock Dialog */}\n      <Dialog open={restockDialogOpen} onClose={() => setRestockDialogOpen(false)}>\n        <DialogTitle>Restock Product</DialogTitle>\n        <DialogContent>\n          {selectedProductForRestock && (\n            <Box sx={{ pt: 2 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedProductForRestock.name}\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n                Current Stock: {selectedProductForRestock.currentStock} • Min Level: {selectedProductForRestock.minStockLevel}\n              </Typography>\n\n              <TextField\n                fullWidth\n                label=\"Restock Quantity\"\n                type=\"number\"\n                value={restockQuantity}\n                onChange={(e) => setRestockQuantity(e.target.value)}\n                sx={{ mt: 2, mb: 2 }}\n                inputProps={{ min: 1 }}\n              />\n\n              <FormControl fullWidth sx={{ mb: 2 }}>\n                <InputLabel>Reason</InputLabel>\n                <Select\n                  value={restockReason}\n                  onChange={(e) => setRestockReason(e.target.value)}\n                  label=\"Reason\"\n                >\n                  <MenuItem value=\"Regular restock\">Regular restock</MenuItem>\n                  <MenuItem value=\"Emergency restock\">Emergency restock</MenuItem>\n                  <MenuItem value=\"Bulk purchase\">Bulk purchase</MenuItem>\n                  <MenuItem value=\"Supplier delivery\">Supplier delivery</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setRestockDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleRestockSubmit} variant=\"contained\">\n            Restock\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,WAAW,IAAIC,SAAS,EACxBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMf,SAAS,GAAGA,CAAA,KAAM;EAAAgB,EAAA;EACtB,MAAM;IACJC,QAAQ;IACRC,cAAc;IACdC,mBAAmB;IACnBC,qBAAqB;IACrBC,mBAAmB;IACnBC,sBAAsB;IACtBC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC;EACF,CAAC,GAAGd,YAAY,CAAC,CAAC;EAElB,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2E,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuF,QAAQ,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2F,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAChF,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,iBAAiB,CAAC;;EAErE;EACA,MAAMiG,gBAAgB,GAAGtC,QAAQ,CAACuC,MAAM,CAACC,OAAO,IAAI;IAClD,MAAMC,aAAa,GAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAC9DH,OAAO,CAACK,GAAG,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAC5DH,OAAO,CAACM,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;IACnF,MAAMI,eAAe,GAAGjC,cAAc,KAAK,KAAK,IAAI0B,OAAO,CAACQ,QAAQ,KAAKlC,cAAc;IACvF,MAAMmC,aAAa,GAAGjC,YAAY,KAAK,KAAK,IACtBA,YAAY,KAAK,KAAK,IAAIwB,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACW,aAAc,IACxEnC,YAAY,KAAK,KAAK,IAAIwB,OAAO,CAACU,YAAY,KAAK,CAAE,IACrDlC,YAAY,KAAK,QAAQ,IAAIwB,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACW,aAAc;IAEhG,MAAMC,YAAY,GAAGC,eAAe,CAACb,OAAO,CAAC;IAC7C,MAAMc,aAAa,GAAGpC,YAAY,KAAK,KAAK,IACtBA,YAAY,KAAK,UAAU,IAAIkC,YAAY,KAAKA,YAAY,CAACG,KAAK,KAAK,SAAS,IAAIH,YAAY,CAACG,KAAK,KAAK,OAAO,CAAE,IACpHrC,YAAY,KAAK,SAAS,IAAIkC,YAAY,IAAIA,YAAY,CAACG,KAAK,KAAK,OAAO,IAAIH,YAAY,CAACI,IAAI,GAAG,CAAE,IACtGtC,YAAY,KAAK,MAAM,KAAK,CAACkC,YAAY,IAAIA,YAAY,CAACG,KAAK,KAAK,SAAS,CAAE;IAErG,OAAOd,aAAa,IAAIM,eAAe,IAAIE,aAAa,IAAIK,aAAa;EAC3E,CAAC,CAAC;;EAEF;EACA,MAAMG,gBAAgB,GAAGvD,mBAAmB,CAAC,CAAC;EAC9C,MAAMwD,kBAAkB,GAAGvD,qBAAqB,CAAC,CAAC;EAClD,MAAMwD,gBAAgB,GAAGvD,mBAAmB,CAAC,CAAC;EAC9C,MAAMwD,UAAU,GAAGvD,sBAAsB,CAAC,CAAC;EAC3C,MAAMwD,UAAU,GAAGvD,aAAa,CAAC,CAAC;EAElC,MAAMwD,cAAc,GAAItB,OAAO,IAAK;IAClC,IAAIA,OAAO,CAACU,YAAY,KAAK,CAAC,EAAE,OAAO;MAAEa,MAAM,EAAE,cAAc;MAAER,KAAK,EAAE;IAAQ,CAAC;IACjF,IAAIf,OAAO,CAACU,YAAY,IAAIV,OAAO,CAACW,aAAa,EAAE,OAAO;MAAEY,MAAM,EAAE,WAAW;MAAER,KAAK,EAAE;IAAU,CAAC;IACnG,OAAO;MAAEQ,MAAM,EAAE,UAAU;MAAER,KAAK,EAAE;IAAU,CAAC;EACjD,CAAC;EAED,MAAMS,kBAAkB,GAAIxB,OAAO,IAAK;IACtC,OAAOyB,IAAI,CAACC,GAAG,CAAE1B,OAAO,CAACU,YAAY,GAAGV,OAAO,CAAC2B,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;EAC5E,CAAC;EAED,MAAMd,eAAe,GAAIb,OAAO,IAAK;IACnC,IAAI,CAACA,OAAO,CAAC4B,UAAU,EAAE,OAAO,IAAI;IAEpC,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,MAAMF,UAAU,GAAG,IAAIE,IAAI,CAAC9B,OAAO,CAAC4B,UAAU,CAAC;IAC/C,MAAMG,eAAe,GAAGN,IAAI,CAACO,IAAI,CAAC,CAACJ,UAAU,GAAGC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/E,IAAIE,eAAe,GAAG,CAAC,EAAE;MACvB,OAAO;QAAER,MAAM,EAAE,SAAS;QAAER,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAES,IAAI,CAACQ,GAAG,CAACF,eAAe;MAAE,CAAC;IAC/E,CAAC,MAAM,IAAIA,eAAe,IAAI,CAAC,EAAE;MAC/B,OAAO;QAAER,MAAM,EAAE,cAAc;QAAER,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAEe;MAAgB,CAAC;IAC1E,CAAC,MAAM,IAAIA,eAAe,IAAI,EAAE,EAAE;MAChC,OAAO;QAAER,MAAM,EAAE,UAAU;QAAER,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAEe;MAAgB,CAAC;IACxE;IAEA,OAAO;MAAER,MAAM,EAAE,MAAM;MAAER,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAEe;IAAgB,CAAC;EACpE,CAAC;EAED,MAAMG,mBAAmB,GAAIlC,OAAO,IAAK;IACvCjB,kBAAkB,CAACiB,OAAO,CAAC;IAC3BnB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMsD,iBAAiB,GAAInC,OAAO,IAAK;IACrCb,iBAAiB,CAACa,OAAO,CAAC;IAC1BX,WAAW,CAAC,MAAM,CAAC;IACnBJ,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjD,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC,KAAK,CAAC;IAClBJ,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoD,sBAAsB,GAAGA,CAAA,KAAM;IACnCpD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmD,kBAAkB,GAAItC,OAAO,IAAK;IACtCP,4BAA4B,CAACO,OAAO,CAAC;IACrCL,kBAAkB,CAAC8B,IAAI,CAACc,GAAG,CAACvC,OAAO,CAAC2B,aAAa,GAAG3B,OAAO,CAACU,YAAY,EAAE,CAAC,CAAC,CAAC8B,QAAQ,CAAC,CAAC,CAAC;IACxFjD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMkD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIjD,yBAAyB,IAAIE,eAAe,EAAE;MAChD,MAAMgD,QAAQ,GAAGC,QAAQ,CAACjD,eAAe,CAAC;;MAE1C;MACA1B,gBAAgB,CAAC;QACf4E,SAAS,EAAEpD,yBAAyB,CAACqD,EAAE;QACvCC,IAAI,EAAE,SAAS;QACfJ,QAAQ,EAAEA,QAAQ;QAClBK,MAAM,EAAEnD,aAAa;QACrBoD,WAAW,EAAE,OAAO;QAAE;QACtBC,KAAK,EAAE,aAAaP,QAAQ;MAC9B,CAAC,CAAC;MAEFnD,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,4BAA4B,CAAC,IAAI,CAAC;MAClCE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,gBAAgB,CAAC,iBAAiB,CAAC;IACrC;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAIlD,OAAO,IAAK;IACpC;IACAhC,gBAAgB,CAAC;MACf4E,SAAS,EAAE5C,OAAO,CAAC6C,EAAE;MACrBC,IAAI,EAAE,OAAO;MACbJ,QAAQ,EAAE,CAAC,CAAC;MACZK,MAAM,EAAE,gBAAgB;MACxBC,WAAW,EAAE,OAAO;MACpBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIrE,eAAe,EAAE;MACnBf,aAAa,CAACe,eAAe,CAAC+D,EAAE,CAAC;MACjChE,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAMqE,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CxG,OAAA;IAAKyG,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIxG,OAAA,CAACxD,GAAG;MAACkK,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,oBACE/G,OAAA,CAACxD,GAAG;IAACkK,EAAE,EAAE;MAAEM,CAAC,EAAE;IAAE,CAAE;IAAAV,QAAA,gBAEhBtG,OAAA,CAACxD,GAAG;MAACkK,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAd,QAAA,gBACzFtG,OAAA,CAACtD,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACZ,EAAE,EAAE;UAAEa,UAAU,EAAE;QAAO,CAAE;QAAAjB,QAAA,EAAC;MAEpE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/G,OAAA,CAACxD,GAAG;QAACkK,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEM,GAAG,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACnCtG,OAAA,CAAC5C,MAAM;UACLiK,OAAO,EAAC,UAAU;UAClBI,SAAS,eAAEzH,OAAA,CAACN,WAAW;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BW,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAvB,QAAA,EACzC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/G,OAAA,CAAC5C,MAAM;UACLiK,OAAO,EAAC,UAAU;UAClBI,SAAS,eAAEzH,OAAA,CAACJ,UAAU;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,EAC3B;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/G,OAAA,CAAC5C,MAAM;UACLiK,OAAO,EAAC,WAAW;UACnBI,SAAS,eAAEzH,OAAA,CAACtB,OAAO;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBL,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAe,CAAE;UAChCJ,OAAO,EAAE5C,gBAAiB;UAAAwB,QAAA,EAC3B;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/G,OAAA,CAACrD,IAAI;MAACoL,SAAS;MAACC,OAAO,EAAE,CAAE;MAACtB,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACxCtG,OAAA,CAACrD,IAAI;QAACsL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9BtG,OAAA,CAACpD,IAAI;UAAA0J,QAAA,eACHtG,OAAA,CAACnD,WAAW;YAAAyJ,QAAA,eACVtG,OAAA,CAACxD,GAAG;cAACkK,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClFtG,OAAA,CAACxD,GAAG;gBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,eAAe;kBAAC4E,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/G,OAAA,CAACtD,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAAC5D,KAAK,EAAC,cAAc;kBAAA6C,QAAA,EAC1CpG,QAAQ,CAACoI;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/G,OAAA,CAACd,aAAa;gBAACwH,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAE9E,KAAK,EAAE;gBAAe;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/G,OAAA,CAACrD,IAAI;QAACsL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9BtG,OAAA,CAACpD,IAAI;UAAA0J,QAAA,eACHtG,OAAA,CAACnD,WAAW;YAAAyJ,QAAA,eACVtG,OAAA,CAACxD,GAAG;cAACkK,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClFtG,OAAA,CAACxD,GAAG;gBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,eAAe;kBAAC4E,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/G,OAAA,CAACtD,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAAC5D,KAAK,EAAC,cAAc;kBAAA6C,QAAA,EAC1C3C,gBAAgB,CAAC2E;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/G,OAAA,CAACjC,KAAK;gBAACyK,YAAY,EAAE7E,gBAAgB,CAAC2E,MAAO;gBAAC7E,KAAK,EAAC,SAAS;gBAAA6C,QAAA,eAC3DtG,OAAA,CAAChB,WAAW;kBAAC0H,EAAE,EAAE;oBAAE6B,QAAQ,EAAE,EAAE;oBAAE9E,KAAK,EAAE;kBAAe;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/G,OAAA,CAACrD,IAAI;QAACsL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9BtG,OAAA,CAACpD,IAAI;UAAA0J,QAAA,eACHtG,OAAA,CAACnD,WAAW;YAAAyJ,QAAA,eACVtG,OAAA,CAACxD,GAAG;cAACkK,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClFtG,OAAA,CAACxD,GAAG;gBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,eAAe;kBAAC4E,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/G,OAAA,CAACtD,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAAC5D,KAAK,EAAC,YAAY;kBAAA6C,QAAA,EACxC1C,kBAAkB,CAAC0E;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/G,OAAA,CAACZ,gBAAgB;gBAACsH,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAE9E,KAAK,EAAE;gBAAa;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/G,OAAA,CAACrD,IAAI;QAACsL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAC9BtG,OAAA,CAACpD,IAAI;UAAA0J,QAAA,eACHtG,OAAA,CAACnD,WAAW;YAAAyJ,QAAA,eACVtG,OAAA,CAACxD,GAAG;cAACkK,EAAE,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAb,QAAA,gBAClFtG,OAAA,CAACxD,GAAG;gBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,eAAe;kBAAC4E,YAAY;kBAAA/B,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/G,OAAA,CAACtD,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAAC5D,KAAK,EAAC,cAAc;kBAAA6C,QAAA,EAC1CR,cAAc,CAAChC,UAAU;gBAAC;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/G,OAAA,CAACV,SAAS;gBAACoH,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAE9E,KAAK,EAAE;gBAAe;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNpD,gBAAgB,CAAC2E,MAAM,GAAG,CAAC,iBAC1BtI,OAAA,CAAClC,KAAK;MAAC2K,QAAQ,EAAC,SAAS;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACtCtG,OAAA;QAAAsG,QAAA,GAAS3C,gBAAgB,CAAC2E,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,kDACrD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEAnD,kBAAkB,CAAC0E,MAAM,GAAG,CAAC,iBAC5BtI,OAAA,CAAClC,KAAK;MAAC2K,QAAQ,EAAC,OAAO;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACpCtG,OAAA;QAAAsG,QAAA,GAAS1C,kBAAkB,CAAC0E,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,sBACvD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEAlD,gBAAgB,CAACyE,MAAM,GAAG,CAAC,iBAC1BtI,OAAA,CAAClC,KAAK;MAAC2K,QAAQ,EAAC,MAAM;MAAC/B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACnCtG,OAAA;QAAAsG,QAAA,GAASzC,gBAAgB,CAACyE,MAAM,EAAC,WAAS;MAAA;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,iCACrD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAGD/G,OAAA,CAACvD,KAAK;MAACiK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACnBtG,OAAA,CAAChC,IAAI;QAACuI,KAAK,EAAE3F,UAAW;QAAC8H,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK/H,aAAa,CAAC+H,QAAQ,CAAE;QAAAtC,QAAA,gBAC1EtG,OAAA,CAAC/B,GAAG;UAAC4K,KAAK,EAAC;QAAc;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B/G,OAAA,CAAC/B,GAAG;UAAC4K,KAAK,EAAC;QAAW;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB/G,OAAA,CAAC/B,GAAG;UAAC4K,KAAK,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7B/G,OAAA,CAAC/B,GAAG;UAAC4K,KAAK,EAAC;QAAiB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/G,OAAA,CAACqG,QAAQ;MAACE,KAAK,EAAE3F,UAAW;MAAC4F,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCtG,OAAA,CAACvD,KAAK;QAACiK,EAAE,EAAE;UAAEM,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,eACzBtG,OAAA,CAACrD,IAAI;UAACoL,SAAS;UAACC,OAAO,EAAE,CAAE;UAACZ,UAAU,EAAC,QAAQ;UAAAd,QAAA,gBAC7CtG,OAAA,CAACrD,IAAI;YAACsL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBtG,OAAA,CAAClD,SAAS;cACRgM,SAAS;cACTC,WAAW,EAAC,oBAAoB;cAChCxC,KAAK,EAAEzF,UAAW;cAClB4H,QAAQ,EAAGC,CAAC,IAAK5H,aAAa,CAAC4H,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;cAC/C0C,UAAU,EAAE;gBACVC,cAAc,eACZlJ,OAAA,CAACjD,cAAc;kBAACoM,QAAQ,EAAC,OAAO;kBAAA7C,QAAA,eAC9BtG,OAAA,CAACxB,UAAU;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAEpB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP/G,OAAA,CAACrD,IAAI;YAACsL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBtG,OAAA,CAAChD,WAAW;cAAC8L,SAAS;cAAAxC,QAAA,gBACpBtG,OAAA,CAAC/C,UAAU;gBAAAqJ,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC/G,OAAA,CAAC9C,MAAM;gBACLqJ,KAAK,EAAEvF,cAAe;gBACtB0H,QAAQ,EAAGC,CAAC,IAAK1H,iBAAiB,CAAC0H,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;gBACnDsC,KAAK,EAAC,UAAU;gBAAAvC,QAAA,gBAEhBtG,OAAA,CAAC7C,QAAQ;kBAACoJ,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC9ChD,UAAU,CAACqF,GAAG,CAAElG,QAAQ,iBACvBlD,OAAA,CAAC7C,QAAQ;kBAAgBoJ,KAAK,EAAErD,QAAS;kBAAAoD,QAAA,EACtCpD;gBAAQ,GADIA,QAAQ;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP/G,OAAA,CAACrD,IAAI;YAACsL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBtG,OAAA,CAAChD,WAAW;cAAC8L,SAAS;cAAAxC,QAAA,gBACpBtG,OAAA,CAAC/C,UAAU;gBAAAqJ,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC/G,OAAA,CAAC9C,MAAM;gBACLqJ,KAAK,EAAErF,YAAa;gBACpBwH,QAAQ,EAAGC,CAAC,IAAKxH,eAAe,CAACwH,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;gBACjDsC,KAAK,EAAC,cAAc;gBAAAvC,QAAA,gBAEpBtG,OAAA,CAAC7C,QAAQ;kBAACoJ,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC3C/G,OAAA,CAAC7C,QAAQ;kBAACoJ,KAAK,EAAC,QAAQ;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC5C/G,OAAA,CAAC7C,QAAQ;kBAACoJ,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1C/G,OAAA,CAAC7C,QAAQ;kBAACoJ,KAAK,EAAC,KAAK;kBAAAD,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACP/G,OAAA,CAACrD,IAAI;YAACsL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBtG,OAAA,CAAC5C,MAAM;cACL0L,SAAS;cACTzB,OAAO,EAAC,UAAU;cAClBI,SAAS,eAAEzH,OAAA,CAACR,UAAU;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BW,OAAO,EAAEA,CAAA,KAAM;gBACb3G,aAAa,CAAC,EAAE,CAAC;gBACjBE,iBAAiB,CAAC,KAAK,CAAC;gBACxBE,eAAe,CAAC,KAAK,CAAC;cACxB,CAAE;cAAAmF,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX/G,OAAA,CAACqG,QAAQ;MAACE,KAAK,EAAE3F,UAAW;MAAC4F,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCtG,OAAA,CAACvC,cAAc;QAAC6J,SAAS,EAAE7K,KAAM;QAAA6J,QAAA,eAC/BtG,OAAA,CAAC1C,KAAK;UAAAgJ,QAAA,gBACJtG,OAAA,CAACtC,SAAS;YAAA4I,QAAA,eACRtG,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAU;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/G,OAAA,CAACzC,SAAS;YAAA+I,QAAA,EACP9D,gBAAgB,CAAC4G,GAAG,CAAE1G,OAAO,IAAK;cACjC,MAAM2G,WAAW,GAAGrF,cAAc,CAACtB,OAAO,CAAC;cAC3C,MAAM4G,eAAe,GAAGpF,kBAAkB,CAACxB,OAAO,CAAC;cACnD,MAAMY,YAAY,GAAGC,eAAe,CAACb,OAAO,CAAC;cAE7C,oBACE1C,OAAA,CAACrC,QAAQ;gBAEP+I,EAAE,EAAE;kBACF6C,eAAe,EAAEjG,YAAY,KAAKA,YAAY,CAACG,KAAK,KAAK,OAAO,IAAIH,YAAY,CAACG,KAAK,KAAK,SAAS,CAAC,GACjG,GAAGH,YAAY,CAACG,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS,KAAK,GAC5D,SAAS;kBACb,SAAS,EAAE;oBACT8F,eAAe,EAAEjG,YAAY,KAAKA,YAAY,CAACG,KAAK,KAAK,OAAO,IAAIH,YAAY,CAACG,KAAK,KAAK,SAAS,CAAC,GACjG,GAAGH,YAAY,CAACG,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS,MAAM,GAC7D;kBACN;gBACF,CAAE;gBAAA6C,QAAA,gBAEFtG,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACxD,GAAG;oBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAjB,QAAA,EAC9C5D,OAAO,CAACE;oBAAI;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACb/G,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAAC5D,KAAK,EAAC,eAAe;sBAAA6C,QAAA,EAC9C5D,OAAO,CAACM;oBAAK;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE5D,OAAO,CAACK;gBAAG;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpC/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAAC3C,IAAI;oBACHwL,KAAK,EAAEnG,OAAO,CAACQ,QAAS;oBACxBsG,IAAI,EAAC,OAAO;oBACZnC,OAAO,EAAC;kBAAU;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,gBACRtG,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAAf,QAAA,GACxB5D,OAAO,CAACU,YAAY,EAAC,KAAG,EAACV,OAAO,CAAC2B,aAAa;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACb/G,OAAA,CAAC1B,cAAc;oBACb+I,OAAO,EAAC,aAAa;oBACrBd,KAAK,EAAE+C,eAAgB;oBACvB5C,EAAE,EAAE;sBAAE+C,EAAE,EAAE,CAAC;sBAAEC,MAAM,EAAE,CAAC;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAC1ClG,KAAK,EAAE4F,WAAW,CAAC5F;kBAAM;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,gBACRtG,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAC5D,KAAK,EAAC,eAAe;oBAAA6C,QAAA,GAAC,OAC3C,EAAC5D,OAAO,CAACW,aAAa;kBAAA;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACb/G,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAC5D,KAAK,EAAC,eAAe;oBAAA6C,QAAA,GAAC,OAC3C,EAAC5D,OAAO,CAAC2B,aAAa;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAER,cAAc,CAACpD,OAAO,CAACkH,SAAS;gBAAC;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1D/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EACPR,cAAc,CAACpD,OAAO,CAACU,YAAY,GAAGV,OAAO,CAACkH,SAAS;gBAAC;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EACPhD,YAAY,gBACXtD,OAAA,CAACxD,GAAG;oBAAA8J,QAAA,gBACFtG,OAAA,CAAC3C,IAAI;sBACHwL,KAAK,EAAEvF,YAAY,CAACW,MAAO;sBAC3BR,KAAK,EAAEH,YAAY,CAACG,KAAM;sBAC1B+F,IAAI,EAAC,OAAO;sBACZ9C,EAAE,EAAE;wBAAEO,EAAE,EAAE;sBAAI;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACF/G,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,SAAS;sBAACH,OAAO,EAAC,OAAO;sBAACzD,KAAK,EAAC,eAAe;sBAAA6C,QAAA,EAChEhD,YAAY,CAACI,IAAI,GAAG,CAAC,GAAG,GAAGJ,YAAY,CAACI,IAAI,OAAO,GAAG,GAAGJ,YAAY,CAACI,IAAI;oBAAW;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC,EACZrE,OAAO,CAAC4B,UAAU,iBACjBtE,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,SAAS;sBAACH,OAAO,EAAC,OAAO;sBAACzD,KAAK,EAAC,eAAe;sBAAA6C,QAAA,EAChE5D,OAAO,CAAC4B;oBAAU;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CACb;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAEN/G,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAC5D,KAAK,EAAC,eAAe;oBAAA6C,QAAA,EAAC;kBAElD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAAC3C,IAAI;oBACHwL,KAAK,EAAEQ,WAAW,CAACpF,MAAO;oBAC1BR,KAAK,EAAE4F,WAAW,CAAC5F,KAAM;oBACzB+F,IAAI,EAAC;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACxD,GAAG;oBAACkK,EAAE,EAAE;sBAAEQ,OAAO,EAAE,MAAM;sBAAEM,GAAG,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBACnCtG,OAAA,CAACnC,OAAO;sBAACgM,KAAK,EAAC,cAAc;sBAAAvD,QAAA,eAC3BtG,OAAA,CAACpC,UAAU;wBACT4L,IAAI,EAAC,OAAO;wBACZ/F,KAAK,EAAC,SAAS;wBACfiE,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAACnC,OAAO,CAAE;wBAAA4D,QAAA,eAE1CtG,OAAA,CAACpB,QAAQ;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACV/G,OAAA,CAACnC,OAAO;sBAACgM,KAAK,EAAC,gBAAgB;sBAAAvD,QAAA,eAC7BtG,OAAA,CAACpC,UAAU;wBACT4L,IAAI,EAAC,OAAO;wBACZ/F,KAAK,EAAC,OAAO;wBACbiE,OAAO,EAAEA,CAAA,KAAM9C,mBAAmB,CAAClC,OAAO,CAAE;wBAAA4D,QAAA,eAE5CtG,OAAA,CAAClB,UAAU;0BAAA8H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAzGPrE,OAAO,CAAC6C,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0GP,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGX/G,OAAA,CAACqG,QAAQ;MAACE,KAAK,EAAE3F,UAAW;MAAC4F,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCtG,OAAA,CAACvC,cAAc;QAAC6J,SAAS,EAAE7K,KAAM;QAAA6J,QAAA,eAC/BtG,OAAA,CAAC1C,KAAK;UAAAgJ,QAAA,gBACJtG,OAAA,CAACtC,SAAS;YAAA4I,QAAA,eACRtG,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/G,OAAA,CAACzC,SAAS;YAAA+I,QAAA,EACP3C,gBAAgB,CAACyF,GAAG,CAAE1G,OAAO,iBAC5B1C,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,eACRtG,OAAA,CAACxD,GAAG;kBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,WAAW;oBAACE,UAAU,EAAC,MAAM;oBAAAjB,QAAA,EAC9C5D,OAAO,CAACE;kBAAI;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACb/G,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAC5D,KAAK,EAAC,eAAe;oBAAA6C,QAAA,GAC9C5D,OAAO,CAACM,KAAK,EAAC,KAAG,EAACN,OAAO,CAACK,GAAG;kBAAA;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZ/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,eACRtG,OAAA,CAAC3C,IAAI;kBACHwL,KAAK,EAAEnG,OAAO,CAACU,YAAa;kBAC5BK,KAAK,EAAC,SAAS;kBACf+F,IAAI,EAAC;gBAAO;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAE5D,OAAO,CAACW;cAAa;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,eACRtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,YAAY;kBAAC8D,UAAU,EAAC,MAAM;kBAAAjB,QAAA,EAC7C5D,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACU;gBAAY;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAE5D,OAAO,CAACoH;cAAa;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,eACRtG,OAAA,CAAC5C,MAAM;kBACLiK,OAAO,EAAC,WAAW;kBACnBmC,IAAI,EAAC,OAAO;kBACZ/F,KAAK,EAAC,SAAS;kBACfiE,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAACtC,OAAO,CAAE;kBAAA4D,QAAA,EAC5C;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAlCCrE,OAAO,CAAC6C,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCf,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGX/G,OAAA,CAACqG,QAAQ;MAACE,KAAK,EAAE3F,UAAW;MAAC4F,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCtG,OAAA,CAACvC,cAAc;QAAC6J,SAAS,EAAE7K,KAAM;QAAA6J,QAAA,eAC/BtG,OAAA,CAAC1C,KAAK;UAAAgJ,QAAA,gBACJtG,OAAA,CAACtC,SAAS;YAAA4I,QAAA,eACRtG,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/G,OAAA,CAACzC,SAAS;YAAA+I,QAAA,EACPzC,gBAAgB,CAACuF,GAAG,CAAE1G,OAAO,IAAK;cACjC,MAAM+B,eAAe,GAAGN,IAAI,CAACO,IAAI,CAC/B,CAAC,IAAIF,IAAI,CAAC9B,OAAO,CAAC4B,UAAU,CAAC,GAAG,IAAIE,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CACpE,CAAC;cAED,oBACExE,OAAA,CAACrC,QAAQ;gBAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACxD,GAAG;oBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAjB,QAAA,EAC9C5D,OAAO,CAACE;oBAAI;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACb/G,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAAC5D,KAAK,EAAC,eAAe;sBAAA6C,QAAA,GAC9C5D,OAAO,CAACM,KAAK,EAAC,KAAG,EAACN,OAAO,CAACK,GAAG;oBAAA;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE5D,OAAO,CAACU;gBAAY;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE5D,OAAO,CAAC4B;gBAAU;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3C/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAAC3C,IAAI;oBACHwL,KAAK,EAAE,GAAGpE,eAAe,OAAQ;oBACjChB,KAAK,EAAEgB,eAAe,IAAI,CAAC,GAAG,OAAO,GAAG,SAAU;oBAClD+E,IAAI,EAAC;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAAC5C,MAAM;oBACLiK,OAAO,EAAC,UAAU;oBAClBmC,IAAI,EAAC,OAAO;oBACZ/F,KAAK,EAAC,SAAS;oBACfiE,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAClD,OAAO,CAAE;oBAAA4D,QAAA,EAC1C;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA7BCrE,OAAO,CAAC6C,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Bf,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGX/G,OAAA,CAACqG,QAAQ;MAACE,KAAK,EAAE3F,UAAW;MAAC4F,KAAK,EAAE,CAAE;MAAAF,QAAA,gBACpCtG,OAAA,CAACtD,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EAAC;MAExC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/G,OAAA,CAACvC,cAAc;QAAC6J,SAAS,EAAE7K,KAAM;QAAA6J,QAAA,eAC/BtG,OAAA,CAAC1C,KAAK;UAAAgJ,QAAA,gBACJtG,OAAA,CAACtC,SAAS;YAAA4I,QAAA,eACRtG,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnC/G,OAAA,CAACxC,SAAS;gBAAA8I,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/G,OAAA,CAACzC,SAAS;YAAA+I,QAAA,GACPnG,cAAc,CACZ4J,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzF,IAAI,CAACyF,CAAC,CAACC,IAAI,CAAC,GAAG,IAAI1F,IAAI,CAACwF,CAAC,CAACE,IAAI,CAAC,CAAC,CACnDC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAAA,CACbf,GAAG,CAAEgB,QAAQ,IAAK;cACjB,MAAM1H,OAAO,GAAG/B,cAAc,CAACyJ,QAAQ,CAAC9E,SAAS,CAAC;cAClD,oBACEtF,OAAA,CAACrC,QAAQ;gBAAA2I,QAAA,gBACPtG,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE8D,QAAQ,CAACF;gBAAI;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACxD,GAAG;oBAAA8J,QAAA,gBACFtG,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAjB,QAAA,EAC9C,CAAA5D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,IAAI,KAAI;oBAAiB;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACb/G,OAAA,CAACtD,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAAC5D,KAAK,EAAC,eAAe;sBAAA6C,QAAA,EAC9C,CAAA5D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,GAAG,KAAI;oBAAK;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAAC3C,IAAI;oBACHwL,KAAK,EAAEuB,QAAQ,CAAC5E,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,OAAQ;oBACzD/B,KAAK,EAAE2G,QAAQ,CAAC5E,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAU;oBAC3DgE,IAAI,EAAC;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACtD,UAAU;oBACT+G,KAAK,EAAE2G,QAAQ,CAAChF,QAAQ,GAAG,CAAC,GAAG,cAAc,GAAG,YAAa;oBAC7DmC,UAAU,EAAC,MAAM;oBAAAjB,QAAA,GAEhB8D,QAAQ,CAAChF,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEgF,QAAQ,CAAChF,QAAQ;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE8D,QAAQ,CAAC3E;gBAAM;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxC/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,EAAE8D,QAAQ,CAAC1E;gBAAW;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C/G,OAAA,CAACxC,SAAS;kBAAA8I,QAAA,eACRtG,OAAA,CAACtD,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAAC5D,KAAK,EAAC,eAAe;oBAAA6C,QAAA,EAC9C8D,QAAQ,CAACzE;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAjCCqD,QAAQ,CAAC7E,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkChB,CAAC;YAEf,CAAC,CAAC,EACH5G,cAAc,CAACmI,MAAM,KAAK,CAAC,iBAC1BtI,OAAA,CAACrC,QAAQ;cAAA2I,QAAA,eACPtG,OAAA,CAACxC,SAAS;gBAAC6M,OAAO,EAAE,CAAE;gBAACC,KAAK,EAAC,QAAQ;gBAAAhE,QAAA,eACnCtG,OAAA,CAACtD,UAAU;kBAAC+G,KAAK,EAAC,eAAe;kBAAA6C,QAAA,EAAC;gBAElC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGX/G,OAAA,CAAC9B,MAAM;MAACqM,IAAI,EAAEjJ,gBAAiB;MAACkJ,OAAO,EAAEA,CAAA,KAAMjJ,mBAAmB,CAAC,KAAK,CAAE;MAAA+E,QAAA,gBACxEtG,OAAA,CAAC7B,WAAW;QAAAmI,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC/G,OAAA,CAAC5B,aAAa;QAAAkI,QAAA,eACZtG,OAAA,CAACtD,UAAU;UAAA4J,QAAA,GAAC,oCACuB,EAAC9E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB,IAAI,EAAC,mCAC1D;QAAA;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB/G,OAAA,CAAC3B,aAAa;QAAAiI,QAAA,gBACZtG,OAAA,CAAC5C,MAAM;UAACsK,OAAO,EAAEA,CAAA,KAAMnG,mBAAmB,CAAC,KAAK,CAAE;UAAA+E,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClE/G,OAAA,CAAC5C,MAAM;UAACsK,OAAO,EAAE7B,aAAc;UAACpC,KAAK,EAAC,OAAO;UAAC4D,OAAO,EAAC,WAAW;UAAAf,QAAA,EAAC;QAElE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/G,OAAA,CAACF,WAAW;MACVyK,IAAI,EAAE7I,eAAgB;MACtB8I,OAAO,EAAEzF,sBAAuB;MAChCrC,OAAO,EAAEd,cAAe;MACxB6I,IAAI,EAAE3I;IAAS;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGF/G,OAAA,CAAC9B,MAAM;MAACqM,IAAI,EAAEvI,iBAAkB;MAACwI,OAAO,EAAEA,CAAA,KAAMvI,oBAAoB,CAAC,KAAK,CAAE;MAAAqE,QAAA,gBAC1EtG,OAAA,CAAC7B,WAAW;QAAAmI,QAAA,EAAC;MAAe;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1C/G,OAAA,CAAC5B,aAAa;QAAAkI,QAAA,EACXpE,yBAAyB,iBACxBlC,OAAA,CAACxD,GAAG;UAACkK,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACjBtG,OAAA,CAACtD,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACgB,YAAY;YAAA/B,QAAA,EAClCpE,yBAAyB,CAACU;UAAI;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACb/G,OAAA,CAACtD,UAAU;YAAC2K,OAAO,EAAC,OAAO;YAAC5D,KAAK,EAAC,eAAe;YAAC4E,YAAY;YAAA/B,QAAA,GAAC,iBAC9C,EAACpE,yBAAyB,CAACkB,YAAY,EAAC,qBAAc,EAAClB,yBAAyB,CAACmB,aAAa;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC,eAEb/G,OAAA,CAAClD,SAAS;YACRgM,SAAS;YACTD,KAAK,EAAC,kBAAkB;YACxBrD,IAAI,EAAC,QAAQ;YACbe,KAAK,EAAEnE,eAAgB;YACvBsG,QAAQ,EAAGC,CAAC,IAAKtG,kBAAkB,CAACsG,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;YACpDG,EAAE,EAAE;cAAE+C,EAAE,EAAE,CAAC;cAAExC,EAAE,EAAE;YAAE,CAAE;YACrByD,UAAU,EAAE;cAAEtG,GAAG,EAAE;YAAE;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEF/G,OAAA,CAAChD,WAAW;YAAC8L,SAAS;YAACpC,EAAE,EAAE;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,gBACnCtG,OAAA,CAAC/C,UAAU;cAAAqJ,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/B/G,OAAA,CAAC9C,MAAM;cACLqJ,KAAK,EAAEjE,aAAc;cACrBoG,QAAQ,EAAGC,CAAC,IAAKpG,gBAAgB,CAACoG,CAAC,CAACK,MAAM,CAACzC,KAAK,CAAE;cAClDsC,KAAK,EAAC,QAAQ;cAAAvC,QAAA,gBAEdtG,OAAA,CAAC7C,QAAQ;gBAACoJ,KAAK,EAAC,iBAAiB;gBAAAD,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5D/G,OAAA,CAAC7C,QAAQ;gBAACoJ,KAAK,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAChE/G,OAAA,CAAC7C,QAAQ;gBAACoJ,KAAK,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxD/G,OAAA,CAAC7C,QAAQ;gBAACoJ,KAAK,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB/G,OAAA,CAAC3B,aAAa;QAAAiI,QAAA,gBACZtG,OAAA,CAAC5C,MAAM;UAACsK,OAAO,EAAEA,CAAA,KAAMzF,oBAAoB,CAAC,KAAK,CAAE;UAAAqE,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnE/G,OAAA,CAAC5C,MAAM;UAACsK,OAAO,EAAEvC,mBAAoB;UAACkC,OAAO,EAAC,WAAW;UAAAf,QAAA,EAAC;QAE1D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAzwBIhB,SAAS;EAAA,QAYTY,YAAY;AAAA;AAAA8K,EAAA,GAZZ1L,SAAS;AA2wBf,eAAeA,SAAS;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}