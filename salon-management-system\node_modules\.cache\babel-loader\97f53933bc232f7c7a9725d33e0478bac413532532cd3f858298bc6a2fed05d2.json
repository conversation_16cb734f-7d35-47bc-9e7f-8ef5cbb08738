{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\contexts\\\\BillingContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BillingContext = /*#__PURE__*/createContext();\nexport const useBilling = () => {\n  _s();\n  const context = useContext(BillingContext);\n  if (!context) {\n    throw new Error('useBilling must be used within a BillingProvider');\n  }\n  return context;\n};\n_s(useBilling, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const BillingProvider = ({\n  children\n}) => {\n  _s2();\n  const [invoices, setInvoices] = useState([{\n    id: 'INV-2024-001',\n    customerId: 1,\n    customerName: '<PERSON>',\n    customerEmail: '<EMAIL>',\n    customerPhone: '(*************',\n    date: '2024-07-17',\n    dueDate: '2024-07-24',\n    services: [{\n      id: 1,\n      name: 'Hair Cut & Style',\n      price: 85,\n      stylist: 'Emma Wilson',\n      duration: 60\n    }, {\n      id: 2,\n      name: 'Hair Color',\n      price: 120,\n      stylist: 'Emma Wilson',\n      duration: 90\n    }],\n    subtotal: 205,\n    discountType: 'percentage',\n    discountValue: 10,\n    discountAmount: 20.5,\n    taxRate: 8.5,\n    taxAmount: 15.68,\n    total: 200.18,\n    status: 'paid',\n    paymentMethod: 'card',\n    paymentStatus: 'completed',\n    paymentDate: '2024-07-17',\n    transactionId: 'txn_1234567890',\n    notes: 'Regular customer - 10% loyalty discount applied',\n    createdAt: '2024-07-17T10:30:00Z',\n    updatedAt: '2024-07-17T10:35:00Z'\n  }, {\n    id: 'INV-2024-002',\n    customerId: 2,\n    customerName: 'Mike Davis',\n    customerEmail: '<EMAIL>',\n    customerPhone: '(*************',\n    date: '2024-07-17',\n    dueDate: '2024-07-24',\n    services: [{\n      id: 3,\n      name: 'Beard Trim',\n      price: 35,\n      stylist: 'John Smith',\n      duration: 30\n    }],\n    subtotal: 35,\n    discountType: null,\n    discountValue: 0,\n    discountAmount: 0,\n    taxRate: 8.5,\n    taxAmount: 2.98,\n    total: 37.98,\n    status: 'pending',\n    paymentMethod: null,\n    paymentStatus: 'pending',\n    paymentDate: null,\n    transactionId: null,\n    notes: '',\n    createdAt: '2024-07-17T14:15:00Z',\n    updatedAt: '2024-07-17T14:15:00Z'\n  }]);\n  const [payments, setPayments] = useState([{\n    id: 'PAY-001',\n    invoiceId: 'INV-2024-001',\n    amount: 200.18,\n    method: 'card',\n    status: 'completed',\n    transactionId: 'txn_1234567890',\n    gateway: 'stripe',\n    date: '2024-07-17T10:35:00Z',\n    cardLast4: '4242',\n    cardBrand: 'visa'\n  }]);\n  const [discounts, setDiscounts] = useState([{\n    id: 1,\n    code: 'WELCOME10',\n    name: 'Welcome Discount',\n    type: 'percentage',\n    value: 10,\n    minAmount: 50,\n    maxDiscount: 25,\n    validFrom: '2024-01-01',\n    validTo: '2024-12-31',\n    usageLimit: 100,\n    usedCount: 15,\n    status: 'active',\n    description: 'Welcome discount for new customers'\n  }, {\n    id: 2,\n    code: 'SUMMER20',\n    name: 'Summer Special',\n    type: 'percentage',\n    value: 20,\n    minAmount: 100,\n    maxDiscount: 50,\n    validFrom: '2024-06-01',\n    validTo: '2024-08-31',\n    usageLimit: 50,\n    usedCount: 8,\n    status: 'active',\n    description: 'Summer season discount'\n  }, {\n    id: 3,\n    code: 'FLAT15',\n    name: 'Flat Discount',\n    type: 'fixed',\n    value: 15,\n    minAmount: 75,\n    maxDiscount: 15,\n    validFrom: '2024-07-01',\n    validTo: '2024-07-31',\n    usageLimit: 25,\n    usedCount: 12,\n    status: 'active',\n    description: 'Fixed amount discount'\n  }]);\n  const [transactions, setTransactions] = useState([{\n    id: 'TXN-001',\n    invoiceId: 'INV-2024-001',\n    type: 'payment',\n    amount: 200.18,\n    method: 'card',\n    status: 'completed',\n    date: '2024-07-17T10:35:00Z',\n    description: 'Payment for services'\n  }, {\n    id: 'TXN-002',\n    invoiceId: 'INV-2024-001',\n    type: 'refund',\n    amount: -20.00,\n    method: 'card',\n    status: 'completed',\n    date: '2024-07-17T16:20:00Z',\n    description: 'Partial refund - service adjustment'\n  }]);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedInvoices = localStorage.getItem('salon_billing_invoices');\n    const savedPayments = localStorage.getItem('salon_billing_payments');\n    const savedDiscounts = localStorage.getItem('salon_billing_discounts');\n    const savedTransactions = localStorage.getItem('salon_billing_transactions');\n    if (savedInvoices) setInvoices(JSON.parse(savedInvoices));\n    if (savedPayments) setPayments(JSON.parse(savedPayments));\n    if (savedDiscounts) setDiscounts(JSON.parse(savedDiscounts));\n    if (savedTransactions) setTransactions(JSON.parse(savedTransactions));\n  }, []);\n\n  // Save to localStorage whenever data changes\n  useEffect(() => {\n    localStorage.setItem('salon_billing_invoices', JSON.stringify(invoices));\n  }, [invoices]);\n  useEffect(() => {\n    localStorage.setItem('salon_billing_payments', JSON.stringify(payments));\n  }, [payments]);\n  useEffect(() => {\n    localStorage.setItem('salon_billing_discounts', JSON.stringify(discounts));\n  }, [discounts]);\n  useEffect(() => {\n    localStorage.setItem('salon_billing_transactions', JSON.stringify(transactions));\n  }, [transactions]);\n\n  // Helper functions\n  const generateInvoiceNumber = () => {\n    const year = new Date().getFullYear();\n    const count = invoices.length + 1;\n    return `INV-${year}-${count.toString().padStart(3, '0')}`;\n  };\n  const calculateInvoiceTotal = (services, discountType, discountValue, taxRate) => {\n    const subtotal = services.reduce((sum, service) => sum + service.price, 0);\n    let discountAmount = 0;\n    if (discountType === 'percentage') {\n      discountAmount = subtotal * discountValue / 100;\n    } else if (discountType === 'fixed') {\n      discountAmount = discountValue;\n    }\n    const afterDiscount = subtotal - discountAmount;\n    const taxAmount = afterDiscount * taxRate / 100;\n    const total = afterDiscount + taxAmount;\n    return {\n      subtotal: parseFloat(subtotal.toFixed(2)),\n      discountAmount: parseFloat(discountAmount.toFixed(2)),\n      taxAmount: parseFloat(taxAmount.toFixed(2)),\n      total: parseFloat(total.toFixed(2))\n    };\n  };\n  const validateDiscount = (code, subtotal) => {\n    const discount = discounts.find(d => d.code.toLowerCase() === code.toLowerCase() && d.status === 'active');\n    if (!discount) {\n      return {\n        valid: false,\n        error: 'Invalid discount code'\n      };\n    }\n    const now = new Date();\n    const validFrom = new Date(discount.validFrom);\n    const validTo = new Date(discount.validTo);\n    if (now < validFrom || now > validTo) {\n      return {\n        valid: false,\n        error: 'Discount code has expired'\n      };\n    }\n    if (subtotal < discount.minAmount) {\n      return {\n        valid: false,\n        error: `Minimum amount required: $${discount.minAmount}`\n      };\n    }\n    if (discount.usedCount >= discount.usageLimit) {\n      return {\n        valid: false,\n        error: 'Discount code usage limit reached'\n      };\n    }\n    return {\n      valid: true,\n      discount\n    };\n  };\n  const getRevenueStats = (period = 'month') => {\n    const now = new Date();\n    const paidInvoices = invoices.filter(inv => inv.status === 'paid');\n    let filteredInvoices = paidInvoices;\n    if (period === 'day') {\n      const today = now.toISOString().split('T')[0];\n      filteredInvoices = paidInvoices.filter(inv => inv.date === today);\n    } else if (period === 'week') {\n      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= weekAgo);\n    } else if (period === 'month') {\n      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= monthAgo);\n    }\n    const totalRevenue = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalInvoices = filteredInvoices.length;\n    const averageInvoice = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;\n    return {\n      totalRevenue: parseFloat(totalRevenue.toFixed(2)),\n      totalInvoices,\n      averageInvoice: parseFloat(averageInvoice.toFixed(2))\n    };\n  };\n  const getMostBookedServices = () => {\n    const serviceStats = {};\n    invoices.forEach(invoice => {\n      invoice.services.forEach(service => {\n        if (!serviceStats[service.name]) {\n          serviceStats[service.name] = {\n            name: service.name,\n            count: 0,\n            revenue: 0\n          };\n        }\n        serviceStats[service.name].count += 1;\n        serviceStats[service.name].revenue += service.price;\n      });\n    });\n    return Object.values(serviceStats).sort((a, b) => b.count - a.count).slice(0, 10);\n  };\n  const getStaffPerformance = () => {\n    const staffStats = {};\n    invoices.forEach(invoice => {\n      invoice.services.forEach(service => {\n        if (!staffStats[service.stylist]) {\n          staffStats[service.stylist] = {\n            name: service.stylist,\n            serviceCount: 0,\n            revenue: 0,\n            averageService: 0\n          };\n        }\n        staffStats[service.stylist].serviceCount += 1;\n        staffStats[service.stylist].revenue += service.price;\n      });\n    });\n    Object.values(staffStats).forEach(staff => {\n      staff.averageService = staff.serviceCount > 0 ? staff.revenue / staff.serviceCount : 0;\n      staff.revenue = parseFloat(staff.revenue.toFixed(2));\n      staff.averageService = parseFloat(staff.averageService.toFixed(2));\n    });\n    return Object.values(staffStats).sort((a, b) => b.revenue - a.revenue);\n  };\n\n  // CRUD operations for invoices\n  const createInvoice = invoiceData => {\n    const newInvoice = {\n      ...invoiceData,\n      id: generateInvoiceNumber(),\n      date: new Date().toISOString().split('T')[0],\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      status: 'pending',\n      paymentStatus: 'pending',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    const totals = calculateInvoiceTotal(newInvoice.services, newInvoice.discountType, newInvoice.discountValue, newInvoice.taxRate || 8.5);\n    Object.assign(newInvoice, totals);\n    setInvoices(prev => [...prev, newInvoice]);\n    return newInvoice;\n  };\n  const updateInvoice = (id, updates) => {\n    setInvoices(prev => prev.map(invoice => invoice.id === id ? {\n      ...invoice,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    } : invoice));\n  };\n  const deleteInvoice = id => {\n    setInvoices(prev => prev.filter(invoice => invoice.id !== id));\n    setPayments(prev => prev.filter(payment => payment.invoiceId !== id));\n    setTransactions(prev => prev.filter(transaction => transaction.invoiceId !== id));\n  };\n\n  // Payment operations\n  const processPayment = paymentData => {\n    const newPayment = {\n      ...paymentData,\n      id: `PAY-${Date.now()}`,\n      date: new Date().toISOString(),\n      status: 'completed' // In real app, this would be set by payment gateway\n    };\n    setPayments(prev => [...prev, newPayment]);\n\n    // Update invoice status\n    updateInvoice(paymentData.invoiceId, {\n      status: 'paid',\n      paymentStatus: 'completed',\n      paymentDate: newPayment.date,\n      paymentMethod: paymentData.method,\n      transactionId: newPayment.transactionId\n    });\n\n    // Add transaction record\n    const transaction = {\n      id: `TXN-${Date.now()}`,\n      invoiceId: paymentData.invoiceId,\n      type: 'payment',\n      amount: paymentData.amount,\n      method: paymentData.method,\n      status: 'completed',\n      date: newPayment.date,\n      description: 'Payment for services'\n    };\n    setTransactions(prev => [...prev, transaction]);\n    return newPayment;\n  };\n\n  // Discount operations\n  const createDiscount = discountData => {\n    const newDiscount = {\n      ...discountData,\n      id: Math.max(...discounts.map(d => d.id), 0) + 1,\n      usedCount: 0,\n      status: 'active'\n    };\n    setDiscounts(prev => [...prev, newDiscount]);\n    return newDiscount;\n  };\n  const updateDiscount = (id, updates) => {\n    setDiscounts(prev => prev.map(discount => discount.id === id ? {\n      ...discount,\n      ...updates\n    } : discount));\n  };\n  const deleteDiscount = id => {\n    setDiscounts(prev => prev.filter(discount => discount.id !== id));\n  };\n  const applyDiscount = code => {\n    setDiscounts(prev => prev.map(discount => discount.code.toLowerCase() === code.toLowerCase() ? {\n      ...discount,\n      usedCount: discount.usedCount + 1\n    } : discount));\n  };\n  const value = {\n    // State\n    invoices,\n    payments,\n    discounts,\n    transactions,\n    // Helper functions\n    generateInvoiceNumber,\n    calculateInvoiceTotal,\n    validateDiscount,\n    getRevenueStats,\n    getMostBookedServices,\n    getStaffPerformance,\n    // CRUD operations\n    createInvoice,\n    updateInvoice,\n    deleteInvoice,\n    processPayment,\n    createDiscount,\n    updateDiscount,\n    deleteDiscount,\n    applyDiscount,\n    // Setters for direct manipulation if needed\n    setInvoices,\n    setPayments,\n    setDiscounts,\n    setTransactions\n  };\n  return /*#__PURE__*/_jsxDEV(BillingContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 482,\n    columnNumber: 5\n  }, this);\n};\n_s2(BillingProvider, \"SG7+mI6AryNUeWDAnSnEC3g4SS4=\");\n_c = BillingProvider;\nvar _c;\n$RefreshReg$(_c, \"BillingProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "BillingContext", "useBilling", "_s", "context", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s2", "invoices", "setInvoices", "id", "customerId", "customerName", "customerEmail", "customerPhone", "date", "dueDate", "services", "name", "price", "stylist", "duration", "subtotal", "discountType", "discountValue", "discountAmount", "taxRate", "taxAmount", "total", "status", "paymentMethod", "paymentStatus", "paymentDate", "transactionId", "notes", "createdAt", "updatedAt", "payments", "setPayments", "invoiceId", "amount", "method", "gateway", "cardLast4", "card<PERSON>rand", "discounts", "setDiscounts", "code", "type", "value", "minAmount", "maxDiscount", "validFrom", "validTo", "usageLimit", "usedCount", "description", "transactions", "setTransactions", "savedInvoices", "localStorage", "getItem", "savedPayments", "savedDiscounts", "savedTransactions", "JSON", "parse", "setItem", "stringify", "generateInvoiceNumber", "year", "Date", "getFullYear", "count", "length", "toString", "padStart", "calculateInvoiceTotal", "reduce", "sum", "service", "afterDiscount", "parseFloat", "toFixed", "validateDiscount", "discount", "find", "d", "toLowerCase", "valid", "error", "now", "getRevenueStats", "period", "paidInvoices", "filter", "inv", "filteredInvoices", "today", "toISOString", "split", "weekAgo", "getTime", "monthAgo", "totalRevenue", "totalInvoices", "averageInvoice", "getMostBookedServices", "serviceStats", "for<PERSON>ach", "invoice", "revenue", "Object", "values", "sort", "a", "b", "slice", "getStaffPerformance", "staffStats", "serviceCount", "averageService", "staff", "createInvoice", "invoiceData", "newInvoice", "totals", "assign", "prev", "updateInvoice", "updates", "map", "deleteInvoice", "payment", "transaction", "processPayment", "paymentData", "newPayment", "createDiscount", "discountData", "newDiscount", "Math", "max", "updateDiscount", "deleteDiscount", "applyDiscount", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/contexts/BillingContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst BillingContext = createContext();\n\nexport const useBilling = () => {\n  const context = useContext(BillingContext);\n  if (!context) {\n    throw new Error('useBilling must be used within a BillingProvider');\n  }\n  return context;\n};\n\nexport const BillingProvider = ({ children }) => {\n  const [invoices, setInvoices] = useState([\n    {\n      id: 'INV-2024-001',\n      customerId: 1,\n      customerName: '<PERSON>',\n      customerEmail: '<EMAIL>',\n      customerPhone: '(*************',\n      date: '2024-07-17',\n      dueDate: '2024-07-24',\n      services: [\n        {\n          id: 1,\n          name: 'Hair Cut & Style',\n          price: 85,\n          stylist: '<PERSON>',\n          duration: 60\n        },\n        {\n          id: 2,\n          name: 'Hair Color',\n          price: 120,\n          stylist: '<PERSON>',\n          duration: 90\n        }\n      ],\n      subtotal: 205,\n      discountType: 'percentage',\n      discountValue: 10,\n      discountAmount: 20.5,\n      taxRate: 8.5,\n      taxAmount: 15.68,\n      total: 200.18,\n      status: 'paid',\n      paymentMethod: 'card',\n      paymentStatus: 'completed',\n      paymentDate: '2024-07-17',\n      transactionId: 'txn_1234567890',\n      notes: 'Regular customer - 10% loyalty discount applied',\n      createdAt: '2024-07-17T10:30:00Z',\n      updatedAt: '2024-07-17T10:35:00Z'\n    },\n    {\n      id: 'INV-2024-002',\n      customerId: 2,\n      customerName: 'Mike Davis',\n      customerEmail: '<EMAIL>',\n      customerPhone: '(*************',\n      date: '2024-07-17',\n      dueDate: '2024-07-24',\n      services: [\n        {\n          id: 3,\n          name: 'Beard Trim',\n          price: 35,\n          stylist: 'John Smith',\n          duration: 30\n        }\n      ],\n      subtotal: 35,\n      discountType: null,\n      discountValue: 0,\n      discountAmount: 0,\n      taxRate: 8.5,\n      taxAmount: 2.98,\n      total: 37.98,\n      status: 'pending',\n      paymentMethod: null,\n      paymentStatus: 'pending',\n      paymentDate: null,\n      transactionId: null,\n      notes: '',\n      createdAt: '2024-07-17T14:15:00Z',\n      updatedAt: '2024-07-17T14:15:00Z'\n    }\n  ]);\n\n  const [payments, setPayments] = useState([\n    {\n      id: 'PAY-001',\n      invoiceId: 'INV-2024-001',\n      amount: 200.18,\n      method: 'card',\n      status: 'completed',\n      transactionId: 'txn_1234567890',\n      gateway: 'stripe',\n      date: '2024-07-17T10:35:00Z',\n      cardLast4: '4242',\n      cardBrand: 'visa'\n    }\n  ]);\n\n  const [discounts, setDiscounts] = useState([\n    {\n      id: 1,\n      code: 'WELCOME10',\n      name: 'Welcome Discount',\n      type: 'percentage',\n      value: 10,\n      minAmount: 50,\n      maxDiscount: 25,\n      validFrom: '2024-01-01',\n      validTo: '2024-12-31',\n      usageLimit: 100,\n      usedCount: 15,\n      status: 'active',\n      description: 'Welcome discount for new customers'\n    },\n    {\n      id: 2,\n      code: 'SUMMER20',\n      name: 'Summer Special',\n      type: 'percentage',\n      value: 20,\n      minAmount: 100,\n      maxDiscount: 50,\n      validFrom: '2024-06-01',\n      validTo: '2024-08-31',\n      usageLimit: 50,\n      usedCount: 8,\n      status: 'active',\n      description: 'Summer season discount'\n    },\n    {\n      id: 3,\n      code: 'FLAT15',\n      name: 'Flat Discount',\n      type: 'fixed',\n      value: 15,\n      minAmount: 75,\n      maxDiscount: 15,\n      validFrom: '2024-07-01',\n      validTo: '2024-07-31',\n      usageLimit: 25,\n      usedCount: 12,\n      status: 'active',\n      description: 'Fixed amount discount'\n    }\n  ]);\n\n  const [transactions, setTransactions] = useState([\n    {\n      id: 'TXN-001',\n      invoiceId: 'INV-2024-001',\n      type: 'payment',\n      amount: 200.18,\n      method: 'card',\n      status: 'completed',\n      date: '2024-07-17T10:35:00Z',\n      description: 'Payment for services'\n    },\n    {\n      id: 'TXN-002',\n      invoiceId: 'INV-2024-001',\n      type: 'refund',\n      amount: -20.00,\n      method: 'card',\n      status: 'completed',\n      date: '2024-07-17T16:20:00Z',\n      description: 'Partial refund - service adjustment'\n    }\n  ]);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedInvoices = localStorage.getItem('salon_billing_invoices');\n    const savedPayments = localStorage.getItem('salon_billing_payments');\n    const savedDiscounts = localStorage.getItem('salon_billing_discounts');\n    const savedTransactions = localStorage.getItem('salon_billing_transactions');\n\n    if (savedInvoices) setInvoices(JSON.parse(savedInvoices));\n    if (savedPayments) setPayments(JSON.parse(savedPayments));\n    if (savedDiscounts) setDiscounts(JSON.parse(savedDiscounts));\n    if (savedTransactions) setTransactions(JSON.parse(savedTransactions));\n  }, []);\n\n  // Save to localStorage whenever data changes\n  useEffect(() => {\n    localStorage.setItem('salon_billing_invoices', JSON.stringify(invoices));\n  }, [invoices]);\n\n  useEffect(() => {\n    localStorage.setItem('salon_billing_payments', JSON.stringify(payments));\n  }, [payments]);\n\n  useEffect(() => {\n    localStorage.setItem('salon_billing_discounts', JSON.stringify(discounts));\n  }, [discounts]);\n\n  useEffect(() => {\n    localStorage.setItem('salon_billing_transactions', JSON.stringify(transactions));\n  }, [transactions]);\n\n  // Helper functions\n  const generateInvoiceNumber = () => {\n    const year = new Date().getFullYear();\n    const count = invoices.length + 1;\n    return `INV-${year}-${count.toString().padStart(3, '0')}`;\n  };\n\n  const calculateInvoiceTotal = (services, discountType, discountValue, taxRate) => {\n    const subtotal = services.reduce((sum, service) => sum + service.price, 0);\n    \n    let discountAmount = 0;\n    if (discountType === 'percentage') {\n      discountAmount = (subtotal * discountValue) / 100;\n    } else if (discountType === 'fixed') {\n      discountAmount = discountValue;\n    }\n    \n    const afterDiscount = subtotal - discountAmount;\n    const taxAmount = (afterDiscount * taxRate) / 100;\n    const total = afterDiscount + taxAmount;\n    \n    return {\n      subtotal: parseFloat(subtotal.toFixed(2)),\n      discountAmount: parseFloat(discountAmount.toFixed(2)),\n      taxAmount: parseFloat(taxAmount.toFixed(2)),\n      total: parseFloat(total.toFixed(2))\n    };\n  };\n\n  const validateDiscount = (code, subtotal) => {\n    const discount = discounts.find(d => \n      d.code.toLowerCase() === code.toLowerCase() && \n      d.status === 'active'\n    );\n    \n    if (!discount) {\n      return { valid: false, error: 'Invalid discount code' };\n    }\n    \n    const now = new Date();\n    const validFrom = new Date(discount.validFrom);\n    const validTo = new Date(discount.validTo);\n    \n    if (now < validFrom || now > validTo) {\n      return { valid: false, error: 'Discount code has expired' };\n    }\n    \n    if (subtotal < discount.minAmount) {\n      return { valid: false, error: `Minimum amount required: $${discount.minAmount}` };\n    }\n    \n    if (discount.usedCount >= discount.usageLimit) {\n      return { valid: false, error: 'Discount code usage limit reached' };\n    }\n    \n    return { valid: true, discount };\n  };\n\n  const getRevenueStats = (period = 'month') => {\n    const now = new Date();\n    const paidInvoices = invoices.filter(inv => inv.status === 'paid');\n    \n    let filteredInvoices = paidInvoices;\n    \n    if (period === 'day') {\n      const today = now.toISOString().split('T')[0];\n      filteredInvoices = paidInvoices.filter(inv => inv.date === today);\n    } else if (period === 'week') {\n      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= weekAgo);\n    } else if (period === 'month') {\n      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= monthAgo);\n    }\n    \n    const totalRevenue = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);\n    const totalInvoices = filteredInvoices.length;\n    const averageInvoice = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;\n    \n    return {\n      totalRevenue: parseFloat(totalRevenue.toFixed(2)),\n      totalInvoices,\n      averageInvoice: parseFloat(averageInvoice.toFixed(2))\n    };\n  };\n\n  const getMostBookedServices = () => {\n    const serviceStats = {};\n    \n    invoices.forEach(invoice => {\n      invoice.services.forEach(service => {\n        if (!serviceStats[service.name]) {\n          serviceStats[service.name] = {\n            name: service.name,\n            count: 0,\n            revenue: 0\n          };\n        }\n        serviceStats[service.name].count += 1;\n        serviceStats[service.name].revenue += service.price;\n      });\n    });\n    \n    return Object.values(serviceStats)\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 10);\n  };\n\n  const getStaffPerformance = () => {\n    const staffStats = {};\n    \n    invoices.forEach(invoice => {\n      invoice.services.forEach(service => {\n        if (!staffStats[service.stylist]) {\n          staffStats[service.stylist] = {\n            name: service.stylist,\n            serviceCount: 0,\n            revenue: 0,\n            averageService: 0\n          };\n        }\n        staffStats[service.stylist].serviceCount += 1;\n        staffStats[service.stylist].revenue += service.price;\n      });\n    });\n    \n    Object.values(staffStats).forEach(staff => {\n      staff.averageService = staff.serviceCount > 0 ? staff.revenue / staff.serviceCount : 0;\n      staff.revenue = parseFloat(staff.revenue.toFixed(2));\n      staff.averageService = parseFloat(staff.averageService.toFixed(2));\n    });\n    \n    return Object.values(staffStats).sort((a, b) => b.revenue - a.revenue);\n  };\n\n  // CRUD operations for invoices\n  const createInvoice = (invoiceData) => {\n    const newInvoice = {\n      ...invoiceData,\n      id: generateInvoiceNumber(),\n      date: new Date().toISOString().split('T')[0],\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      status: 'pending',\n      paymentStatus: 'pending',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    const totals = calculateInvoiceTotal(\n      newInvoice.services,\n      newInvoice.discountType,\n      newInvoice.discountValue,\n      newInvoice.taxRate || 8.5\n    );\n\n    Object.assign(newInvoice, totals);\n\n    setInvoices(prev => [...prev, newInvoice]);\n    return newInvoice;\n  };\n\n  const updateInvoice = (id, updates) => {\n    setInvoices(prev => prev.map(invoice =>\n      invoice.id === id\n        ? { ...invoice, ...updates, updatedAt: new Date().toISOString() }\n        : invoice\n    ));\n  };\n\n  const deleteInvoice = (id) => {\n    setInvoices(prev => prev.filter(invoice => invoice.id !== id));\n    setPayments(prev => prev.filter(payment => payment.invoiceId !== id));\n    setTransactions(prev => prev.filter(transaction => transaction.invoiceId !== id));\n  };\n\n  // Payment operations\n  const processPayment = (paymentData) => {\n    const newPayment = {\n      ...paymentData,\n      id: `PAY-${Date.now()}`,\n      date: new Date().toISOString(),\n      status: 'completed' // In real app, this would be set by payment gateway\n    };\n\n    setPayments(prev => [...prev, newPayment]);\n\n    // Update invoice status\n    updateInvoice(paymentData.invoiceId, {\n      status: 'paid',\n      paymentStatus: 'completed',\n      paymentDate: newPayment.date,\n      paymentMethod: paymentData.method,\n      transactionId: newPayment.transactionId\n    });\n\n    // Add transaction record\n    const transaction = {\n      id: `TXN-${Date.now()}`,\n      invoiceId: paymentData.invoiceId,\n      type: 'payment',\n      amount: paymentData.amount,\n      method: paymentData.method,\n      status: 'completed',\n      date: newPayment.date,\n      description: 'Payment for services'\n    };\n\n    setTransactions(prev => [...prev, transaction]);\n\n    return newPayment;\n  };\n\n  // Discount operations\n  const createDiscount = (discountData) => {\n    const newDiscount = {\n      ...discountData,\n      id: Math.max(...discounts.map(d => d.id), 0) + 1,\n      usedCount: 0,\n      status: 'active'\n    };\n\n    setDiscounts(prev => [...prev, newDiscount]);\n    return newDiscount;\n  };\n\n  const updateDiscount = (id, updates) => {\n    setDiscounts(prev => prev.map(discount =>\n      discount.id === id ? { ...discount, ...updates } : discount\n    ));\n  };\n\n  const deleteDiscount = (id) => {\n    setDiscounts(prev => prev.filter(discount => discount.id !== id));\n  };\n\n  const applyDiscount = (code) => {\n    setDiscounts(prev => prev.map(discount =>\n      discount.code.toLowerCase() === code.toLowerCase()\n        ? { ...discount, usedCount: discount.usedCount + 1 }\n        : discount\n    ));\n  };\n\n  const value = {\n    // State\n    invoices,\n    payments,\n    discounts,\n    transactions,\n\n    // Helper functions\n    generateInvoiceNumber,\n    calculateInvoiceTotal,\n    validateDiscount,\n    getRevenueStats,\n    getMostBookedServices,\n    getStaffPerformance,\n\n    // CRUD operations\n    createInvoice,\n    updateInvoice,\n    deleteInvoice,\n    processPayment,\n    createDiscount,\n    updateDiscount,\n    deleteDiscount,\n    applyDiscount,\n\n    // Setters for direct manipulation if needed\n    setInvoices,\n    setPayments,\n    setDiscounts,\n    setTransactions\n  };\n\n  return (\n    <BillingContext.Provider value={value}>\n      {children}\n    </BillingContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,cAAc,gBAAGN,aAAa,CAAC,CAAC;AAEtC,OAAO,MAAMO,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,OAAO,GAAGR,UAAU,CAACK,cAAc,CAAC;EAC1C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,kDAAkD,CAAC;EACrE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,UAAU;AAQvB,OAAO,MAAMI,eAAe,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC/C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,CACvC;IACEc,EAAE,EAAE,cAAc;IAClBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,yBAAyB;IACxCC,aAAa,EAAE,gBAAgB;IAC/BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,CACR;MACEP,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEX,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,QAAQ,EAAE,GAAG;IACbC,YAAY,EAAE,YAAY;IAC1BC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,IAAI;IACpBC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,WAAW;IAC1BC,WAAW,EAAE,YAAY;IACzBC,aAAa,EAAE,gBAAgB;IAC/BC,KAAK,EAAE,iDAAiD;IACxDC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACE1B,EAAE,EAAE,cAAc;IAClBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,YAAY;IAC1BC,aAAa,EAAE,sBAAsB;IACrCC,aAAa,EAAE,gBAAgB;IAC/BC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,CACR;MACEP,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE;IACZ,CAAC,CACF;IACDC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,CACvC;IACEc,EAAE,EAAE,SAAS;IACb6B,SAAS,EAAE,cAAc;IACzBC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,MAAM;IACdZ,MAAM,EAAE,WAAW;IACnBI,aAAa,EAAE,gBAAgB;IAC/BS,OAAO,EAAE,QAAQ;IACjB3B,IAAI,EAAE,sBAAsB;IAC5B4B,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,CACzC;IACEc,EAAE,EAAE,CAAC;IACLqC,IAAI,EAAE,WAAW;IACjB7B,IAAI,EAAE,kBAAkB;IACxB8B,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,EAAE;IACb1B,MAAM,EAAE,QAAQ;IAChB2B,WAAW,EAAE;EACf,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLqC,IAAI,EAAE,UAAU;IAChB7B,IAAI,EAAE,gBAAgB;IACtB8B,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,GAAG;IACdC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,CAAC;IACZ1B,MAAM,EAAE,QAAQ;IAChB2B,WAAW,EAAE;EACf,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLqC,IAAI,EAAE,QAAQ;IACd7B,IAAI,EAAE,eAAe;IACrB8B,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACb1B,MAAM,EAAE,QAAQ;IAChB2B,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,CAC/C;IACEc,EAAE,EAAE,SAAS;IACb6B,SAAS,EAAE,cAAc;IACzBS,IAAI,EAAE,SAAS;IACfR,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,MAAM;IACdZ,MAAM,EAAE,WAAW;IACnBd,IAAI,EAAE,sBAAsB;IAC5ByC,WAAW,EAAE;EACf,CAAC,EACD;IACE9C,EAAE,EAAE,SAAS;IACb6B,SAAS,EAAE,cAAc;IACzBS,IAAI,EAAE,QAAQ;IACdR,MAAM,EAAE,CAAC,KAAK;IACdC,MAAM,EAAE,MAAM;IACdZ,MAAM,EAAE,WAAW;IACnBd,IAAI,EAAE,sBAAsB;IAC5ByC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;;EAEF;EACA3D,SAAS,CAAC,MAAM;IACd,MAAM8D,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACpE,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACpE,MAAME,cAAc,GAAGH,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC;IACtE,MAAMG,iBAAiB,GAAGJ,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;IAE5E,IAAIF,aAAa,EAAElD,WAAW,CAACwD,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC,CAAC;IACzD,IAAIG,aAAa,EAAExB,WAAW,CAAC2B,IAAI,CAACC,KAAK,CAACJ,aAAa,CAAC,CAAC;IACzD,IAAIC,cAAc,EAAEjB,YAAY,CAACmB,IAAI,CAACC,KAAK,CAACH,cAAc,CAAC,CAAC;IAC5D,IAAIC,iBAAiB,EAAEN,eAAe,CAACO,IAAI,CAACC,KAAK,CAACF,iBAAiB,CAAC,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnE,SAAS,CAAC,MAAM;IACd+D,YAAY,CAACO,OAAO,CAAC,wBAAwB,EAAEF,IAAI,CAACG,SAAS,CAAC5D,QAAQ,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdX,SAAS,CAAC,MAAM;IACd+D,YAAY,CAACO,OAAO,CAAC,wBAAwB,EAAEF,IAAI,CAACG,SAAS,CAAC/B,QAAQ,CAAC,CAAC;EAC1E,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdxC,SAAS,CAAC,MAAM;IACd+D,YAAY,CAACO,OAAO,CAAC,yBAAyB,EAAEF,IAAI,CAACG,SAAS,CAACvB,SAAS,CAAC,CAAC;EAC5E,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEfhD,SAAS,CAAC,MAAM;IACd+D,YAAY,CAACO,OAAO,CAAC,4BAA4B,EAAEF,IAAI,CAACG,SAAS,CAACX,YAAY,CAAC,CAAC;EAClF,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMY,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACrC,MAAMC,KAAK,GAAGjE,QAAQ,CAACkE,MAAM,GAAG,CAAC;IACjC,OAAO,OAAOJ,IAAI,IAAIG,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC3D,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAC5D,QAAQ,EAAEM,YAAY,EAAEC,aAAa,EAAEE,OAAO,KAAK;IAChF,MAAMJ,QAAQ,GAAGL,QAAQ,CAAC6D,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAAC7D,KAAK,EAAE,CAAC,CAAC;IAE1E,IAAIM,cAAc,GAAG,CAAC;IACtB,IAAIF,YAAY,KAAK,YAAY,EAAE;MACjCE,cAAc,GAAIH,QAAQ,GAAGE,aAAa,GAAI,GAAG;IACnD,CAAC,MAAM,IAAID,YAAY,KAAK,OAAO,EAAE;MACnCE,cAAc,GAAGD,aAAa;IAChC;IAEA,MAAMyD,aAAa,GAAG3D,QAAQ,GAAGG,cAAc;IAC/C,MAAME,SAAS,GAAIsD,aAAa,GAAGvD,OAAO,GAAI,GAAG;IACjD,MAAME,KAAK,GAAGqD,aAAa,GAAGtD,SAAS;IAEvC,OAAO;MACLL,QAAQ,EAAE4D,UAAU,CAAC5D,QAAQ,CAAC6D,OAAO,CAAC,CAAC,CAAC,CAAC;MACzC1D,cAAc,EAAEyD,UAAU,CAACzD,cAAc,CAAC0D,OAAO,CAAC,CAAC,CAAC,CAAC;MACrDxD,SAAS,EAAEuD,UAAU,CAACvD,SAAS,CAACwD,OAAO,CAAC,CAAC,CAAC,CAAC;MAC3CvD,KAAK,EAAEsD,UAAU,CAACtD,KAAK,CAACuD,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC;EACH,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACrC,IAAI,EAAEzB,QAAQ,KAAK;IAC3C,MAAM+D,QAAQ,GAAGxC,SAAS,CAACyC,IAAI,CAACC,CAAC,IAC/BA,CAAC,CAACxC,IAAI,CAACyC,WAAW,CAAC,CAAC,KAAKzC,IAAI,CAACyC,WAAW,CAAC,CAAC,IAC3CD,CAAC,CAAC1D,MAAM,KAAK,QACf,CAAC;IAED,IAAI,CAACwD,QAAQ,EAAE;MACb,OAAO;QAAEI,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAwB,CAAC;IACzD;IAEA,MAAMC,GAAG,GAAG,IAAIpB,IAAI,CAAC,CAAC;IACtB,MAAMnB,SAAS,GAAG,IAAImB,IAAI,CAACc,QAAQ,CAACjC,SAAS,CAAC;IAC9C,MAAMC,OAAO,GAAG,IAAIkB,IAAI,CAACc,QAAQ,CAAChC,OAAO,CAAC;IAE1C,IAAIsC,GAAG,GAAGvC,SAAS,IAAIuC,GAAG,GAAGtC,OAAO,EAAE;MACpC,OAAO;QAAEoC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAA4B,CAAC;IAC7D;IAEA,IAAIpE,QAAQ,GAAG+D,QAAQ,CAACnC,SAAS,EAAE;MACjC,OAAO;QAAEuC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,6BAA6BL,QAAQ,CAACnC,SAAS;MAAG,CAAC;IACnF;IAEA,IAAImC,QAAQ,CAAC9B,SAAS,IAAI8B,QAAQ,CAAC/B,UAAU,EAAE;MAC7C,OAAO;QAAEmC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAoC,CAAC;IACrE;IAEA,OAAO;MAAED,KAAK,EAAE,IAAI;MAAEJ;IAAS,CAAC;EAClC,CAAC;EAED,MAAMO,eAAe,GAAGA,CAACC,MAAM,GAAG,OAAO,KAAK;IAC5C,MAAMF,GAAG,GAAG,IAAIpB,IAAI,CAAC,CAAC;IACtB,MAAMuB,YAAY,GAAGtF,QAAQ,CAACuF,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACnE,MAAM,KAAK,MAAM,CAAC;IAElE,IAAIoE,gBAAgB,GAAGH,YAAY;IAEnC,IAAID,MAAM,KAAK,KAAK,EAAE;MACpB,MAAMK,KAAK,GAAGP,GAAG,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7CH,gBAAgB,GAAGH,YAAY,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACjF,IAAI,KAAKmF,KAAK,CAAC;IACnE,CAAC,MAAM,IAAIL,MAAM,KAAK,MAAM,EAAE;MAC5B,MAAMQ,OAAO,GAAG,IAAI9B,IAAI,CAACoB,GAAG,CAACW,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACjEL,gBAAgB,GAAGH,YAAY,CAACC,MAAM,CAACC,GAAG,IAAI,IAAIzB,IAAI,CAACyB,GAAG,CAACjF,IAAI,CAAC,IAAIsF,OAAO,CAAC;IAC9E,CAAC,MAAM,IAAIR,MAAM,KAAK,OAAO,EAAE;MAC7B,MAAMU,QAAQ,GAAG,IAAIhC,IAAI,CAACoB,GAAG,CAACW,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACnEL,gBAAgB,GAAGH,YAAY,CAACC,MAAM,CAACC,GAAG,IAAI,IAAIzB,IAAI,CAACyB,GAAG,CAACjF,IAAI,CAAC,IAAIwF,QAAQ,CAAC;IAC/E;IAEA,MAAMC,YAAY,GAAGP,gBAAgB,CAACnB,MAAM,CAAC,CAACC,GAAG,EAAEiB,GAAG,KAAKjB,GAAG,GAAGiB,GAAG,CAACpE,KAAK,EAAE,CAAC,CAAC;IAC9E,MAAM6E,aAAa,GAAGR,gBAAgB,CAACvB,MAAM;IAC7C,MAAMgC,cAAc,GAAGD,aAAa,GAAG,CAAC,GAAGD,YAAY,GAAGC,aAAa,GAAG,CAAC;IAE3E,OAAO;MACLD,YAAY,EAAEtB,UAAU,CAACsB,YAAY,CAACrB,OAAO,CAAC,CAAC,CAAC,CAAC;MACjDsB,aAAa;MACbC,cAAc,EAAExB,UAAU,CAACwB,cAAc,CAACvB,OAAO,CAAC,CAAC,CAAC;IACtD,CAAC;EACH,CAAC;EAED,MAAMwB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,YAAY,GAAG,CAAC,CAAC;IAEvBpG,QAAQ,CAACqG,OAAO,CAACC,OAAO,IAAI;MAC1BA,OAAO,CAAC7F,QAAQ,CAAC4F,OAAO,CAAC7B,OAAO,IAAI;QAClC,IAAI,CAAC4B,YAAY,CAAC5B,OAAO,CAAC9D,IAAI,CAAC,EAAE;UAC/B0F,YAAY,CAAC5B,OAAO,CAAC9D,IAAI,CAAC,GAAG;YAC3BA,IAAI,EAAE8D,OAAO,CAAC9D,IAAI;YAClBuD,KAAK,EAAE,CAAC;YACRsC,OAAO,EAAE;UACX,CAAC;QACH;QACAH,YAAY,CAAC5B,OAAO,CAAC9D,IAAI,CAAC,CAACuD,KAAK,IAAI,CAAC;QACrCmC,YAAY,CAAC5B,OAAO,CAAC9D,IAAI,CAAC,CAAC6F,OAAO,IAAI/B,OAAO,CAAC7D,KAAK;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO6F,MAAM,CAACC,MAAM,CAACL,YAAY,CAAC,CAC/BM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC3C,KAAK,GAAG0C,CAAC,CAAC1C,KAAK,CAAC,CACjC4C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACjB,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,UAAU,GAAG,CAAC,CAAC;IAErB/G,QAAQ,CAACqG,OAAO,CAACC,OAAO,IAAI;MAC1BA,OAAO,CAAC7F,QAAQ,CAAC4F,OAAO,CAAC7B,OAAO,IAAI;QAClC,IAAI,CAACuC,UAAU,CAACvC,OAAO,CAAC5D,OAAO,CAAC,EAAE;UAChCmG,UAAU,CAACvC,OAAO,CAAC5D,OAAO,CAAC,GAAG;YAC5BF,IAAI,EAAE8D,OAAO,CAAC5D,OAAO;YACrBoG,YAAY,EAAE,CAAC;YACfT,OAAO,EAAE,CAAC;YACVU,cAAc,EAAE;UAClB,CAAC;QACH;QACAF,UAAU,CAACvC,OAAO,CAAC5D,OAAO,CAAC,CAACoG,YAAY,IAAI,CAAC;QAC7CD,UAAU,CAACvC,OAAO,CAAC5D,OAAO,CAAC,CAAC2F,OAAO,IAAI/B,OAAO,CAAC7D,KAAK;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF6F,MAAM,CAACC,MAAM,CAACM,UAAU,CAAC,CAACV,OAAO,CAACa,KAAK,IAAI;MACzCA,KAAK,CAACD,cAAc,GAAGC,KAAK,CAACF,YAAY,GAAG,CAAC,GAAGE,KAAK,CAACX,OAAO,GAAGW,KAAK,CAACF,YAAY,GAAG,CAAC;MACtFE,KAAK,CAACX,OAAO,GAAG7B,UAAU,CAACwC,KAAK,CAACX,OAAO,CAAC5B,OAAO,CAAC,CAAC,CAAC,CAAC;MACpDuC,KAAK,CAACD,cAAc,GAAGvC,UAAU,CAACwC,KAAK,CAACD,cAAc,CAACtC,OAAO,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IAEF,OAAO6B,MAAM,CAACC,MAAM,CAACM,UAAU,CAAC,CAACL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,OAAO,GAAGI,CAAC,CAACJ,OAAO,CAAC;EACxE,CAAC;;EAED;EACA,MAAMY,aAAa,GAAIC,WAAW,IAAK;IACrC,MAAMC,UAAU,GAAG;MACjB,GAAGD,WAAW;MACdlH,EAAE,EAAE2D,qBAAqB,CAAC,CAAC;MAC3BtD,IAAI,EAAE,IAAIwD,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CpF,OAAO,EAAE,IAAIuD,IAAI,CAACA,IAAI,CAACoB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACQ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnFvE,MAAM,EAAE,SAAS;MACjBE,aAAa,EAAE,SAAS;MACxBI,SAAS,EAAE,IAAIoC,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC;MACnC/D,SAAS,EAAE,IAAImC,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC;IACpC,CAAC;IAED,MAAM2B,MAAM,GAAGjD,qBAAqB,CAClCgD,UAAU,CAAC5G,QAAQ,EACnB4G,UAAU,CAACtG,YAAY,EACvBsG,UAAU,CAACrG,aAAa,EACxBqG,UAAU,CAACnG,OAAO,IAAI,GACxB,CAAC;IAEDsF,MAAM,CAACe,MAAM,CAACF,UAAU,EAAEC,MAAM,CAAC;IAEjCrH,WAAW,CAACuH,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,UAAU,CAAC,CAAC;IAC1C,OAAOA,UAAU;EACnB,CAAC;EAED,MAAMI,aAAa,GAAGA,CAACvH,EAAE,EAAEwH,OAAO,KAAK;IACrCzH,WAAW,CAACuH,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACrB,OAAO,IAClCA,OAAO,CAACpG,EAAE,KAAKA,EAAE,GACb;MAAE,GAAGoG,OAAO;MAAE,GAAGoB,OAAO;MAAE9F,SAAS,EAAE,IAAImC,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC;IAAE,CAAC,GAC/DW,OACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsB,aAAa,GAAI1H,EAAE,IAAK;IAC5BD,WAAW,CAACuH,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAACe,OAAO,IAAIA,OAAO,CAACpG,EAAE,KAAKA,EAAE,CAAC,CAAC;IAC9D4B,WAAW,CAAC0F,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAACsC,OAAO,IAAIA,OAAO,CAAC9F,SAAS,KAAK7B,EAAE,CAAC,CAAC;IACrEgD,eAAe,CAACsE,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAACuC,WAAW,IAAIA,WAAW,CAAC/F,SAAS,KAAK7B,EAAE,CAAC,CAAC;EACnF,CAAC;;EAED;EACA,MAAM6H,cAAc,GAAIC,WAAW,IAAK;IACtC,MAAMC,UAAU,GAAG;MACjB,GAAGD,WAAW;MACd9H,EAAE,EAAE,OAAO6D,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAE;MACvB5E,IAAI,EAAE,IAAIwD,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC;MAC9BtE,MAAM,EAAE,WAAW,CAAC;IACtB,CAAC;IAEDS,WAAW,CAAC0F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,UAAU,CAAC,CAAC;;IAE1C;IACAR,aAAa,CAACO,WAAW,CAACjG,SAAS,EAAE;MACnCV,MAAM,EAAE,MAAM;MACdE,aAAa,EAAE,WAAW;MAC1BC,WAAW,EAAEyG,UAAU,CAAC1H,IAAI;MAC5Be,aAAa,EAAE0G,WAAW,CAAC/F,MAAM;MACjCR,aAAa,EAAEwG,UAAU,CAACxG;IAC5B,CAAC,CAAC;;IAEF;IACA,MAAMqG,WAAW,GAAG;MAClB5H,EAAE,EAAE,OAAO6D,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAE;MACvBpD,SAAS,EAAEiG,WAAW,CAACjG,SAAS;MAChCS,IAAI,EAAE,SAAS;MACfR,MAAM,EAAEgG,WAAW,CAAChG,MAAM;MAC1BC,MAAM,EAAE+F,WAAW,CAAC/F,MAAM;MAC1BZ,MAAM,EAAE,WAAW;MACnBd,IAAI,EAAE0H,UAAU,CAAC1H,IAAI;MACrByC,WAAW,EAAE;IACf,CAAC;IAEDE,eAAe,CAACsE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,WAAW,CAAC,CAAC;IAE/C,OAAOG,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACvC,MAAMC,WAAW,GAAG;MAClB,GAAGD,YAAY;MACfjI,EAAE,EAAEmI,IAAI,CAACC,GAAG,CAAC,GAAGjG,SAAS,CAACsF,GAAG,CAAC5C,CAAC,IAAIA,CAAC,CAAC7E,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;MAChD6C,SAAS,EAAE,CAAC;MACZ1B,MAAM,EAAE;IACV,CAAC;IAEDiB,YAAY,CAACkF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEY,WAAW,CAAC,CAAC;IAC5C,OAAOA,WAAW;EACpB,CAAC;EAED,MAAMG,cAAc,GAAGA,CAACrI,EAAE,EAAEwH,OAAO,KAAK;IACtCpF,YAAY,CAACkF,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAC9C,QAAQ,IACpCA,QAAQ,CAAC3E,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAG2E,QAAQ;MAAE,GAAG6C;IAAQ,CAAC,GAAG7C,QACrD,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2D,cAAc,GAAItI,EAAE,IAAK;IAC7BoC,YAAY,CAACkF,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAACV,QAAQ,IAAIA,QAAQ,CAAC3E,EAAE,KAAKA,EAAE,CAAC,CAAC;EACnE,CAAC;EAED,MAAMuI,aAAa,GAAIlG,IAAI,IAAK;IAC9BD,YAAY,CAACkF,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAC9C,QAAQ,IACpCA,QAAQ,CAACtC,IAAI,CAACyC,WAAW,CAAC,CAAC,KAAKzC,IAAI,CAACyC,WAAW,CAAC,CAAC,GAC9C;MAAE,GAAGH,QAAQ;MAAE9B,SAAS,EAAE8B,QAAQ,CAAC9B,SAAS,GAAG;IAAE,CAAC,GAClD8B,QACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMpC,KAAK,GAAG;IACZ;IACAzC,QAAQ;IACR6B,QAAQ;IACRQ,SAAS;IACTY,YAAY;IAEZ;IACAY,qBAAqB;IACrBQ,qBAAqB;IACrBO,gBAAgB;IAChBQ,eAAe;IACfe,qBAAqB;IACrBW,mBAAmB;IAEnB;IACAK,aAAa;IACbM,aAAa;IACbG,aAAa;IACbG,cAAc;IACdG,cAAc;IACdK,cAAc;IACdC,cAAc;IACdC,aAAa;IAEb;IACAxI,WAAW;IACX6B,WAAW;IACXQ,YAAY;IACZY;EACF,CAAC;EAED,oBACE3D,OAAA,CAACC,cAAc,CAACkJ,QAAQ;IAACjG,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EACnCA;EAAQ;IAAA6I,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAAC/I,GAAA,CAzdWF,eAAe;AAAAkJ,EAAA,GAAflJ,eAAe;AAAA,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}