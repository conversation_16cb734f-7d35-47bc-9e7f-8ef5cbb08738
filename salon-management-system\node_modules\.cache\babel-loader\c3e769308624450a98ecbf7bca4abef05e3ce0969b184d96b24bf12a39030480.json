{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\BillingReports.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, Typography, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, LinearProgress, Chip, Tabs, Tab } from '@mui/material';\nimport { TrendingUp as TrendingUpIcon, AttachMoney as MoneyIcon, People as PeopleIcon, Schedule as ScheduleIcon, Star as StarIcon, Assessment as ReportIcon, GetApp as ExportIcon, DateRange as DateIcon } from '@mui/icons-material';\nimport { useBilling } from '../contexts/BillingContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BillingReports = () => {\n  _s();\n  const {\n    invoices,\n    getRevenueStats,\n    getMostBookedServices,\n    getStaffPerformance\n  } = useBilling();\n  const [currentTab, setCurrentTab] = useState(0);\n  const [reportPeriod, setReportPeriod] = useState('month');\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n\n  // Calculate various statistics\n  const revenueStats = getRevenueStats(reportPeriod);\n  const mostBookedServices = getMostBookedServices();\n  const staffPerformance = getStaffPerformance();\n\n  // Calculate peak hours/days\n  const getPeakAnalysis = () => {\n    const hourStats = {};\n    const dayStats = {};\n    invoices.forEach(invoice => {\n      const date = new Date(invoice.createdAt);\n      const hour = date.getHours();\n      const day = date.toLocaleDateString('en-US', {\n        weekday: 'long'\n      });\n      hourStats[hour] = (hourStats[hour] || 0) + 1;\n      dayStats[day] = (dayStats[day] || 0) + 1;\n    });\n    const peakHour = Object.entries(hourStats).reduce((a, b) => hourStats[a[0]] > hourStats[b[0]] ? a : b, [0, 0]);\n    const peakDay = Object.entries(dayStats).reduce((a, b) => dayStats[a[0]] > dayStats[b[0]] ? a : b, ['Monday', 0]);\n    return {\n      peakHour: `${peakHour[0]}:00`,\n      peakHourCount: peakHour[1],\n      peakDay: peakDay[0],\n      peakDayCount: peakDay[1],\n      hourStats,\n      dayStats\n    };\n  };\n  const peakAnalysis = getPeakAnalysis();\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n  const generateMonthlyReport = () => {\n    const monthlyData = {};\n    invoices.forEach(invoice => {\n      const date = new Date(invoice.date);\n      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;\n      if (!monthlyData[monthKey]) {\n        monthlyData[monthKey] = {\n          month: date.toLocaleDateString('en-US', {\n            month: 'long',\n            year: 'numeric'\n          }),\n          revenue: 0,\n          invoiceCount: 0,\n          averageInvoice: 0\n        };\n      }\n      if (invoice.status === 'paid') {\n        monthlyData[monthKey].revenue += invoice.total;\n      }\n      monthlyData[monthKey].invoiceCount += 1;\n    });\n    Object.values(monthlyData).forEach(month => {\n      month.averageInvoice = month.invoiceCount > 0 ? month.revenue / month.invoiceCount : 0;\n      month.revenue = parseFloat(month.revenue.toFixed(2));\n      month.averageInvoice = parseFloat(month.averageInvoice.toFixed(2));\n    });\n    return Object.values(monthlyData).sort((a, b) => new Date(a.month) - new Date(b.month));\n  };\n  const monthlyReport = generateMonthlyReport();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"Reports & Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            minWidth: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: reportPeriod,\n            onChange: e => setReportPeriod(e.target.value),\n            label: \"Period\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"day\",\n              children: \"Today\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"week\",\n              children: \"This Week\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"month\",\n              children: \"This Month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 24\n          }, this),\n          children: \"Export Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: [\"Revenue (\", reportPeriod, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: formatCurrency(revenueStats.totalRevenue)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: [revenueStats.totalInvoices, \" invoices\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'success.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Average Invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: formatCurrency(revenueStats.averageInvoice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"Per transaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MoneyIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Peak Day\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"info.main\",\n                  children: peakAnalysis.peakDay\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: [peakAnalysis.peakDayCount, \" bookings\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'info.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Peak Hour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"warning.main\",\n                  children: peakAnalysis.peakHour\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: [peakAnalysis.peakHourCount, \" bookings\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DateIcon, {\n                sx: {\n                  fontSize: 40,\n                  color: 'warning.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: currentTab,\n        onChange: (e, newValue) => setCurrentTab(newValue),\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Revenue Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Service Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Staff Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Peak Hours Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Monthly Revenue Trend\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Revenue\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Invoices\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Average Invoice\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Growth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: monthlyReport.map((month, index) => {\n                    const prevMonth = monthlyReport[index - 1];\n                    const growth = prevMonth ? ((month.revenue - prevMonth.revenue) / prevMonth.revenue * 100).toFixed(1) : 0;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: month.month\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          fontWeight: \"bold\",\n                          children: formatCurrency(month.revenue)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 290,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: month.invoiceCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: formatCurrency(month.averageInvoice)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: index > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                          label: `${growth > 0 ? '+' : ''}${growth}%`,\n                          color: growth > 0 ? 'success' : growth < 0 ? 'error' : 'default',\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 298,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 27\n                      }, this)]\n                    }, month.month, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Most Booked Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Bookings\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Avg. Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Popularity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: mostBookedServices.map((service, index) => {\n                var _mostBookedServices$;\n                const maxCount = ((_mostBookedServices$ = mostBookedServices[0]) === null || _mostBookedServices$ === void 0 ? void 0 : _mostBookedServices$.count) || 1;\n                const popularity = service.count / maxCount * 100;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        children: service.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 27\n                      }, this), index < 3 && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        sx: {\n                          ml: 1,\n                          color: 'gold',\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: service.count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(service.revenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(service.revenue / service.count)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: 200\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                        variant: \"determinate\",\n                        value: popularity,\n                        sx: {\n                          flex: 1,\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [popularity.toFixed(0), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this)]\n                }, service.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 2,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Staff Performance Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Staff Member\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Avg. Service Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Performance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: staffPerformance.map((staff, index) => {\n                var _staffPerformance$;\n                const maxRevenue = ((_staffPerformance$ = staffPerformance[0]) === null || _staffPerformance$ === void 0 ? void 0 : _staffPerformance$.revenue) || 1;\n                const performance = staff.revenue / maxRevenue * 100;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        fontWeight: \"bold\",\n                        children: staff.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 27\n                      }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                        label: \"Top Performer\",\n                        color: \"success\",\n                        size: \"small\",\n                        sx: {\n                          ml: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      children: staff.serviceCount\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: \"bold\",\n                      children: formatCurrency(staff.revenue)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(staff.averageService)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: 200\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                        variant: \"determinate\",\n                        value: performance,\n                        color: index === 0 ? 'success' : 'primary',\n                        sx: {\n                          flex: 1,\n                          mr: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 437,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [performance.toFixed(0), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(StarIcon, {\n                        sx: {\n                          fontSize: 16,\n                          color: i < Math.floor(performance / 20) ? 'gold' : 'grey.300'\n                        }\n                      }, i, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this)]\n                }, staff.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: currentTab,\n      index: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Peak Days Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Day\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Bookings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Activity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: Object.entries(peakAnalysis.dayStats).sort(([, a], [, b]) => b - a).map(([day, count]) => {\n                    const maxCount = Math.max(...Object.values(peakAnalysis.dayStats));\n                    const activity = count / maxCount * 100;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          children: day\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 497,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          fontWeight: \"bold\",\n                          children: count\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 502,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: 150\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                            variant: \"determinate\",\n                            value: activity,\n                            sx: {\n                              flex: 1,\n                              mr: 1\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 508,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            children: [activity.toFixed(0), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 513,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 507,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 29\n                      }, this)]\n                    }, day, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Peak Hours Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Hour\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Bookings\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Activity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: Object.entries(peakAnalysis.hourStats).sort(([, a], [, b]) => b - a).slice(0, 10).map(([hour, count]) => {\n                    const maxCount = Math.max(...Object.values(peakAnalysis.hourStats));\n                    const activity = count / maxCount * 100;\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          children: [hour, \":00\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 552,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        align: \"right\",\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle2\",\n                          fontWeight: \"bold\",\n                          children: count\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 557,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 556,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: 150\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                            variant: \"determinate\",\n                            value: activity,\n                            sx: {\n                              flex: 1,\n                              mr: 1\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 563,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            children: [activity.toFixed(0), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 568,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 562,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 29\n                      }, this)]\n                    }, hour, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(BillingReports, \"tTWUlDE/1li3EETQEZOTyijzT7w=\", false, function () {\n  return [useBilling];\n});\n_c = BillingReports;\nexport default BillingReports;\nvar _c;\n$RefreshReg$(_c, \"BillingReports\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "LinearProgress", "Chip", "Tabs", "Tab", "TrendingUp", "TrendingUpIcon", "AttachMoney", "MoneyIcon", "People", "PeopleIcon", "Schedule", "ScheduleIcon", "Star", "StarIcon", "Assessment", "ReportIcon", "GetApp", "ExportIcon", "DateRange", "DateIcon", "useBilling", "jsxDEV", "_jsxDEV", "BillingReports", "_s", "invoices", "getRevenueStats", "getMostBookedServices", "getStaffPerformance", "currentTab", "setCurrentTab", "reportPeriod", "setReportPeriod", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "Date", "getMonth", "selected<PERSON>ear", "setSelectedYear", "getFullYear", "revenueStats", "mostBookedServices", "staffPerformance", "getPeakAnalysis", "hourStats", "dayStats", "for<PERSON>ach", "invoice", "date", "createdAt", "hour", "getHours", "day", "toLocaleDateString", "weekday", "peakHour", "Object", "entries", "reduce", "a", "b", "peakDay", "peakHourCount", "peakDayCount", "peakAnalysis", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "TabPanel", "children", "value", "index", "hidden", "sx", "pt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "generateMonthlyReport", "monthlyData", "<PERSON><PERSON><PERSON>", "month", "year", "revenue", "invoiceCount", "averageInvoice", "status", "total", "values", "parseFloat", "toFixed", "sort", "monthlyReport", "p", "mb", "display", "justifyContent", "alignItems", "variant", "component", "fontWeight", "gap", "size", "min<PERSON><PERSON><PERSON>", "onChange", "e", "target", "label", "startIcon", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "totalRevenue", "totalInvoices", "fontSize", "newValue", "align", "map", "prevMonth", "growth", "service", "_mostBookedServices$", "maxCount", "count", "popularity", "name", "ml", "width", "flex", "mr", "staff", "_staffPerformance$", "max<PERSON><PERSON><PERSON><PERSON>", "performance", "serviceCount", "averageService", "Array", "_", "i", "Math", "floor", "max", "activity", "slice", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/BillingReports.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  LinearProgress,\n  Chip,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport {\n  TrendingUp as TrendingUpIcon,\n  AttachMoney as MoneyIcon,\n  People as PeopleIcon,\n  Schedule as ScheduleIcon,\n  Star as StarIcon,\n  Assessment as ReportIcon,\n  GetApp as ExportIcon,\n  DateRange as DateIcon\n} from '@mui/icons-material';\nimport { useBilling } from '../contexts/BillingContext';\n\nconst BillingReports = () => {\n  const {\n    invoices,\n    getRevenueStats,\n    getMostBookedServices,\n    getStaffPerformance\n  } = useBilling();\n\n  const [currentTab, setCurrentTab] = useState(0);\n  const [reportPeriod, setReportPeriod] = useState('month');\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());\n\n  // Calculate various statistics\n  const revenueStats = getRevenueStats(reportPeriod);\n  const mostBookedServices = getMostBookedServices();\n  const staffPerformance = getStaffPerformance();\n\n  // Calculate peak hours/days\n  const getPeakAnalysis = () => {\n    const hourStats = {};\n    const dayStats = {};\n    \n    invoices.forEach(invoice => {\n      const date = new Date(invoice.createdAt);\n      const hour = date.getHours();\n      const day = date.toLocaleDateString('en-US', { weekday: 'long' });\n      \n      hourStats[hour] = (hourStats[hour] || 0) + 1;\n      dayStats[day] = (dayStats[day] || 0) + 1;\n    });\n    \n    const peakHour = Object.entries(hourStats).reduce((a, b) => \n      hourStats[a[0]] > hourStats[b[0]] ? a : b, [0, 0]\n    );\n    \n    const peakDay = Object.entries(dayStats).reduce((a, b) => \n      dayStats[a[0]] > dayStats[b[0]] ? a : b, ['Monday', 0]\n    );\n    \n    return {\n      peakHour: `${peakHour[0]}:00`,\n      peakHourCount: peakHour[1],\n      peakDay: peakDay[0],\n      peakDayCount: peakDay[1],\n      hourStats,\n      dayStats\n    };\n  };\n\n  const peakAnalysis = getPeakAnalysis();\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  const generateMonthlyReport = () => {\n    const monthlyData = {};\n    \n    invoices.forEach(invoice => {\n      const date = new Date(invoice.date);\n      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;\n      \n      if (!monthlyData[monthKey]) {\n        monthlyData[monthKey] = {\n          month: date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),\n          revenue: 0,\n          invoiceCount: 0,\n          averageInvoice: 0\n        };\n      }\n      \n      if (invoice.status === 'paid') {\n        monthlyData[monthKey].revenue += invoice.total;\n      }\n      monthlyData[monthKey].invoiceCount += 1;\n    });\n    \n    Object.values(monthlyData).forEach(month => {\n      month.averageInvoice = month.invoiceCount > 0 ? month.revenue / month.invoiceCount : 0;\n      month.revenue = parseFloat(month.revenue.toFixed(2));\n      month.averageInvoice = parseFloat(month.averageInvoice.toFixed(2));\n    });\n    \n    return Object.values(monthlyData).sort((a, b) => new Date(a.month) - new Date(b.month));\n  };\n\n  const monthlyReport = generateMonthlyReport();\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"h4\" component=\"h1\" sx={{ fontWeight: 'bold' }}>\n          Reports & Analytics\n        </Typography>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <FormControl size=\"small\" sx={{ minWidth: 120 }}>\n            <InputLabel>Period</InputLabel>\n            <Select\n              value={reportPeriod}\n              onChange={(e) => setReportPeriod(e.target.value)}\n              label=\"Period\"\n            >\n              <MenuItem value=\"day\">Today</MenuItem>\n              <MenuItem value=\"week\">This Week</MenuItem>\n              <MenuItem value=\"month\">This Month</MenuItem>\n            </Select>\n          </FormControl>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n          >\n            Export Report\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Key Metrics Cards */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Revenue ({reportPeriod})\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {formatCurrency(revenueStats.totalRevenue)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    {revenueStats.totalInvoices} invoices\n                  </Typography>\n                </Box>\n                <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Average Invoice\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {formatCurrency(revenueStats.averageInvoice)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Per transaction\n                  </Typography>\n                </Box>\n                <MoneyIcon sx={{ fontSize: 40, color: 'primary.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Peak Day\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"info.main\">\n                    {peakAnalysis.peakDay}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    {peakAnalysis.peakDayCount} bookings\n                  </Typography>\n                </Box>\n                <ScheduleIcon sx={{ fontSize: 40, color: 'info.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Peak Hour\n                  </Typography>\n                  <Typography variant=\"h4\" color=\"warning.main\">\n                    {peakAnalysis.peakHour}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    {peakAnalysis.peakHourCount} bookings\n                  </Typography>\n                </Box>\n                <DateIcon sx={{ fontSize: 40, color: 'warning.main' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Tabs */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>\n          <Tab label=\"Revenue Analysis\" />\n          <Tab label=\"Service Performance\" />\n          <Tab label=\"Staff Performance\" />\n          <Tab label=\"Peak Hours Analysis\" />\n        </Tabs>\n      </Paper>\n\n      {/* Revenue Analysis Tab */}\n      <TabPanel value={currentTab} index={0}>\n        <Grid container spacing={3}>\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Monthly Revenue Trend\n              </Typography>\n              <TableContainer>\n                <Table>\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Month</TableCell>\n                      <TableCell align=\"right\">Revenue</TableCell>\n                      <TableCell align=\"right\">Invoices</TableCell>\n                      <TableCell align=\"right\">Average Invoice</TableCell>\n                      <TableCell align=\"right\">Growth</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {monthlyReport.map((month, index) => {\n                      const prevMonth = monthlyReport[index - 1];\n                      const growth = prevMonth ? \n                        ((month.revenue - prevMonth.revenue) / prevMonth.revenue * 100).toFixed(1) : 0;\n                      \n                      return (\n                        <TableRow key={month.month}>\n                          <TableCell>{month.month}</TableCell>\n                          <TableCell align=\"right\">\n                            <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                              {formatCurrency(month.revenue)}\n                            </Typography>\n                          </TableCell>\n                          <TableCell align=\"right\">{month.invoiceCount}</TableCell>\n                          <TableCell align=\"right\">{formatCurrency(month.averageInvoice)}</TableCell>\n                          <TableCell align=\"right\">\n                            {index > 0 && (\n                              <Chip \n                                label={`${growth > 0 ? '+' : ''}${growth}%`}\n                                color={growth > 0 ? 'success' : growth < 0 ? 'error' : 'default'}\n                                size=\"small\"\n                              />\n                            )}\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </Paper>\n          </Grid>\n        </Grid>\n      </TabPanel>\n\n      {/* Service Performance Tab */}\n      <TabPanel value={currentTab} index={1}>\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Most Booked Services\n          </Typography>\n          <TableContainer>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Service</TableCell>\n                  <TableCell align=\"right\">Bookings</TableCell>\n                  <TableCell align=\"right\">Revenue</TableCell>\n                  <TableCell align=\"right\">Avg. Price</TableCell>\n                  <TableCell>Popularity</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {mostBookedServices.map((service, index) => {\n                  const maxCount = mostBookedServices[0]?.count || 1;\n                  const popularity = (service.count / maxCount) * 100;\n                  \n                  return (\n                    <TableRow key={service.name}>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle2\">\n                            {service.name}\n                          </Typography>\n                          {index < 3 && (\n                            <StarIcon sx={{ ml: 1, color: 'gold', fontSize: 16 }} />\n                          )}\n                        </Box>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {service.count}\n                        </Typography>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(service.revenue)}\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(service.revenue / service.count)}\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: 200 }}>\n                          <LinearProgress\n                            variant=\"determinate\"\n                            value={popularity}\n                            sx={{ flex: 1, mr: 1 }}\n                          />\n                          <Typography variant=\"body2\">\n                            {popularity.toFixed(0)}%\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      </TabPanel>\n\n      {/* Staff Performance Tab */}\n      <TabPanel value={currentTab} index={2}>\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Staff Performance Analysis\n          </Typography>\n          <TableContainer>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Staff Member</TableCell>\n                  <TableCell align=\"right\">Services</TableCell>\n                  <TableCell align=\"right\">Revenue</TableCell>\n                  <TableCell align=\"right\">Avg. Service Value</TableCell>\n                  <TableCell>Performance</TableCell>\n                  <TableCell>Rating</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {staffPerformance.map((staff, index) => {\n                  const maxRevenue = staffPerformance[0]?.revenue || 1;\n                  const performance = (staff.revenue / maxRevenue) * 100;\n\n                  return (\n                    <TableRow key={staff.name}>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                            {staff.name}\n                          </Typography>\n                          {index === 0 && (\n                            <Chip\n                              label=\"Top Performer\"\n                              color=\"success\"\n                              size=\"small\"\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <Typography variant=\"subtitle2\">\n                          {staff.serviceCount}\n                        </Typography>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                          {formatCurrency(staff.revenue)}\n                        </Typography>\n                      </TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(staff.averageService)}\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: 200 }}>\n                          <LinearProgress\n                            variant=\"determinate\"\n                            value={performance}\n                            color={index === 0 ? 'success' : 'primary'}\n                            sx={{ flex: 1, mr: 1 }}\n                          />\n                          <Typography variant=\"body2\">\n                            {performance.toFixed(0)}%\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          {[...Array(5)].map((_, i) => (\n                            <StarIcon\n                              key={i}\n                              sx={{\n                                fontSize: 16,\n                                color: i < Math.floor(performance / 20) ? 'gold' : 'grey.300'\n                              }}\n                            />\n                          ))}\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  );\n                })}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      </TabPanel>\n\n      {/* Peak Hours Analysis Tab */}\n      <TabPanel value={currentTab} index={3}>\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Peak Days Analysis\n              </Typography>\n              <TableContainer>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Day</TableCell>\n                      <TableCell align=\"right\">Bookings</TableCell>\n                      <TableCell>Activity</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {Object.entries(peakAnalysis.dayStats)\n                      .sort(([,a], [,b]) => b - a)\n                      .map(([day, count]) => {\n                        const maxCount = Math.max(...Object.values(peakAnalysis.dayStats));\n                        const activity = (count / maxCount) * 100;\n\n                        return (\n                          <TableRow key={day}>\n                            <TableCell>\n                              <Typography variant=\"subtitle2\">\n                                {day}\n                              </Typography>\n                            </TableCell>\n                            <TableCell align=\"right\">\n                              <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                                {count}\n                              </Typography>\n                            </TableCell>\n                            <TableCell>\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: 150 }}>\n                                <LinearProgress\n                                  variant=\"determinate\"\n                                  value={activity}\n                                  sx={{ flex: 1, mr: 1 }}\n                                />\n                                <Typography variant=\"body2\">\n                                  {activity.toFixed(0)}%\n                                </Typography>\n                              </Box>\n                            </TableCell>\n                          </TableRow>\n                        );\n                      })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Peak Hours Analysis\n              </Typography>\n              <TableContainer>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Hour</TableCell>\n                      <TableCell align=\"right\">Bookings</TableCell>\n                      <TableCell>Activity</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {Object.entries(peakAnalysis.hourStats)\n                      .sort(([,a], [,b]) => b - a)\n                      .slice(0, 10)\n                      .map(([hour, count]) => {\n                        const maxCount = Math.max(...Object.values(peakAnalysis.hourStats));\n                        const activity = (count / maxCount) * 100;\n\n                        return (\n                          <TableRow key={hour}>\n                            <TableCell>\n                              <Typography variant=\"subtitle2\">\n                                {hour}:00\n                              </Typography>\n                            </TableCell>\n                            <TableCell align=\"right\">\n                              <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                                {count}\n                              </Typography>\n                            </TableCell>\n                            <TableCell>\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: 150 }}>\n                                <LinearProgress\n                                  variant=\"determinate\"\n                                  value={activity}\n                                  sx={{ flex: 1, mr: 1 }}\n                                />\n                                <Typography variant=\"body2\">\n                                  {activity.toFixed(0)}%\n                                </Typography>\n                              </Box>\n                            </TableCell>\n                          </TableRow>\n                        );\n                      })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </Paper>\n          </Grid>\n        </Grid>\n      </TabPanel>\n    </Box>\n  );\n};\n\nexport default BillingReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,SAAS,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,QAAQ,QAChB,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IACJC,QAAQ;IACRC,eAAe;IACfC,qBAAqB;IACrBC;EACF,CAAC,GAAGR,UAAU,CAAC,CAAC;EAEhB,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,IAAIqD,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAIqD,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMC,YAAY,GAAGd,eAAe,CAACK,YAAY,CAAC;EAClD,MAAMU,kBAAkB,GAAGd,qBAAqB,CAAC,CAAC;EAClD,MAAMe,gBAAgB,GAAGd,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,QAAQ,GAAG,CAAC,CAAC;IAEnBpB,QAAQ,CAACqB,OAAO,CAACC,OAAO,IAAI;MAC1B,MAAMC,IAAI,GAAG,IAAIb,IAAI,CAACY,OAAO,CAACE,SAAS,CAAC;MACxC,MAAMC,IAAI,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC5B,MAAMC,GAAG,GAAGJ,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAC,CAAC;MAEjEV,SAAS,CAACM,IAAI,CAAC,GAAG,CAACN,SAAS,CAACM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAC5CL,QAAQ,CAACO,GAAG,CAAC,GAAG,CAACP,QAAQ,CAACO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAMG,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAACb,SAAS,CAAC,CAACc,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KACrDhB,SAAS,CAACe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGf,SAAS,CAACgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAClD,CAAC;IAED,MAAMC,OAAO,GAAGL,MAAM,CAACC,OAAO,CAACZ,QAAQ,CAAC,CAACa,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KACnDf,QAAQ,CAACc,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGd,QAAQ,CAACe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CACvD,CAAC;IAED,OAAO;MACLL,QAAQ,EAAE,GAAGA,QAAQ,CAAC,CAAC,CAAC,KAAK;MAC7BO,aAAa,EAAEP,QAAQ,CAAC,CAAC,CAAC;MAC1BM,OAAO,EAAEA,OAAO,CAAC,CAAC,CAAC;MACnBE,YAAY,EAAEF,OAAO,CAAC,CAAC,CAAC;MACxBjB,SAAS;MACTC;IACF,CAAC;EACH,CAAC;EAED,MAAMmB,YAAY,GAAGrB,eAAe,CAAC,CAAC;EAEtC,MAAMsB,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CrD,OAAA;IAAKsD,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIrD,OAAA,CAACvC,GAAG;MAAC8F,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,WAAW,GAAG,CAAC,CAAC;IAEtB3D,QAAQ,CAACqB,OAAO,CAACC,OAAO,IAAI;MAC1B,MAAMC,IAAI,GAAG,IAAIb,IAAI,CAACY,OAAO,CAACC,IAAI,CAAC;MACnC,MAAMqC,QAAQ,GAAG,GAAGrC,IAAI,CAACT,WAAW,CAAC,CAAC,IAAIS,IAAI,CAACZ,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;MAE/D,IAAI,CAACgD,WAAW,CAACC,QAAQ,CAAC,EAAE;QAC1BD,WAAW,CAACC,QAAQ,CAAC,GAAG;UACtBC,KAAK,EAAEtC,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;YAAEiC,KAAK,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAC;UAC3EC,OAAO,EAAE,CAAC;UACVC,YAAY,EAAE,CAAC;UACfC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,IAAI3C,OAAO,CAAC4C,MAAM,KAAK,MAAM,EAAE;QAC7BP,WAAW,CAACC,QAAQ,CAAC,CAACG,OAAO,IAAIzC,OAAO,CAAC6C,KAAK;MAChD;MACAR,WAAW,CAACC,QAAQ,CAAC,CAACI,YAAY,IAAI,CAAC;IACzC,CAAC,CAAC;IAEFjC,MAAM,CAACqC,MAAM,CAACT,WAAW,CAAC,CAACtC,OAAO,CAACwC,KAAK,IAAI;MAC1CA,KAAK,CAACI,cAAc,GAAGJ,KAAK,CAACG,YAAY,GAAG,CAAC,GAAGH,KAAK,CAACE,OAAO,GAAGF,KAAK,CAACG,YAAY,GAAG,CAAC;MACtFH,KAAK,CAACE,OAAO,GAAGM,UAAU,CAACR,KAAK,CAACE,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;MACpDT,KAAK,CAACI,cAAc,GAAGI,UAAU,CAACR,KAAK,CAACI,cAAc,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IAEF,OAAOvC,MAAM,CAACqC,MAAM,CAACT,WAAW,CAAC,CAACY,IAAI,CAAC,CAACrC,CAAC,EAAEC,CAAC,KAAK,IAAIzB,IAAI,CAACwB,CAAC,CAAC2B,KAAK,CAAC,GAAG,IAAInD,IAAI,CAACyB,CAAC,CAAC0B,KAAK,CAAC,CAAC;EACzF,CAAC;EAED,MAAMW,aAAa,GAAGd,qBAAqB,CAAC,CAAC;EAE7C,oBACE7D,OAAA,CAACvC,GAAG;IAAC8F,EAAE,EAAE;MAAEqB,CAAC,EAAE;IAAE,CAAE;IAAAzB,QAAA,gBAEhBnD,OAAA,CAACvC,GAAG;MAAC8F,EAAE,EAAE;QAAEsB,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA7B,QAAA,gBACzFnD,OAAA,CAACrC,UAAU;QAACsH,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAAC3B,EAAE,EAAE;UAAE4B,UAAU,EAAE;QAAO,CAAE;QAAAhC,QAAA,EAAC;MAEpE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5D,OAAA,CAACvC,GAAG;QAAC8F,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEM,GAAG,EAAE;QAAE,CAAE;QAAAjC,QAAA,gBACnCnD,OAAA,CAACjC,WAAW;UAACsH,IAAI,EAAC,OAAO;UAAC9B,EAAE,EAAE;YAAE+B,QAAQ,EAAE;UAAI,CAAE;UAAAnC,QAAA,gBAC9CnD,OAAA,CAAChC,UAAU;YAAAmF,QAAA,EAAC;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/B5D,OAAA,CAAC/B,MAAM;YACLmF,KAAK,EAAE3C,YAAa;YACpB8E,QAAQ,EAAGC,CAAC,IAAK9E,eAAe,CAAC8E,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;YACjDsC,KAAK,EAAC,QAAQ;YAAAvC,QAAA,gBAEdnD,OAAA,CAAC9B,QAAQ;cAACkF,KAAK,EAAC,KAAK;cAAAD,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtC5D,OAAA,CAAC9B,QAAQ;cAACkF,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3C5D,OAAA,CAAC9B,QAAQ;cAACkF,KAAK,EAAC,OAAO;cAAAD,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACd5D,OAAA,CAAC7B,MAAM;UACL8G,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAE3F,OAAA,CAACL,UAAU;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,EAC3B;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA,CAACpC,IAAI;MAACgI,SAAS;MAACC,OAAO,EAAE,CAAE;MAACtC,EAAE,EAAE;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,gBACxCnD,OAAA,CAACpC,IAAI;QAACkI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9C,QAAA,eAC9BnD,OAAA,CAACnC,IAAI;UAAAsF,QAAA,eACHnD,OAAA,CAAClC,WAAW;YAAAqF,QAAA,eACVnD,OAAA,CAACvC,GAAG;cAAC8F,EAAE,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAA5B,QAAA,gBAClFnD,OAAA,CAACvC,GAAG;gBAAA0F,QAAA,gBACFnD,OAAA,CAACrC,UAAU;kBAACuI,KAAK,EAAC,eAAe;kBAACC,YAAY;kBAAAhD,QAAA,GAAC,WACpC,EAAC1C,YAAY,EAAC,GACzB;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,IAAI;kBAACiB,KAAK,EAAC,cAAc;kBAAA/C,QAAA,EAC1CR,cAAc,CAACzB,YAAY,CAACkF,YAAY;gBAAC;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,OAAO;kBAACiB,KAAK,EAAC,eAAe;kBAAA/C,QAAA,GAC9CjC,YAAY,CAACmF,aAAa,EAAC,WAC9B;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5D,OAAA,CAACjB,cAAc;gBAACwE,EAAE,EAAE;kBAAE+C,QAAQ,EAAE,EAAE;kBAAEJ,KAAK,EAAE;gBAAe;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5D,OAAA,CAACpC,IAAI;QAACkI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9C,QAAA,eAC9BnD,OAAA,CAACnC,IAAI;UAAAsF,QAAA,eACHnD,OAAA,CAAClC,WAAW;YAAAqF,QAAA,eACVnD,OAAA,CAACvC,GAAG;cAAC8F,EAAE,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAA5B,QAAA,gBAClFnD,OAAA,CAACvC,GAAG;gBAAA0F,QAAA,gBACFnD,OAAA,CAACrC,UAAU;kBAACuI,KAAK,EAAC,eAAe;kBAACC,YAAY;kBAAAhD,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,IAAI;kBAACiB,KAAK,EAAC,cAAc;kBAAA/C,QAAA,EAC1CR,cAAc,CAACzB,YAAY,CAACkD,cAAc;gBAAC;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,OAAO;kBAACiB,KAAK,EAAC,eAAe;kBAAA/C,QAAA,EAAC;gBAElD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5D,OAAA,CAACf,SAAS;gBAACsE,EAAE,EAAE;kBAAE+C,QAAQ,EAAE,EAAE;kBAAEJ,KAAK,EAAE;gBAAe;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5D,OAAA,CAACpC,IAAI;QAACkI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9C,QAAA,eAC9BnD,OAAA,CAACnC,IAAI;UAAAsF,QAAA,eACHnD,OAAA,CAAClC,WAAW;YAAAqF,QAAA,eACVnD,OAAA,CAACvC,GAAG;cAAC8F,EAAE,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAA5B,QAAA,gBAClFnD,OAAA,CAACvC,GAAG;gBAAA0F,QAAA,gBACFnD,OAAA,CAACrC,UAAU;kBAACuI,KAAK,EAAC,eAAe;kBAACC,YAAY;kBAAAhD,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,IAAI;kBAACiB,KAAK,EAAC,WAAW;kBAAA/C,QAAA,EACvCT,YAAY,CAACH;gBAAO;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,OAAO;kBAACiB,KAAK,EAAC,eAAe;kBAAA/C,QAAA,GAC9CT,YAAY,CAACD,YAAY,EAAC,WAC7B;gBAAA;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5D,OAAA,CAACX,YAAY;gBAACkE,EAAE,EAAE;kBAAE+C,QAAQ,EAAE,EAAE;kBAAEJ,KAAK,EAAE;gBAAY;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5D,OAAA,CAACpC,IAAI;QAACkI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9C,QAAA,eAC9BnD,OAAA,CAACnC,IAAI;UAAAsF,QAAA,eACHnD,OAAA,CAAClC,WAAW;YAAAqF,QAAA,eACVnD,OAAA,CAACvC,GAAG;cAAC8F,EAAE,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAA5B,QAAA,gBAClFnD,OAAA,CAACvC,GAAG;gBAAA0F,QAAA,gBACFnD,OAAA,CAACrC,UAAU;kBAACuI,KAAK,EAAC,eAAe;kBAACC,YAAY;kBAAAhD,QAAA,EAAC;gBAE/C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,IAAI;kBAACiB,KAAK,EAAC,cAAc;kBAAA/C,QAAA,EAC1CT,YAAY,CAACT;gBAAQ;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACb5D,OAAA,CAACrC,UAAU;kBAACsH,OAAO,EAAC,OAAO;kBAACiB,KAAK,EAAC,eAAe;kBAAA/C,QAAA,GAC9CT,YAAY,CAACF,aAAa,EAAC,WAC9B;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5D,OAAA,CAACH,QAAQ;gBAAC0D,EAAE,EAAE;kBAAE+C,QAAQ,EAAE,EAAE;kBAAEJ,KAAK,EAAE;gBAAe;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5D,OAAA,CAACtC,KAAK;MAAC6F,EAAE,EAAE;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,eACnBnD,OAAA,CAACpB,IAAI;QAACwE,KAAK,EAAE7C,UAAW;QAACgF,QAAQ,EAAEA,CAACC,CAAC,EAAEe,QAAQ,KAAK/F,aAAa,CAAC+F,QAAQ,CAAE;QAAApD,QAAA,gBAC1EnD,OAAA,CAACnB,GAAG;UAAC6G,KAAK,EAAC;QAAkB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChC5D,OAAA,CAACnB,GAAG;UAAC6G,KAAK,EAAC;QAAqB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnC5D,OAAA,CAACnB,GAAG;UAAC6G,KAAK,EAAC;QAAmB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjC5D,OAAA,CAACnB,GAAG;UAAC6G,KAAK,EAAC;QAAqB;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR5D,OAAA,CAACkD,QAAQ;MAACE,KAAK,EAAE7C,UAAW;MAAC8C,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCnD,OAAA,CAACpC,IAAI;QAACgI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1C,QAAA,eACzBnD,OAAA,CAACpC,IAAI;UAACkI,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA5C,QAAA,eAChBnD,OAAA,CAACtC,KAAK;YAAC6F,EAAE,EAAE;cAAEqB,CAAC,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAClBnD,OAAA,CAACrC,UAAU;cAACsH,OAAO,EAAC,IAAI;cAACkB,YAAY;cAAAhD,QAAA,EAAC;YAEtC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5D,OAAA,CAACzB,cAAc;cAAA4E,QAAA,eACbnD,OAAA,CAAC5B,KAAK;gBAAA+E,QAAA,gBACJnD,OAAA,CAACxB,SAAS;kBAAA2E,QAAA,eACRnD,OAAA,CAACvB,QAAQ;oBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;sBAAA6E,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5B5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5C5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7C5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAe;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACpD5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ5D,OAAA,CAAC3B,SAAS;kBAAA8E,QAAA,EACPwB,aAAa,CAAC8B,GAAG,CAAC,CAACzC,KAAK,EAAEX,KAAK,KAAK;oBACnC,MAAMqD,SAAS,GAAG/B,aAAa,CAACtB,KAAK,GAAG,CAAC,CAAC;oBAC1C,MAAMsD,MAAM,GAAGD,SAAS,GACtB,CAAC,CAAC1C,KAAK,CAACE,OAAO,GAAGwC,SAAS,CAACxC,OAAO,IAAIwC,SAAS,CAACxC,OAAO,GAAG,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;oBAEhF,oBACEzE,OAAA,CAACvB,QAAQ;sBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;wBAAA6E,QAAA,EAAEa,KAAK,CAACA;sBAAK;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACpC5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;0BAACsH,OAAO,EAAC,WAAW;0BAACE,UAAU,EAAC,MAAM;0BAAAhC,QAAA,EAC9CR,cAAc,CAACqB,KAAK,CAACE,OAAO;wBAAC;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,EAAEa,KAAK,CAACG;sBAAY;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACzD5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,EAAER,cAAc,CAACqB,KAAK,CAACI,cAAc;sBAAC;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC3E5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,EACrBE,KAAK,GAAG,CAAC,iBACRrD,OAAA,CAACrB,IAAI;0BACH+G,KAAK,EAAE,GAAGiB,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,MAAM,GAAI;0BAC5CT,KAAK,EAAES,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGA,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,SAAU;0BACjEtB,IAAI,EAAC;wBAAO;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBACF;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA,GAjBCI,KAAK,CAACA,KAAK;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkBhB,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGX5D,OAAA,CAACkD,QAAQ;MAACE,KAAK,EAAE7C,UAAW;MAAC8C,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCnD,OAAA,CAACtC,KAAK;QAAC6F,EAAE,EAAE;UAAEqB,CAAC,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAClBnD,OAAA,CAACrC,UAAU;UAACsH,OAAO,EAAC,IAAI;UAACkB,YAAY;UAAAhD,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5D,OAAA,CAACzB,cAAc;UAAA4E,QAAA,eACbnD,OAAA,CAAC5B,KAAK;YAAA+E,QAAA,gBACJnD,OAAA,CAACxB,SAAS;cAAA2E,QAAA,eACRnD,OAAA,CAACvB,QAAQ;gBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;kBAAA6E,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7C5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5C5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/C5D,OAAA,CAAC1B,SAAS;kBAAA6E,QAAA,EAAC;gBAAU;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ5D,OAAA,CAAC3B,SAAS;cAAA8E,QAAA,EACPhC,kBAAkB,CAACsF,GAAG,CAAC,CAACG,OAAO,EAAEvD,KAAK,KAAK;gBAAA,IAAAwD,oBAAA;gBAC1C,MAAMC,QAAQ,GAAG,EAAAD,oBAAA,GAAA1F,kBAAkB,CAAC,CAAC,CAAC,cAAA0F,oBAAA,uBAArBA,oBAAA,CAAuBE,KAAK,KAAI,CAAC;gBAClD,MAAMC,UAAU,GAAIJ,OAAO,CAACG,KAAK,GAAGD,QAAQ,GAAI,GAAG;gBAEnD,oBACE9G,OAAA,CAACvB,QAAQ;kBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;oBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;sBAAC8F,EAAE,EAAE;wBAAEuB,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACjDnD,OAAA,CAACrC,UAAU;wBAACsH,OAAO,EAAC,WAAW;wBAAA9B,QAAA,EAC5ByD,OAAO,CAACK;sBAAI;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EACZP,KAAK,GAAG,CAAC,iBACRrD,OAAA,CAACT,QAAQ;wBAACgE,EAAE,EAAE;0BAAE2D,EAAE,EAAE,CAAC;0BAAEhB,KAAK,EAAE,MAAM;0BAAEI,QAAQ,EAAE;wBAAG;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CACxD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;sBAACsH,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAhC,QAAA,EAC9CyD,OAAO,CAACG;oBAAK;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,EACrBR,cAAc,CAACiE,OAAO,CAAC1C,OAAO;kBAAC;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,EACrBR,cAAc,CAACiE,OAAO,CAAC1C,OAAO,GAAG0C,OAAO,CAACG,KAAK;kBAAC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;sBAAC8F,EAAE,EAAE;wBAAEuB,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEmC,KAAK,EAAE;sBAAI,CAAE;sBAAAhE,QAAA,gBAC7DnD,OAAA,CAACtB,cAAc;wBACbuG,OAAO,EAAC,aAAa;wBACrB7B,KAAK,EAAE4D,UAAW;wBAClBzD,EAAE,EAAE;0BAAE6D,IAAI,EAAE,CAAC;0BAAEC,EAAE,EAAE;wBAAE;sBAAE;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACF5D,OAAA,CAACrC,UAAU;wBAACsH,OAAO,EAAC,OAAO;wBAAA9B,QAAA,GACxB6D,UAAU,CAACvC,OAAO,CAAC,CAAC,CAAC,EAAC,GACzB;sBAAA;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAjCCgD,OAAO,CAACK,IAAI;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkCjB,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX5D,OAAA,CAACkD,QAAQ;MAACE,KAAK,EAAE7C,UAAW;MAAC8C,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCnD,OAAA,CAACtC,KAAK;QAAC6F,EAAE,EAAE;UAAEqB,CAAC,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAClBnD,OAAA,CAACrC,UAAU;UAACsH,OAAO,EAAC,IAAI;UAACkB,YAAY;UAAAhD,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5D,OAAA,CAACzB,cAAc;UAAA4E,QAAA,eACbnD,OAAA,CAAC5B,KAAK;YAAA+E,QAAA,gBACJnD,OAAA,CAACxB,SAAS;cAAA2E,QAAA,eACRnD,OAAA,CAACvB,QAAQ;gBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;kBAAA6E,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnC5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7C5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5C5D,OAAA,CAAC1B,SAAS;kBAACkI,KAAK,EAAC,OAAO;kBAAArD,QAAA,EAAC;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACvD5D,OAAA,CAAC1B,SAAS;kBAAA6E,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClC5D,OAAA,CAAC1B,SAAS;kBAAA6E,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ5D,OAAA,CAAC3B,SAAS;cAAA8E,QAAA,EACP/B,gBAAgB,CAACqF,GAAG,CAAC,CAACa,KAAK,EAAEjE,KAAK,KAAK;gBAAA,IAAAkE,kBAAA;gBACtC,MAAMC,UAAU,GAAG,EAAAD,kBAAA,GAAAnG,gBAAgB,CAAC,CAAC,CAAC,cAAAmG,kBAAA,uBAAnBA,kBAAA,CAAqBrD,OAAO,KAAI,CAAC;gBACpD,MAAMuD,WAAW,GAAIH,KAAK,CAACpD,OAAO,GAAGsD,UAAU,GAAI,GAAG;gBAEtD,oBACExH,OAAA,CAACvB,QAAQ;kBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;oBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;sBAAC8F,EAAE,EAAE;wBAAEuB,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACjDnD,OAAA,CAACrC,UAAU;wBAACsH,OAAO,EAAC,WAAW;wBAACE,UAAU,EAAC,MAAM;wBAAAhC,QAAA,EAC9CmE,KAAK,CAACL;sBAAI;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,EACZP,KAAK,KAAK,CAAC,iBACVrD,OAAA,CAACrB,IAAI;wBACH+G,KAAK,EAAC,eAAe;wBACrBQ,KAAK,EAAC,SAAS;wBACfb,IAAI,EAAC,OAAO;wBACZ9B,EAAE,EAAE;0BAAE2D,EAAE,EAAE;wBAAE;sBAAE;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;sBAACsH,OAAO,EAAC,WAAW;sBAAA9B,QAAA,EAC5BmE,KAAK,CAACI;oBAAY;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;sBAACsH,OAAO,EAAC,WAAW;sBAACE,UAAU,EAAC,MAAM;sBAAAhC,QAAA,EAC9CR,cAAc,CAAC2E,KAAK,CAACpD,OAAO;oBAAC;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAACkI,KAAK,EAAC,OAAO;oBAAArD,QAAA,EACrBR,cAAc,CAAC2E,KAAK,CAACK,cAAc;kBAAC;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;sBAAC8F,EAAE,EAAE;wBAAEuB,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAEmC,KAAK,EAAE;sBAAI,CAAE;sBAAAhE,QAAA,gBAC7DnD,OAAA,CAACtB,cAAc;wBACbuG,OAAO,EAAC,aAAa;wBACrB7B,KAAK,EAAEqE,WAAY;wBACnBvB,KAAK,EAAE7C,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU;wBAC3CE,EAAE,EAAE;0BAAE6D,IAAI,EAAE,CAAC;0BAAEC,EAAE,EAAE;wBAAE;sBAAE;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACF5D,OAAA,CAACrC,UAAU;wBAACsH,OAAO,EAAC,OAAO;wBAAA9B,QAAA,GACxBsE,WAAW,CAAChD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1B;sBAAA;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;oBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;sBAAC8F,EAAE,EAAE;wBAAEuB,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,EAChD,CAAC,GAAGyE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACnB,GAAG,CAAC,CAACoB,CAAC,EAAEC,CAAC,kBACtB9H,OAAA,CAACT,QAAQ;wBAEPgE,EAAE,EAAE;0BACF+C,QAAQ,EAAE,EAAE;0BACZJ,KAAK,EAAE4B,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACP,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,GAAG;wBACrD;sBAAE,GAJGK,CAAC;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAKP,CACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAtDC0D,KAAK,CAACL,IAAI;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuDf,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX5D,OAAA,CAACkD,QAAQ;MAACE,KAAK,EAAE7C,UAAW;MAAC8C,KAAK,EAAE,CAAE;MAAAF,QAAA,eACpCnD,OAAA,CAACpC,IAAI;QAACgI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1C,QAAA,gBACzBnD,OAAA,CAACpC,IAAI;UAACkI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA9C,QAAA,eACvBnD,OAAA,CAACtC,KAAK;YAAC6F,EAAE,EAAE;cAAEqB,CAAC,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAClBnD,OAAA,CAACrC,UAAU;cAACsH,OAAO,EAAC,IAAI;cAACkB,YAAY;cAAAhD,QAAA,EAAC;YAEtC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5D,OAAA,CAACzB,cAAc;cAAA4E,QAAA,eACbnD,OAAA,CAAC5B,KAAK;gBAACiH,IAAI,EAAC,OAAO;gBAAAlC,QAAA,gBACjBnD,OAAA,CAACxB,SAAS;kBAAA2E,QAAA,eACRnD,OAAA,CAACvB,QAAQ;oBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;sBAAA6E,QAAA,EAAC;oBAAG;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC1B5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7C5D,OAAA,CAAC1B,SAAS;sBAAA6E,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ5D,OAAA,CAAC3B,SAAS;kBAAA8E,QAAA,EACPjB,MAAM,CAACC,OAAO,CAACO,YAAY,CAACnB,QAAQ,CAAC,CACnCmD,IAAI,CAAC,CAAC,GAAErC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3BoE,GAAG,CAAC,CAAC,CAAC3E,GAAG,EAAEiF,KAAK,CAAC,KAAK;oBACrB,MAAMD,QAAQ,GAAGiB,IAAI,CAACE,GAAG,CAAC,GAAG/F,MAAM,CAACqC,MAAM,CAAC7B,YAAY,CAACnB,QAAQ,CAAC,CAAC;oBAClE,MAAM2G,QAAQ,GAAInB,KAAK,GAAGD,QAAQ,GAAI,GAAG;oBAEzC,oBACE9G,OAAA,CAACvB,QAAQ;sBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;wBAAA6E,QAAA,eACRnD,OAAA,CAACrC,UAAU;0BAACsH,OAAO,EAAC,WAAW;0BAAA9B,QAAA,EAC5BrB;wBAAG;0BAAA2B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;0BAACsH,OAAO,EAAC,WAAW;0BAACE,UAAU,EAAC,MAAM;0BAAAhC,QAAA,EAC9C4D;wBAAK;0BAAAtD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;wBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;0BAAC8F,EAAE,EAAE;4BAAEuB,OAAO,EAAE,MAAM;4BAAEE,UAAU,EAAE,QAAQ;4BAAEmC,KAAK,EAAE;0BAAI,CAAE;0BAAAhE,QAAA,gBAC7DnD,OAAA,CAACtB,cAAc;4BACbuG,OAAO,EAAC,aAAa;4BACrB7B,KAAK,EAAE8E,QAAS;4BAChB3E,EAAE,EAAE;8BAAE6D,IAAI,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;4BAAA5D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACF5D,OAAA,CAACrC,UAAU;4BAACsH,OAAO,EAAC,OAAO;4BAAA9B,QAAA,GACxB+E,QAAQ,CAACzD,OAAO,CAAC,CAAC,CAAC,EAAC,GACvB;0BAAA;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA,GAtBC9B,GAAG;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuBR,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP5D,OAAA,CAACpC,IAAI;UAACkI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA9C,QAAA,eACvBnD,OAAA,CAACtC,KAAK;YAAC6F,EAAE,EAAE;cAAEqB,CAAC,EAAE;YAAE,CAAE;YAAAzB,QAAA,gBAClBnD,OAAA,CAACrC,UAAU;cAACsH,OAAO,EAAC,IAAI;cAACkB,YAAY;cAAAhD,QAAA,EAAC;YAEtC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5D,OAAA,CAACzB,cAAc;cAAA4E,QAAA,eACbnD,OAAA,CAAC5B,KAAK;gBAACiH,IAAI,EAAC,OAAO;gBAAAlC,QAAA,gBACjBnD,OAAA,CAACxB,SAAS;kBAAA2E,QAAA,eACRnD,OAAA,CAACvB,QAAQ;oBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;sBAAA6E,QAAA,EAAC;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3B5D,OAAA,CAAC1B,SAAS;sBAACkI,KAAK,EAAC,OAAO;sBAAArD,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7C5D,OAAA,CAAC1B,SAAS;sBAAA6E,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ5D,OAAA,CAAC3B,SAAS;kBAAA8E,QAAA,EACPjB,MAAM,CAACC,OAAO,CAACO,YAAY,CAACpB,SAAS,CAAC,CACpCoD,IAAI,CAAC,CAAC,GAAErC,CAAC,CAAC,EAAE,GAAEC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAC3B8F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZ1B,GAAG,CAAC,CAAC,CAAC7E,IAAI,EAAEmF,KAAK,CAAC,KAAK;oBACtB,MAAMD,QAAQ,GAAGiB,IAAI,CAACE,GAAG,CAAC,GAAG/F,MAAM,CAACqC,MAAM,CAAC7B,YAAY,CAACpB,SAAS,CAAC,CAAC;oBACnE,MAAM4G,QAAQ,GAAInB,KAAK,GAAGD,QAAQ,GAAI,GAAG;oBAEzC,oBACE9G,OAAA,CAACvB,QAAQ;sBAAA0E,QAAA,gBACPnD,OAAA,CAAC1B,SAAS;wBAAA6E,QAAA,eACRnD,OAAA,CAACrC,UAAU;0BAACsH,OAAO,EAAC,WAAW;0BAAA9B,QAAA,GAC5BvB,IAAI,EAAC,KACR;wBAAA;0BAAA6B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;wBAACkI,KAAK,EAAC,OAAO;wBAAArD,QAAA,eACtBnD,OAAA,CAACrC,UAAU;0BAACsH,OAAO,EAAC,WAAW;0BAACE,UAAU,EAAC,MAAM;0BAAAhC,QAAA,EAC9C4D;wBAAK;0BAAAtD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACZ5D,OAAA,CAAC1B,SAAS;wBAAA6E,QAAA,eACRnD,OAAA,CAACvC,GAAG;0BAAC8F,EAAE,EAAE;4BAAEuB,OAAO,EAAE,MAAM;4BAAEE,UAAU,EAAE,QAAQ;4BAAEmC,KAAK,EAAE;0BAAI,CAAE;0BAAAhE,QAAA,gBAC7DnD,OAAA,CAACtB,cAAc;4BACbuG,OAAO,EAAC,aAAa;4BACrB7B,KAAK,EAAE8E,QAAS;4BAChB3E,EAAE,EAAE;8BAAE6D,IAAI,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;4BAAA5D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACF5D,OAAA,CAACrC,UAAU;4BAACsH,OAAO,EAAC,OAAO;4BAAA9B,QAAA,GACxB+E,QAAQ,CAACzD,OAAO,CAAC,CAAC,CAAC,EAAC,GACvB;0BAAA;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA,GAtBChC,IAAI;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuBT,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC1D,EAAA,CApiBID,cAAc;EAAA,QAMdH,UAAU;AAAA;AAAAsI,EAAA,GANVnI,cAAc;AAsiBpB,eAAeA,cAAc;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}