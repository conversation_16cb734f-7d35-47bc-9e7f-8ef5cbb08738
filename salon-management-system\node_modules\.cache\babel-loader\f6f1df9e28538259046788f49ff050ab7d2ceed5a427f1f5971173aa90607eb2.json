{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\ProductForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Box, Typography, Alert, InputAdornment } from '@mui/material';\nimport { useInventory } from '../contexts/InventoryContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductForm = ({\n  open,\n  onClose,\n  product = null,\n  mode = 'add'\n}) => {\n  _s();\n  const {\n    addProduct,\n    updateProduct,\n    getCategories,\n    getSuppliers\n  } = useInventory();\n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    brand: '',\n    sku: '',\n    currentStock: 0,\n    minStockLevel: 0,\n    maxStockLevel: 0,\n    unitPrice: 0,\n    supplier: '',\n    description: '',\n    expiryDate: '',\n    location: '',\n    barcode: '',\n    usageRate: 0,\n    status: 'active'\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const categories = getCategories();\n  const suppliers = getSuppliers();\n\n  // Predefined categories for new products\n  const predefinedCategories = ['Hair Care', 'Hair Color', 'Styling Products', 'Nail Care', 'Skincare', 'Tools & Equipment', 'Cleaning Supplies', 'Other'];\n  const allCategories = [...new Set([...categories, ...predefinedCategories])];\n  useEffect(() => {\n    if (product && mode === 'edit') {\n      setFormData({\n        name: product.name || '',\n        category: product.category || '',\n        brand: product.brand || '',\n        sku: product.sku || '',\n        currentStock: product.currentStock || 0,\n        minStockLevel: product.minStockLevel || 0,\n        maxStockLevel: product.maxStockLevel || 0,\n        unitPrice: product.unitPrice || 0,\n        supplier: product.supplier || '',\n        description: product.description || '',\n        expiryDate: product.expiryDate || '',\n        location: product.location || '',\n        barcode: product.barcode || '',\n        usageRate: product.usageRate || 0,\n        status: product.status || 'active'\n      });\n    } else {\n      // Reset form for add mode\n      setFormData({\n        name: '',\n        category: '',\n        brand: '',\n        sku: '',\n        currentStock: 0,\n        minStockLevel: 0,\n        maxStockLevel: 0,\n        unitPrice: 0,\n        supplier: '',\n        description: '',\n        expiryDate: '',\n        location: '',\n        barcode: '',\n        usageRate: 0,\n        status: 'active'\n      });\n    }\n    setErrors({});\n  }, [product, mode, open]);\n  const handleChange = field => event => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n    if (!formData.category.trim()) {\n      newErrors.category = 'Category is required';\n    }\n    if (!formData.brand.trim()) {\n      newErrors.brand = 'Brand is required';\n    }\n    if (!formData.sku.trim()) {\n      newErrors.sku = 'SKU is required';\n    }\n    if (formData.currentStock < 0) {\n      newErrors.currentStock = 'Current stock cannot be negative';\n    }\n    if (formData.minStockLevel < 0) {\n      newErrors.minStockLevel = 'Minimum stock level cannot be negative';\n    }\n    if (formData.maxStockLevel <= 0) {\n      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';\n    }\n    if (formData.minStockLevel >= formData.maxStockLevel) {\n      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';\n    }\n    if (formData.unitPrice <= 0) {\n      newErrors.unitPrice = 'Unit price must be greater than 0';\n    }\n    if (!formData.supplier.trim()) {\n      newErrors.supplier = 'Supplier is required';\n    }\n    if (formData.usageRate < 0) {\n      newErrors.usageRate = 'Usage rate cannot be negative';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      const productData = {\n        ...formData,\n        currentStock: Number(formData.currentStock),\n        minStockLevel: Number(formData.minStockLevel),\n        maxStockLevel: Number(formData.maxStockLevel),\n        unitPrice: Number(formData.unitPrice),\n        usageRate: Number(formData.usageRate)\n      };\n      if (mode === 'edit' && product) {\n        updateProduct(product.id, productData);\n      } else {\n        addProduct(productData);\n      }\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: mode === 'edit' ? 'Edit Product' : 'Add New Product'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Basic Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Product Name\",\n              value: formData.name,\n              onChange: handleChange('name'),\n              error: !!errors.name,\n              helperText: errors.name,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Brand\",\n              value: formData.brand,\n              onChange: handleChange('brand'),\n              error: !!errors.brand,\n              helperText: errors.brand,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!errors.category,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.category,\n                onChange: handleChange('category'),\n                label: \"Category\",\n                children: allCategories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), errors.category && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"error\",\n                sx: {\n                  mt: 0.5,\n                  ml: 1.5\n                },\n                children: errors.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"SKU\",\n              value: formData.sku,\n              onChange: handleChange('sku'),\n              error: !!errors.sku,\n              helperText: errors.sku,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Description\",\n              value: formData.description,\n              onChange: handleChange('description'),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                mt: 2\n              },\n              children: \"Stock Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Current Stock\",\n              type: \"number\",\n              value: formData.currentStock,\n              onChange: handleChange('currentStock'),\n              error: !!errors.currentStock,\n              helperText: errors.currentStock,\n              inputProps: {\n                min: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Minimum Stock Level\",\n              type: \"number\",\n              value: formData.minStockLevel,\n              onChange: handleChange('minStockLevel'),\n              error: !!errors.minStockLevel,\n              helperText: errors.minStockLevel,\n              inputProps: {\n                min: 0\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Maximum Stock Level\",\n              type: \"number\",\n              value: formData.maxStockLevel,\n              onChange: handleChange('maxStockLevel'),\n              error: !!errors.maxStockLevel,\n              helperText: errors.maxStockLevel,\n              inputProps: {\n                min: 1\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                mt: 2\n              },\n              children: \"Pricing & Supplier\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Unit Price\",\n              type: \"number\",\n              value: formData.unitPrice,\n              onChange: handleChange('unitPrice'),\n              error: !!errors.unitPrice,\n              helperText: errors.unitPrice,\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: \"$\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 35\n                }, this)\n              },\n              inputProps: {\n                min: 0,\n                step: 0.01\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Supplier\",\n              value: formData.supplier,\n              onChange: handleChange('supplier'),\n              error: !!errors.supplier,\n              helperText: errors.supplier,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                mt: 2\n              },\n              children: \"Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Expiry Date\",\n              type: \"date\",\n              value: formData.expiryDate,\n              onChange: handleChange('expiryDate'),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Storage Location\",\n              value: formData.location,\n              onChange: handleChange('location'),\n              placeholder: \"e.g., Storage Room A - Shelf 1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Barcode\",\n              value: formData.barcode,\n              onChange: handleChange('barcode')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Usage Rate (per week)\",\n              type: \"number\",\n              value: formData.usageRate,\n              onChange: handleChange('usageRate'),\n              error: !!errors.usageRate,\n              helperText: errors.usageRate,\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.status,\n                onChange: handleChange('status'),\n                label: \"Status\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"inactive\",\n                  children: \"Inactive\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"discontinued\",\n                  children: \"Discontinued\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: isSubmitting,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: isSubmitting,\n        children: isSubmitting ? 'Saving...' : mode === 'edit' ? 'Update Product' : 'Add Product'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"P/+HSsHEn6oxtacVDhddYiFkvw0=\", false, function () {\n  return [useInventory];\n});\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "Typography", "<PERSON><PERSON>", "InputAdornment", "useInventory", "jsxDEV", "_jsxDEV", "ProductForm", "open", "onClose", "product", "mode", "_s", "addProduct", "updateProduct", "getCategories", "getSuppliers", "formData", "setFormData", "name", "category", "brand", "sku", "currentStock", "minStockLevel", "maxStockLevel", "unitPrice", "supplier", "description", "expiryDate", "location", "barcode", "usageRate", "status", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "categories", "suppliers", "predefinedCategories", "allCategories", "Set", "handleChange", "field", "event", "value", "target", "prev", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSubmit", "productData", "Number", "id", "error", "console", "handleClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "pt", "container", "spacing", "item", "xs", "variant", "gutterBottom", "md", "label", "onChange", "helperText", "required", "map", "color", "mt", "ml", "multiline", "rows", "type", "inputProps", "min", "InputProps", "startAdornment", "position", "step", "InputLabelProps", "shrink", "placeholder", "p", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/ProductForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Box,\n  Typography,\n  Alert,\n  InputAdornment\n} from '@mui/material';\nimport { useInventory } from '../contexts/InventoryContext';\n\nconst ProductForm = ({ open, onClose, product = null, mode = 'add' }) => {\n  const { addProduct, updateProduct, getCategories, getSuppliers } = useInventory();\n  \n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    brand: '',\n    sku: '',\n    currentStock: 0,\n    minStockLevel: 0,\n    maxStockLevel: 0,\n    unitPrice: 0,\n    supplier: '',\n    description: '',\n    expiryDate: '',\n    location: '',\n    barcode: '',\n    usageRate: 0,\n    status: 'active'\n  });\n\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const categories = getCategories();\n  const suppliers = getSuppliers();\n\n  // Predefined categories for new products\n  const predefinedCategories = [\n    'Hair Care',\n    'Hair Color',\n    'Styling Products',\n    'Nail Care',\n    'Skincare',\n    'Tools & Equipment',\n    'Cleaning Supplies',\n    'Other'\n  ];\n\n  const allCategories = [...new Set([...categories, ...predefinedCategories])];\n\n  useEffect(() => {\n    if (product && mode === 'edit') {\n      setFormData({\n        name: product.name || '',\n        category: product.category || '',\n        brand: product.brand || '',\n        sku: product.sku || '',\n        currentStock: product.currentStock || 0,\n        minStockLevel: product.minStockLevel || 0,\n        maxStockLevel: product.maxStockLevel || 0,\n        unitPrice: product.unitPrice || 0,\n        supplier: product.supplier || '',\n        description: product.description || '',\n        expiryDate: product.expiryDate || '',\n        location: product.location || '',\n        barcode: product.barcode || '',\n        usageRate: product.usageRate || 0,\n        status: product.status || 'active'\n      });\n    } else {\n      // Reset form for add mode\n      setFormData({\n        name: '',\n        category: '',\n        brand: '',\n        sku: '',\n        currentStock: 0,\n        minStockLevel: 0,\n        maxStockLevel: 0,\n        unitPrice: 0,\n        supplier: '',\n        description: '',\n        expiryDate: '',\n        location: '',\n        barcode: '',\n        usageRate: 0,\n        status: 'active'\n      });\n    }\n    setErrors({});\n  }, [product, mode, open]);\n\n  const handleChange = (field) => (event) => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n\n    if (!formData.category.trim()) {\n      newErrors.category = 'Category is required';\n    }\n\n    if (!formData.brand.trim()) {\n      newErrors.brand = 'Brand is required';\n    }\n\n    if (!formData.sku.trim()) {\n      newErrors.sku = 'SKU is required';\n    }\n\n    if (formData.currentStock < 0) {\n      newErrors.currentStock = 'Current stock cannot be negative';\n    }\n\n    if (formData.minStockLevel < 0) {\n      newErrors.minStockLevel = 'Minimum stock level cannot be negative';\n    }\n\n    if (formData.maxStockLevel <= 0) {\n      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';\n    }\n\n    if (formData.minStockLevel >= formData.maxStockLevel) {\n      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';\n    }\n\n    if (formData.unitPrice <= 0) {\n      newErrors.unitPrice = 'Unit price must be greater than 0';\n    }\n\n    if (!formData.supplier.trim()) {\n      newErrors.supplier = 'Supplier is required';\n    }\n\n    if (formData.usageRate < 0) {\n      newErrors.usageRate = 'Usage rate cannot be negative';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const productData = {\n        ...formData,\n        currentStock: Number(formData.currentStock),\n        minStockLevel: Number(formData.minStockLevel),\n        maxStockLevel: Number(formData.maxStockLevel),\n        unitPrice: Number(formData.unitPrice),\n        usageRate: Number(formData.usageRate)\n      };\n\n      if (mode === 'edit' && product) {\n        updateProduct(product.id, productData);\n      } else {\n        addProduct(productData);\n      }\n\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n    }\n  };\n\n  return (\n    <Dialog \n      open={open} \n      onClose={handleClose}\n      maxWidth=\"md\"\n      fullWidth\n    >\n      <DialogTitle>\n        {mode === 'edit' ? 'Edit Product' : 'Add New Product'}\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          <Grid container spacing={3}>\n            {/* Basic Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom>\n                Basic Information\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Product Name\"\n                value={formData.name}\n                onChange={handleChange('name')}\n                error={!!errors.name}\n                helperText={errors.name}\n                required\n              />\n            </Grid>\n            \n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Brand\"\n                value={formData.brand}\n                onChange={handleChange('brand')}\n                error={!!errors.brand}\n                helperText={errors.brand}\n                required\n              />\n            </Grid>\n            \n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth error={!!errors.category} required>\n                <InputLabel>Category</InputLabel>\n                <Select\n                  value={formData.category}\n                  onChange={handleChange('category')}\n                  label=\"Category\"\n                >\n                  {allCategories.map((category) => (\n                    <MenuItem key={category} value={category}>\n                      {category}\n                    </MenuItem>\n                  ))}\n                </Select>\n                {errors.category && (\n                  <Typography variant=\"caption\" color=\"error\" sx={{ mt: 0.5, ml: 1.5 }}>\n                    {errors.category}\n                  </Typography>\n                )}\n              </FormControl>\n            </Grid>\n            \n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"SKU\"\n                value={formData.sku}\n                onChange={handleChange('sku')}\n                error={!!errors.sku}\n                helperText={errors.sku}\n                required\n              />\n            </Grid>\n            \n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Description\"\n                value={formData.description}\n                onChange={handleChange('description')}\n                multiline\n                rows={3}\n              />\n            </Grid>\n\n            {/* Stock Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom sx={{ mt: 2 }}>\n                Stock Information\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Current Stock\"\n                type=\"number\"\n                value={formData.currentStock}\n                onChange={handleChange('currentStock')}\n                error={!!errors.currentStock}\n                helperText={errors.currentStock}\n                inputProps={{ min: 0 }}\n              />\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Minimum Stock Level\"\n                type=\"number\"\n                value={formData.minStockLevel}\n                onChange={handleChange('minStockLevel')}\n                error={!!errors.minStockLevel}\n                helperText={errors.minStockLevel}\n                inputProps={{ min: 0 }}\n                required\n              />\n            </Grid>\n            \n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Maximum Stock Level\"\n                type=\"number\"\n                value={formData.maxStockLevel}\n                onChange={handleChange('maxStockLevel')}\n                error={!!errors.maxStockLevel}\n                helperText={errors.maxStockLevel}\n                inputProps={{ min: 1 }}\n                required\n              />\n            </Grid>\n\n            {/* Pricing and Supplier Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom sx={{ mt: 2 }}>\n                Pricing & Supplier\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Unit Price\"\n                type=\"number\"\n                value={formData.unitPrice}\n                onChange={handleChange('unitPrice')}\n                error={!!errors.unitPrice}\n                helperText={errors.unitPrice}\n                InputProps={{\n                  startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                }}\n                inputProps={{ min: 0, step: 0.01 }}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Supplier\"\n                value={formData.supplier}\n                onChange={handleChange('supplier')}\n                error={!!errors.supplier}\n                helperText={errors.supplier}\n                required\n              />\n            </Grid>\n\n            {/* Additional Information */}\n            <Grid item xs={12}>\n              <Typography variant=\"h6\" gutterBottom sx={{ mt: 2 }}>\n                Additional Information\n              </Typography>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Expiry Date\"\n                type=\"date\"\n                value={formData.expiryDate}\n                onChange={handleChange('expiryDate')}\n                InputLabelProps={{\n                  shrink: true,\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Storage Location\"\n                value={formData.location}\n                onChange={handleChange('location')}\n                placeholder=\"e.g., Storage Room A - Shelf 1\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Barcode\"\n                value={formData.barcode}\n                onChange={handleChange('barcode')}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Usage Rate (per week)\"\n                type=\"number\"\n                value={formData.usageRate}\n                onChange={handleChange('usageRate')}\n                error={!!errors.usageRate}\n                helperText={errors.usageRate}\n                inputProps={{ min: 0, step: 0.1 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Status</InputLabel>\n                <Select\n                  value={formData.status}\n                  onChange={handleChange('status')}\n                  label=\"Status\"\n                >\n                  <MenuItem value=\"active\">Active</MenuItem>\n                  <MenuItem value=\"inactive\">Inactive</MenuItem>\n                  <MenuItem value=\"discontinued\">Discontinued</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n          </Grid>\n        </Box>\n      </DialogContent>\n\n      <DialogActions sx={{ p: 3 }}>\n        <Button onClick={handleClose} disabled={isSubmitting}>\n          Cancel\n        </Button>\n        <Button \n          onClick={handleSubmit}\n          variant=\"contained\"\n          disabled={isSubmitting}\n        >\n          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Product' : 'Add Product')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,cAAc,QACT,eAAe;AACtB,SAASC,YAAY,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM;IAAEC,UAAU;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGZ,YAAY,CAAC,CAAC;EAEjF,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IACvCgC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMmD,UAAU,GAAGvB,aAAa,CAAC,CAAC;EAClC,MAAMwB,SAAS,GAAGvB,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMwB,oBAAoB,GAAG,CAC3B,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,mBAAmB,EACnB,OAAO,CACR;EAED,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGJ,UAAU,EAAE,GAAGE,oBAAoB,CAAC,CAAC,CAAC;EAE5EpD,SAAS,CAAC,MAAM;IACd,IAAIsB,OAAO,IAAIC,IAAI,KAAK,MAAM,EAAE;MAC9BO,WAAW,CAAC;QACVC,IAAI,EAAET,OAAO,CAACS,IAAI,IAAI,EAAE;QACxBC,QAAQ,EAAEV,OAAO,CAACU,QAAQ,IAAI,EAAE;QAChCC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI,EAAE;QAC1BC,GAAG,EAAEZ,OAAO,CAACY,GAAG,IAAI,EAAE;QACtBC,YAAY,EAAEb,OAAO,CAACa,YAAY,IAAI,CAAC;QACvCC,aAAa,EAAEd,OAAO,CAACc,aAAa,IAAI,CAAC;QACzCC,aAAa,EAAEf,OAAO,CAACe,aAAa,IAAI,CAAC;QACzCC,SAAS,EAAEhB,OAAO,CAACgB,SAAS,IAAI,CAAC;QACjCC,QAAQ,EAAEjB,OAAO,CAACiB,QAAQ,IAAI,EAAE;QAChCC,WAAW,EAAElB,OAAO,CAACkB,WAAW,IAAI,EAAE;QACtCC,UAAU,EAAEnB,OAAO,CAACmB,UAAU,IAAI,EAAE;QACpCC,QAAQ,EAAEpB,OAAO,CAACoB,QAAQ,IAAI,EAAE;QAChCC,OAAO,EAAErB,OAAO,CAACqB,OAAO,IAAI,EAAE;QAC9BC,SAAS,EAAEtB,OAAO,CAACsB,SAAS,IAAI,CAAC;QACjCC,MAAM,EAAEvB,OAAO,CAACuB,MAAM,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAf,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE,EAAE;QACPC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,CAACzB,OAAO,EAAEC,IAAI,EAAEH,IAAI,CAAC,CAAC;EAEzB,MAAMmC,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC5B,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIZ,MAAM,CAACU,KAAK,CAAC,EAAE;MACjBT,SAAS,CAACa,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACJ,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACjC,QAAQ,CAACE,IAAI,CAACgC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAC/B,IAAI,GAAG,0BAA0B;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAC9B,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACH,QAAQ,CAACI,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC7B,KAAK,GAAG,mBAAmB;IACvC;IAEA,IAAI,CAACJ,QAAQ,CAACK,GAAG,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACxBD,SAAS,CAAC5B,GAAG,GAAG,iBAAiB;IACnC;IAEA,IAAIL,QAAQ,CAACM,YAAY,GAAG,CAAC,EAAE;MAC7B2B,SAAS,CAAC3B,YAAY,GAAG,kCAAkC;IAC7D;IAEA,IAAIN,QAAQ,CAACO,aAAa,GAAG,CAAC,EAAE;MAC9B0B,SAAS,CAAC1B,aAAa,GAAG,wCAAwC;IACpE;IAEA,IAAIP,QAAQ,CAACQ,aAAa,IAAI,CAAC,EAAE;MAC/ByB,SAAS,CAACzB,aAAa,GAAG,4CAA4C;IACxE;IAEA,IAAIR,QAAQ,CAACO,aAAa,IAAIP,QAAQ,CAACQ,aAAa,EAAE;MACpDyB,SAAS,CAAC1B,aAAa,GAAG,+CAA+C;IAC3E;IAEA,IAAIP,QAAQ,CAACS,SAAS,IAAI,CAAC,EAAE;MAC3BwB,SAAS,CAACxB,SAAS,GAAG,mCAAmC;IAC3D;IAEA,IAAI,CAACT,QAAQ,CAACU,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACvB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAIV,QAAQ,CAACe,SAAS,GAAG,CAAC,EAAE;MAC1BkB,SAAS,CAAClB,SAAS,GAAG,+BAA+B;IACvD;IAEAG,SAAS,CAACe,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAZ,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMmB,WAAW,GAAG;QAClB,GAAGvC,QAAQ;QACXM,YAAY,EAAEkC,MAAM,CAACxC,QAAQ,CAACM,YAAY,CAAC;QAC3CC,aAAa,EAAEiC,MAAM,CAACxC,QAAQ,CAACO,aAAa,CAAC;QAC7CC,aAAa,EAAEgC,MAAM,CAACxC,QAAQ,CAACQ,aAAa,CAAC;QAC7CC,SAAS,EAAE+B,MAAM,CAACxC,QAAQ,CAACS,SAAS,CAAC;QACrCM,SAAS,EAAEyB,MAAM,CAACxC,QAAQ,CAACe,SAAS;MACtC,CAAC;MAED,IAAIrB,IAAI,KAAK,MAAM,IAAID,OAAO,EAAE;QAC9BI,aAAa,CAACJ,OAAO,CAACgD,EAAE,EAAEF,WAAW,CAAC;MACxC,CAAC,MAAM;QACL3C,UAAU,CAAC2C,WAAW,CAAC;MACzB;MAEA/C,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRtB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACzB,YAAY,EAAE;MACjB3B,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA,CAACjB,MAAM;IACLmB,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEoD,WAAY;IACrBC,QAAQ,EAAC,IAAI;IACbC,SAAS;IAAAC,QAAA,gBAET1D,OAAA,CAAChB,WAAW;MAAA0E,QAAA,EACTrD,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG;IAAiB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAEd9D,OAAA,CAACf,aAAa;MAAAyE,QAAA,eACZ1D,OAAA,CAACN,GAAG;QAACqE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eACjB1D,OAAA,CAACX,IAAI;UAAC4E,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBAEzB1D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eAChB1D,OAAA,CAACL,UAAU;cAAC0E,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,cAAc;cACpBhC,KAAK,EAAE7B,QAAQ,CAACE,IAAK;cACrB4D,QAAQ,EAAEpC,YAAY,CAAC,MAAM,CAAE;cAC/BgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACf,IAAK;cACrB6D,UAAU,EAAE9C,MAAM,CAACf,IAAK;cACxB8D,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,OAAO;cACbhC,KAAK,EAAE7B,QAAQ,CAACI,KAAM;cACtB0D,QAAQ,EAAEpC,YAAY,CAAC,OAAO,CAAE;cAChCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACb,KAAM;cACtB2D,UAAU,EAAE9C,MAAM,CAACb,KAAM;cACzB4D,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACV,WAAW;cAACmE,SAAS;cAACJ,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACd,QAAS;cAAC6D,QAAQ;cAAAjB,QAAA,gBACvD1D,OAAA,CAACT,UAAU;gBAAAmE,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC9D,OAAA,CAACR,MAAM;gBACLgD,KAAK,EAAE7B,QAAQ,CAACG,QAAS;gBACzB2D,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;gBACnCmC,KAAK,EAAC,UAAU;gBAAAd,QAAA,EAEfvB,aAAa,CAACyC,GAAG,CAAE9D,QAAQ,iBAC1Bd,OAAA,CAACP,QAAQ;kBAAgB+C,KAAK,EAAE1B,QAAS;kBAAA4C,QAAA,EACtC5C;gBAAQ,GADIA,QAAQ;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,EACRlC,MAAM,CAACd,QAAQ,iBACdd,OAAA,CAACL,UAAU;gBAAC0E,OAAO,EAAC,SAAS;gBAACQ,KAAK,EAAC,OAAO;gBAACd,EAAE,EAAE;kBAAEe,EAAE,EAAE,GAAG;kBAAEC,EAAE,EAAE;gBAAI,CAAE;gBAAArB,QAAA,EAClE9B,MAAM,CAACd;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,KAAK;cACXhC,KAAK,EAAE7B,QAAQ,CAACK,GAAI;cACpByD,QAAQ,EAAEpC,YAAY,CAAC,KAAK,CAAE;cAC9BgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACZ,GAAI;cACpB0D,UAAU,EAAE9C,MAAM,CAACZ,GAAI;cACvB2D,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eAChB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,aAAa;cACnBhC,KAAK,EAAE7B,QAAQ,CAACW,WAAY;cAC5BmD,QAAQ,EAAEpC,YAAY,CAAC,aAAa,CAAE;cACtC2C,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eAChB1D,OAAA,CAACL,UAAU;cAAC0E,OAAO,EAAC,IAAI;cAACC,YAAY;cAACP,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,eAAe;cACrBU,IAAI,EAAC,QAAQ;cACb1C,KAAK,EAAE7B,QAAQ,CAACM,YAAa;cAC7BwD,QAAQ,EAAEpC,YAAY,CAAC,cAAc,CAAE;cACvCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACX,YAAa;cAC7ByD,UAAU,EAAE9C,MAAM,CAACX,YAAa;cAChCkE,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,qBAAqB;cAC3BU,IAAI,EAAC,QAAQ;cACb1C,KAAK,EAAE7B,QAAQ,CAACO,aAAc;cAC9BuD,QAAQ,EAAEpC,YAAY,CAAC,eAAe,CAAE;cACxCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACV,aAAc;cAC9BwD,UAAU,EAAE9C,MAAM,CAACV,aAAc;cACjCiE,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE,CAAE;cACvBT,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,qBAAqB;cAC3BU,IAAI,EAAC,QAAQ;cACb1C,KAAK,EAAE7B,QAAQ,CAACQ,aAAc;cAC9BsD,QAAQ,EAAEpC,YAAY,CAAC,eAAe,CAAE;cACxCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACT,aAAc;cAC9BuD,UAAU,EAAE9C,MAAM,CAACT,aAAc;cACjCgE,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE,CAAE;cACvBT,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eAChB1D,OAAA,CAACL,UAAU;cAAC0E,OAAO,EAAC,IAAI;cAACC,YAAY;cAACP,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,YAAY;cAClBU,IAAI,EAAC,QAAQ;cACb1C,KAAK,EAAE7B,QAAQ,CAACS,SAAU;cAC1BqD,QAAQ,EAAEpC,YAAY,CAAC,WAAW,CAAE;cACpCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACR,SAAU;cAC1BsD,UAAU,EAAE9C,MAAM,CAACR,SAAU;cAC7BiE,UAAU,EAAE;gBACVC,cAAc,eAAEtF,OAAA,CAACH,cAAc;kBAAC0F,QAAQ,EAAC,OAAO;kBAAA7B,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB;cACpE,CAAE;cACFqB,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEI,IAAI,EAAE;cAAK,CAAE;cACnCb,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,UAAU;cAChBhC,KAAK,EAAE7B,QAAQ,CAACU,QAAS;cACzBoD,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;cACnCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACP,QAAS;cACzBqD,UAAU,EAAE9C,MAAM,CAACP,QAAS;cAC5BsD,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAV,QAAA,eAChB1D,OAAA,CAACL,UAAU;cAAC0E,OAAO,EAAC,IAAI;cAACC,YAAY;cAACP,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAApB,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,aAAa;cACnBU,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAE7B,QAAQ,CAACY,UAAW;cAC3BkD,QAAQ,EAAEpC,YAAY,CAAC,YAAY,CAAE;cACrCoD,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,kBAAkB;cACxBhC,KAAK,EAAE7B,QAAQ,CAACa,QAAS;cACzBiD,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;cACnCsD,WAAW,EAAC;YAAgC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,SAAS;cACfhC,KAAK,EAAE7B,QAAQ,CAACc,OAAQ;cACxBgD,QAAQ,EAAEpC,YAAY,CAAC,SAAS;YAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACb,SAAS;cACRsE,SAAS;cACTe,KAAK,EAAC,uBAAuB;cAC7BU,IAAI,EAAC,QAAQ;cACb1C,KAAK,EAAE7B,QAAQ,CAACe,SAAU;cAC1B+C,QAAQ,EAAEpC,YAAY,CAAC,WAAW,CAAE;cACpCgB,KAAK,EAAE,CAAC,CAACzB,MAAM,CAACF,SAAU;cAC1BgD,UAAU,EAAE9C,MAAM,CAACF,SAAU;cAC7ByD,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEI,IAAI,EAAE;cAAI;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP9D,OAAA,CAACX,IAAI;YAAC8E,IAAI;YAACC,EAAE,EAAE,EAAG;YAACG,EAAE,EAAE,CAAE;YAAAb,QAAA,eACvB1D,OAAA,CAACV,WAAW;cAACmE,SAAS;cAAAC,QAAA,gBACpB1D,OAAA,CAACT,UAAU;gBAAAmE,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/B9D,OAAA,CAACR,MAAM;gBACLgD,KAAK,EAAE7B,QAAQ,CAACgB,MAAO;gBACvB8C,QAAQ,EAAEpC,YAAY,CAAC,QAAQ,CAAE;gBACjCmC,KAAK,EAAC,QAAQ;gBAAAd,QAAA,gBAEd1D,OAAA,CAACP,QAAQ;kBAAC+C,KAAK,EAAC,QAAQ;kBAAAkB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1C9D,OAAA,CAACP,QAAQ;kBAAC+C,KAAK,EAAC,UAAU;kBAAAkB,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9C9D,OAAA,CAACP,QAAQ;kBAAC+C,KAAK,EAAC,cAAc;kBAAAkB,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB9D,OAAA,CAACd,aAAa;MAAC6E,EAAE,EAAE;QAAE6B,CAAC,EAAE;MAAE,CAAE;MAAAlC,QAAA,gBAC1B1D,OAAA,CAACZ,MAAM;QAACyG,OAAO,EAAEtC,WAAY;QAACuC,QAAQ,EAAEhE,YAAa;QAAA4B,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA,CAACZ,MAAM;QACLyG,OAAO,EAAE5C,YAAa;QACtBoB,OAAO,EAAC,WAAW;QACnByB,QAAQ,EAAEhE,YAAa;QAAA4B,QAAA,EAEtB5B,YAAY,GAAG,WAAW,GAAIzB,IAAI,KAAK,MAAM,GAAG,gBAAgB,GAAG;MAAc;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxD,EAAA,CA7bIL,WAAW;EAAA,QACoDH,YAAY;AAAA;AAAAiG,EAAA,GAD3E9F,WAAW;AA+bjB,eAAeA,WAAW;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}