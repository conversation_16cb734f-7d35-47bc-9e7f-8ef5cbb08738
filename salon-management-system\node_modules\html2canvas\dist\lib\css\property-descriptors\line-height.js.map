{"version": 3, "file": "line-height.js", "sourceRoot": "", "sources": ["../../../../src/css/property-descriptors/line-height.ts"], "names": [], "mappings": ";;;AACA,2CAAwD;AAExD,gEAAgF;AACnE,QAAA,UAAU,GAAkC;IACrD,IAAI,EAAE,aAAa;IACnB,YAAY,EAAE,QAAQ;IACtB,MAAM,EAAE,KAAK;IACb,IAAI,qBAA2C;CAClD,CAAC;AAEK,IAAM,iBAAiB,GAAG,UAAC,KAAe,EAAE,QAAgB;IAC/D,IAAI,qBAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;QACjD,OAAO,GAAG,GAAG,QAAQ,CAAC;KACzB;SAAM,IAAI,KAAK,CAAC,IAAI,0BAA2B,EAAE;QAC9C,OAAO,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;KAClC;SAAM,IAAI,sCAAkB,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,oCAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;KAC5C;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAVW,QAAA,iBAAiB,qBAU5B"}