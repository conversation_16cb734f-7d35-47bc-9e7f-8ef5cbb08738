{"version": 3, "file": "tokenizer.js", "sourceRoot": "", "sources": ["../../../../src/css/syntax/tokenizer.ts"], "names": [], "mappings": ";AAAA,qCAAqC;;;AAErC,iDAA2D;AAyG9C,QAAA,iBAAiB,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAA,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;AACjB,QAAA,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC;AACtB,QAAA,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC;AAElC,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,OAAO,GAAG,MAAM,CAAC;AACvB,IAAM,eAAe,GAAG,MAAM,CAAC;AAC/B,IAAM,oBAAoB,GAAG,MAAM,CAAC;AACpC,IAAM,KAAK,GAAG,MAAM,CAAC;AACrB,IAAM,cAAc,GAAG,MAAM,CAAC;AAC9B,IAAM,WAAW,GAAG,MAAM,CAAC;AAC3B,IAAM,WAAW,GAAG,MAAM,CAAC;AAC3B,IAAM,WAAW,GAAG,MAAM,CAAC;AAC3B,IAAM,eAAe,GAAG,MAAM,CAAC;AAC/B,IAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,IAAM,gBAAgB,GAAG,MAAM,CAAC;AAChC,IAAM,iBAAiB,GAAG,MAAM,CAAC;AACjC,IAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,IAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,IAAM,gBAAgB,GAAG,MAAM,CAAC;AAChC,IAAM,cAAc,GAAG,MAAM,CAAC;AAC9B,IAAM,iBAAiB,GAAG,MAAM,CAAC;AACjC,IAAM,aAAa,GAAG,MAAM,CAAC;AAC7B,IAAM,mBAAmB,GAAG,MAAM,CAAC;AACnC,IAAM,oBAAoB,GAAG,MAAM,CAAC;AACpC,IAAM,iBAAiB,GAAG,MAAM,CAAC;AACjC,IAAM,kBAAkB,GAAG,MAAM,CAAC;AAClC,IAAM,aAAa,GAAG,MAAM,CAAC;AAC7B,IAAM,mBAAmB,GAAG,MAAM,CAAC;AACnC,IAAM,aAAa,GAAG,MAAM,CAAC;AAC7B,IAAM,KAAK,GAAG,MAAM,CAAC;AACrB,IAAM,OAAO,GAAG,MAAM,CAAC;AACvB,IAAM,qBAAqB,GAAG,MAAM,CAAC;AACrC,IAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,KAAK,GAAG,MAAM,CAAC;AACrB,IAAM,KAAK,GAAG,MAAM,CAAC;AACrB,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,IAAI,GAAG,MAAM,CAAC;AACpB,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,eAAe,GAAG,MAAM,CAAC;AAC/B,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB,IAAM,yBAAyB,GAAG,MAAM,CAAC;AACzC,IAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAM,GAAG,GAAG,CAAC,CAAC,CAAC;AACf,IAAM,IAAI,GAAG,MAAM,CAAC;AACpB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AACjB,IAAM,CAAC,GAAG,MAAM,CAAC;AAEjB,IAAM,OAAO,GAAG,UAAC,SAAiB,IAAK,OAAA,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,MAAM,EAAxC,CAAwC,CAAC;AAChF,IAAM,oBAAoB,GAAG,UAAC,SAAiB,IAAK,OAAA,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,EAA1C,CAA0C,CAAC;AAC/F,IAAM,KAAK,GAAG,UAAC,SAAiB;IAC5B,OAAA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;AAA9F,CAA8F,CAAC;AACnG,IAAM,iBAAiB,GAAG,UAAC,SAAiB,IAAK,OAAA,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAhC,CAAgC,CAAC;AAClF,IAAM,iBAAiB,GAAG,UAAC,SAAiB,IAAK,OAAA,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAhC,CAAgC,CAAC;AAClF,IAAM,QAAQ,GAAG,UAAC,SAAiB,IAAK,OAAA,iBAAiB,CAAC,SAAS,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAA5D,CAA4D,CAAC;AACrG,IAAM,mBAAmB,GAAG,UAAC,SAAiB,IAAK,OAAA,SAAS,IAAI,OAAO,EAApB,CAAoB,CAAC;AACxE,IAAM,YAAY,GAAG,UAAC,SAAiB;IACnC,OAAA,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,oBAAoB,IAAI,SAAS,KAAK,KAAK;AAApF,CAAoF,CAAC;AACzF,IAAM,oBAAoB,GAAG,UAAC,SAAiB;IAC3C,OAAA,QAAQ,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,QAAQ;AAA/E,CAA+E,CAAC;AACpF,IAAM,eAAe,GAAG,UAAC,SAAiB;IACtC,OAAA,oBAAoB,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,YAAY;AAAnF,CAAmF,CAAC;AACxF,IAAM,uBAAuB,GAAG,UAAC,SAAiB;IAC9C,OAAO,CACH,CAAC,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,SAAS,CAAC;QAC7C,SAAS,KAAK,eAAe;QAC7B,CAAC,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,yBAAyB,CAAC;QAClE,SAAS,KAAK,MAAM,CACvB,CAAC;AACN,CAAC,CAAC;AACF,IAAM,aAAa,GAAG,UAAC,EAAU,EAAE,EAAU;IACzC,IAAI,EAAE,KAAK,eAAe,EAAE;QACxB,OAAO,KAAK,CAAC;KAChB;IAED,OAAO,EAAE,KAAK,SAAS,CAAC;AAC5B,CAAC,CAAC;AACF,IAAM,iBAAiB,GAAG,UAAC,EAAU,EAAE,EAAU,EAAE,EAAU;IACzD,IAAI,EAAE,KAAK,YAAY,EAAE;QACrB,OAAO,oBAAoB,CAAC,EAAE,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KAC5D;SAAM,IAAI,oBAAoB,CAAC,EAAE,CAAC,EAAE;QACjC,OAAO,IAAI,CAAC;KACf;SAAM,IAAI,EAAE,KAAK,eAAe,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QACxD,OAAO,IAAI,CAAC;KACf;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,IAAM,aAAa,GAAG,UAAC,EAAU,EAAE,EAAU,EAAE,EAAU;IACrD,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,YAAY,EAAE;QACzC,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE;YACb,OAAO,IAAI,CAAC;SACf;QAED,OAAO,EAAE,KAAK,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC;KAC1C;IAED,IAAI,EAAE,KAAK,SAAS,EAAE;QAClB,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;KACtB;IAED,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC,CAAC;AAEF,IAAM,cAAc,GAAG,UAAC,UAAoB;IACxC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;QAC/D,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;YAChC,IAAI,GAAG,CAAC,CAAC,CAAC;SACb;QACD,CAAC,EAAE,CAAC;KACP;IAED,IAAM,QAAQ,GAAG,EAAE,CAAC;IAEpB,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;IAED,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,8BAAa,eAAI,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3E,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;QAC7B,CAAC,EAAE,CAAC;KACP;IAED,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;IAED,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC9B,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,8BAAa,eAAI,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAElE,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC5C,CAAC,EAAE,CAAC;KACP;IAED,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;QAC/D,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;YAChC,OAAO,GAAG,CAAC,CAAC,CAAC;SAChB;QACD,CAAC,EAAE,CAAC;KACP;IAED,IAAM,QAAQ,GAAG,EAAE,CAAC;IAEpB,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;IAED,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,8BAAa,eAAI,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3E,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,GAAG,GAAG,CAAC,CAAC;AACpF,CAAC,CAAC;AAEF,IAAM,sBAAsB,GAAU;IAClC,IAAI,gCAAkC;CACzC,CAAC;AACF,IAAM,uBAAuB,GAAU;IACnC,IAAI,iCAAmC;CAC1C,CAAC;AACF,IAAM,WAAW,GAAU,EAAC,IAAI,qBAAuB,EAAC,CAAC;AACzD,IAAM,kBAAkB,GAAU,EAAC,IAAI,6BAA8B,EAAC,CAAC;AACvE,IAAM,kBAAkB,GAAU,EAAC,IAAI,4BAA8B,EAAC,CAAC;AACvE,IAAM,YAAY,GAAU,EAAC,IAAI,uBAAwB,EAAC,CAAC;AAC3D,IAAM,gBAAgB,GAAU,EAAC,IAAI,0BAA4B,EAAC,CAAC;AACnE,IAAM,mBAAmB,GAAU,EAAC,IAAI,8BAA+B,EAAC,CAAC;AACzE,IAAM,wBAAwB,GAAU;IACpC,IAAI,mCAAoC;CAC3C,CAAC;AACF,IAAM,yBAAyB,GAAU;IACrC,IAAI,oCAAqC;CAC5C,CAAC;AACF,IAAM,qBAAqB,GAAU,EAAC,IAAI,gCAAiC,EAAC,CAAC;AAC7E,IAAM,aAAa,GAAU,EAAC,IAAI,wBAAyB,EAAC,CAAC;AAC7D,IAAM,gBAAgB,GAAU,EAAC,IAAI,0BAA4B,EAAC,CAAC;AACnE,IAAM,SAAS,GAAU,EAAC,IAAI,oBAAqB,EAAC,CAAC;AACrD,IAAM,SAAS,GAAU,EAAC,IAAI,oBAAqB,EAAC,CAAC;AACrD,IAAM,WAAW,GAAU,EAAC,IAAI,sBAAuB,EAAC,CAAC;AACzD,IAAM,eAAe,GAAU,EAAC,IAAI,0BAA2B,EAAC,CAAC;AACjE,IAAM,yBAAyB,GAAU;IACrC,IAAI,oCAAqC;CAC5C,CAAC;AACF,IAAM,0BAA0B,GAAU;IACtC,IAAI,qCAAsC;CAC7C,CAAC;AACF,IAAM,gBAAgB,GAAU,EAAC,IAAI,2BAA4B,EAAC,CAAC;AACtD,QAAA,SAAS,GAAU,EAAC,IAAI,oBAAqB,EAAC,CAAC;AAE5D;IAGI;QACI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,yBAAK,GAAL,UAAM,KAAa;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,6BAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,wBAAI,GAAJ;QACI,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,OAAO,KAAK,KAAK,iBAAS,EAAE;YACxB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;SAC/B;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gCAAY,GAApB;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1C,QAAQ,SAAS,EAAE;YACf,KAAK,cAAc;gBACf,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YACnD,KAAK,WAAW;gBACZ,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,eAAe,CAAC,EAAE,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;oBAC9C,IAAM,KAAK,GAAG,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,eAAO,CAAC,CAAC,CAAC,yBAAiB,CAAC;oBAC1E,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;oBAEjC,OAAO,EAAC,IAAI,oBAAsB,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAC,CAAC;iBACrD;gBACD,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,kBAAkB,CAAC;iBAC7B;gBACD,MAAM;YACV,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC/C,KAAK,gBAAgB;gBACjB,OAAO,sBAAsB,CAAC;YAClC,KAAK,iBAAiB;gBAClB,OAAO,uBAAuB,CAAC;YACnC,KAAK,QAAQ;gBACT,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,qBAAqB,CAAC;iBAChC;gBACD,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBACrC;gBACD,MAAM;YACV,KAAK,KAAK;gBACN,OAAO,WAAW,CAAC;YACvB,KAAK,YAAY;gBACb,IAAM,EAAE,GAAG,SAAS,CAAC;gBACrB,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBAEjC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;oBAC3B,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBACrC;gBAED,IAAI,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;oBAC/B,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBACvC;gBAED,IAAI,EAAE,KAAK,YAAY,IAAI,EAAE,KAAK,iBAAiB,EAAE;oBACjD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,SAAS,CAAC;iBACpB;gBACD,MAAM;YAEV,KAAK,SAAS;gBACV,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;iBACrC;gBACD,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;oBACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,IAAI,EAAE;wBACT,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAChC,IAAI,CAAC,KAAK,QAAQ,EAAE;4BAChB,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,IAAI,CAAC,KAAK,OAAO,EAAE;gCACf,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;6BAC9B;yBACJ;wBACD,IAAI,CAAC,KAAK,GAAG,EAAE;4BACX,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;yBAC9B;qBACJ;iBACJ;gBACD,MAAM;YACV,KAAK,KAAK;gBACN,OAAO,WAAW,CAAC;YACvB,KAAK,SAAS;gBACV,OAAO,eAAe,CAAC;YAC3B,KAAK,cAAc;gBACf,IACI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,gBAAgB;oBAC1C,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,YAAY;oBACtC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,YAAY,EACxC;oBACE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,SAAS,CAAC;iBACpB;gBACD,MAAM;YACV,KAAK,aAAa;gBACd,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;oBAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjC,OAAO,EAAC,IAAI,0BAA4B,EAAE,KAAK,OAAA,EAAC,CAAC;iBACpD;gBACD,MAAM;YACV,KAAK,mBAAmB;gBACpB,OAAO,yBAAyB,CAAC;YACrC,KAAK,eAAe;gBAChB,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBACjD,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBACvC;gBACD,MAAM;YACV,KAAK,oBAAoB;gBACrB,OAAO,0BAA0B,CAAC;YACtC,KAAK,iBAAiB;gBAClB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,kBAAkB,CAAC;iBAC7B;gBACD,MAAM;YACV,KAAK,kBAAkB;gBACnB,OAAO,wBAAwB,CAAC;YACpC,KAAK,mBAAmB;gBACpB,OAAO,yBAAyB,CAAC;YACrC,KAAK,CAAC,CAAC;YACP,KAAK,CAAC;gBACF,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,EAAE,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,aAAa,CAAC,EAAE;oBACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,wBAAwB,EAAE,CAAC;iBACnC;gBACD,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBACnC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACxC,KAAK,aAAa;gBACd,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,gBAAgB,CAAC;iBAC3B;gBACD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;oBACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,YAAY,CAAC;iBACvB;gBACD,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;oBACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,mBAAmB,CAAC;iBAC9B;gBACD,MAAM;YACV,KAAK,GAAG;gBACJ,OAAO,iBAAS,CAAC;SACxB;QAED,IAAI,YAAY,CAAC,SAAS,CAAC,EAAE;YACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE;YACpB,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACrC;QAED,IAAI,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACjC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;SACvC;QAED,OAAO,EAAC,IAAI,qBAAuB,EAAE,KAAK,EAAE,8BAAa,CAAC,SAAS,CAAC,EAAC,CAAC;IAC1E,CAAC;IAEO,oCAAgB,GAAxB;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACrD,CAAC;IAEO,sCAAkB,GAA1B,UAA2B,SAAiB;QACxC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEO,iCAAa,GAArB,UAAsB,KAAa;QAC/B,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,CAAC,CAAC;SACb;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEO,4CAAwB,GAAhC;QACI,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxC,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvB,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACvC;QACD,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,OAAO,SAAS,KAAK,aAAa,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvB,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpC,aAAa,GAAG,IAAI,CAAC;SACxB;QAED,IAAI,aAAa,EAAE;YACf,IAAM,OAAK,GAAG,QAAQ,CAClB,8BAAa,eAAI,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAxC,CAAwC,CAAC,GAChF,EAAE,CACL,CAAC;YACF,IAAM,GAAG,GAAG,QAAQ,CAAC,8BAAa,eAAI,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAArC,CAAqC,CAAC,GAAG,EAAE,CAAC,CAAC;YACzG,OAAO,EAAC,IAAI,8BAA+B,EAAE,KAAK,SAAA,EAAE,GAAG,KAAA,EAAC,CAAC;SAC5D;QAED,IAAM,KAAK,GAAG,QAAQ,CAAC,8BAAa,eAAI,MAAM,GAAG,EAAE,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;YACxE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpC,IAAM,SAAS,GAAG,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7C,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC1B,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACvC;YACD,IAAM,GAAG,GAAG,QAAQ,CAAC,8BAAa,eAAI,SAAS,GAAG,EAAE,CAAC,CAAC;YAEtD,OAAO,EAAC,IAAI,8BAA+B,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAC,CAAC;SAC5D;aAAM;YACH,OAAO,EAAC,IAAI,8BAA+B,EAAE,KAAK,OAAA,EAAE,GAAG,EAAE,KAAK,EAAC,CAAC;SACnE;IACL,CAAC;IAEO,yCAAqB,GAA7B;QACI,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;SACjC;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;YACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,EAAC,IAAI,yBAA0B,EAAE,KAAK,OAAA,EAAC,CAAC;SAClD;QAED,OAAO,EAAC,IAAI,sBAAuB,EAAE,KAAK,OAAA,EAAC,CAAC;IAChD,CAAC;IAEO,mCAAe,GAAvB;QACI,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC/B,OAAO,EAAC,IAAI,oBAAqB,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC;SACjD;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,cAAc,EAAE;YAChD,IAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACrE,IAAI,WAAW,CAAC,IAAI,yBAA2B,EAAE;gBAC7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,iBAAiB,EAAE;oBAC9E,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,EAAC,IAAI,oBAAqB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAC,CAAC;iBAChE;aACJ;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO,aAAa,CAAC;SACxB;QAED,OAAO,IAAI,EAAE;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1C,IAAI,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK,iBAAiB,EAAE;gBACtD,OAAO,EAAC,IAAI,oBAAqB,EAAE,KAAK,EAAE,8BAAa,eAAI,KAAK,CAAC,EAAC,CAAC;aACtE;iBAAM,IAAI,YAAY,CAAC,SAAS,CAAC,EAAE;gBAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,iBAAiB,EAAE;oBAC9E,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO,EAAC,IAAI,oBAAqB,EAAE,KAAK,EAAE,8BAAa,eAAI,KAAK,CAAC,EAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,OAAO,aAAa,CAAC;aACxB;iBAAM,IACH,SAAS,KAAK,cAAc;gBAC5B,SAAS,KAAK,UAAU;gBACxB,SAAS,KAAK,gBAAgB;gBAC9B,uBAAuB,CAAC,SAAS,CAAC,EACpC;gBACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,OAAO,aAAa,CAAC;aACxB;iBAAM,IAAI,SAAS,KAAK,eAAe,EAAE;gBACtC,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBACjD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;iBAC9C;qBAAM;oBACH,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,OAAO,aAAa,CAAC;iBACxB;aACJ;iBAAM;gBACH,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aACzB;SACJ;IACL,CAAC;IAEO,qCAAiB,GAAzB;QACI,OAAO,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;YACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAEO,yCAAqB,GAA7B;QACI,OAAO,IAAI,EAAE;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1C,IAAI,SAAS,KAAK,iBAAiB,IAAI,SAAS,KAAK,GAAG,EAAE;gBACtD,OAAO;aACV;YAED,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;gBACjD,IAAI,CAAC,uBAAuB,EAAE,CAAC;aAClC;SACJ;IACL,CAAC;IAEO,sCAAkB,GAA1B,UAA2B,KAAa;QACpC,IAAM,gBAAgB,GAAG,KAAK,CAAC;QAC/B,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,OAAO,KAAK,GAAG,CAAC,EAAE;YACd,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACjD,KAAK,IAAI,8BAAa,eAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;YACzD,KAAK,IAAI,MAAM,CAAC;SACnB;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,sCAAkB,GAA1B,UAA2B,eAAuB;QAC9C,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,GAAG;YACC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,eAAe,EAAE;gBAC/E,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBACpC,OAAO,EAAC,IAAI,sBAAwB,EAAE,KAAK,OAAA,EAAC,CAAC;aAChD;YAED,IAAI,SAAS,KAAK,SAAS,EAAE;gBACzB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzB,OAAO,gBAAgB,CAAC;aAC3B;YAED,IAAI,SAAS,KAAK,eAAe,EAAE;gBAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,SAAS,EAAE;oBACpC,IAAI,IAAI,KAAK,SAAS,EAAE;wBACpB,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBACpC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACP,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;qBACvB;yBAAM,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;wBACvC,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBACpC,KAAK,IAAI,8BAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;wBACvD,CAAC,GAAG,CAAC,CAAC,CAAC;qBACV;iBACJ;aACJ;YAED,CAAC,EAAE,CAAC;SACP,QAAQ,IAAI,EAAE;IACnB,CAAC;IAEO,iCAAa,GAArB;QACI,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,IAAI,GAAG,oBAAY,CAAC;QACxB,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,YAAY,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACtC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACtC;QACD,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,EAAE,KAAK,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC5D,IAAI,GAAG,mBAAW,CAAC;YACnB,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;aACtC;SACJ;QAED,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC3B,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,YAAY,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;YACvG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC5D,IAAI,GAAG,mBAAW,CAAC;YACnB,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;aACtC;SACJ;QAED,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAEO,uCAAmB,GAA3B;QACU,IAAA,KAAkB,IAAI,CAAC,aAAa,EAAE,EAArC,MAAM,QAAA,EAAE,KAAK,QAAwB,CAAC;QAC7C,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjC,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAEjC,IAAI,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;YAC/B,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAChC,OAAO,EAAC,IAAI,0BAA2B,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAC,CAAC;SACjE;QAED,IAAI,EAAE,KAAK,eAAe,EAAE;YACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,EAAC,IAAI,2BAA4B,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAC,CAAC;SAC5D;QAED,OAAO,EAAC,IAAI,uBAAwB,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAC,CAAC;IACzD,CAAC;IAEO,2CAAuB,GAA/B;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1C,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE;YAClB,IAAI,GAAG,GAAG,8BAAa,CAAC,SAAS,CAAC,CAAC;YACnC,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnD,GAAG,IAAI,8BAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;aACjD;YAED,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;gBACrC,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3B;YAED,IAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAEvC,IAAI,YAAY,KAAK,CAAC,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,YAAY,GAAG,QAAQ,EAAE;gBACrF,OAAO,qBAAqB,CAAC;aAChC;YAED,OAAO,YAAY,CAAC;SACvB;QAED,IAAI,SAAS,KAAK,GAAG,EAAE;YACnB,OAAO,qBAAqB,CAAC;SAChC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,+BAAW,GAAnB;QACI,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,OAAO,IAAI,EAAE;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1C,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC5B,MAAM,IAAI,8BAAa,CAAC,SAAS,CAAC,CAAC;aACtC;iBAAM,IAAI,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxD,MAAM,IAAI,8BAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;aAC3D;iBAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC;aACjB;SACJ;IACL,CAAC;IACL,gBAAC;AAAD,CAAC,AA7eD,IA6eC;AA7eY,8BAAS"}