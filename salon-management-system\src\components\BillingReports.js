import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  Assessment as ReportIcon,
  GetApp as ExportIcon,
  DateRange as DateIcon
} from '@mui/icons-material';
import { useBilling } from '../contexts/BillingContext';

const BillingReports = () => {
  const {
    invoices,
    getRevenueStats,
    getMostBookedServices,
    getStaffPerformance
  } = useBilling();

  const [currentTab, setCurrentTab] = useState(0);
  const [reportPeriod, setReportPeriod] = useState('month');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Calculate various statistics
  const revenueStats = getRevenueStats(reportPeriod);
  const mostBookedServices = getMostBookedServices();
  const staffPerformance = getStaffPerformance();

  // Calculate peak hours/days
  const getPeakAnalysis = () => {
    const hourStats = {};
    const dayStats = {};
    
    invoices.forEach(invoice => {
      const date = new Date(invoice.createdAt);
      const hour = date.getHours();
      const day = date.toLocaleDateString('en-US', { weekday: 'long' });
      
      hourStats[hour] = (hourStats[hour] || 0) + 1;
      dayStats[day] = (dayStats[day] || 0) + 1;
    });
    
    const peakHour = Object.entries(hourStats).reduce((a, b) => 
      hourStats[a[0]] > hourStats[b[0]] ? a : b, [0, 0]
    );
    
    const peakDay = Object.entries(dayStats).reduce((a, b) => 
      dayStats[a[0]] > dayStats[b[0]] ? a : b, ['Monday', 0]
    );
    
    return {
      peakHour: `${peakHour[0]}:00`,
      peakHourCount: peakHour[1],
      peakDay: peakDay[0],
      peakDayCount: peakDay[1],
      hourStats,
      dayStats
    };
  };

  const peakAnalysis = getPeakAnalysis();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );

  const generateMonthlyReport = () => {
    const monthlyData = {};
    
    invoices.forEach(invoice => {
      const date = new Date(invoice.date);
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
          revenue: 0,
          invoiceCount: 0,
          averageInvoice: 0
        };
      }
      
      if (invoice.status === 'paid') {
        monthlyData[monthKey].revenue += invoice.total;
      }
      monthlyData[monthKey].invoiceCount += 1;
    });
    
    Object.values(monthlyData).forEach(month => {
      month.averageInvoice = month.invoiceCount > 0 ? month.revenue / month.invoiceCount : 0;
      month.revenue = parseFloat(month.revenue.toFixed(2));
      month.averageInvoice = parseFloat(month.averageInvoice.toFixed(2));
    });
    
    return Object.values(monthlyData).sort((a, b) => new Date(a.month) - new Date(b.month));
  };

  const monthlyReport = generateMonthlyReport();

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          Reports & Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Period</InputLabel>
            <Select
              value={reportPeriod}
              onChange={(e) => setReportPeriod(e.target.value)}
              label="Period"
            >
              <MenuItem value="day">Today</MenuItem>
              <MenuItem value="week">This Week</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
          >
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Revenue ({reportPeriod})
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {formatCurrency(revenueStats.totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {revenueStats.totalInvoices} invoices
                  </Typography>
                </Box>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Average Invoice
                  </Typography>
                  <Typography variant="h4" color="primary.main">
                    {formatCurrency(revenueStats.averageInvoice)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Per transaction
                  </Typography>
                </Box>
                <MoneyIcon sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Peak Day
                  </Typography>
                  <Typography variant="h4" color="info.main">
                    {peakAnalysis.peakDay}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {peakAnalysis.peakDayCount} bookings
                  </Typography>
                </Box>
                <ScheduleIcon sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Peak Hour
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {peakAnalysis.peakHour}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {peakAnalysis.peakHourCount} bookings
                  </Typography>
                </Box>
                <DateIcon sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Revenue Analysis" />
          <Tab label="Service Performance" />
          <Tab label="Staff Performance" />
          <Tab label="Peak Hours Analysis" />
        </Tabs>
      </Paper>

      {/* Revenue Analysis Tab */}
      <TabPanel value={currentTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Monthly Revenue Trend
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Month</TableCell>
                      <TableCell align="right">Revenue</TableCell>
                      <TableCell align="right">Invoices</TableCell>
                      <TableCell align="right">Average Invoice</TableCell>
                      <TableCell align="right">Growth</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {monthlyReport.map((month, index) => {
                      const prevMonth = monthlyReport[index - 1];
                      const growth = prevMonth ? 
                        ((month.revenue - prevMonth.revenue) / prevMonth.revenue * 100).toFixed(1) : 0;
                      
                      return (
                        <TableRow key={month.month}>
                          <TableCell>{month.month}</TableCell>
                          <TableCell align="right">
                            <Typography variant="subtitle2" fontWeight="bold">
                              {formatCurrency(month.revenue)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">{month.invoiceCount}</TableCell>
                          <TableCell align="right">{formatCurrency(month.averageInvoice)}</TableCell>
                          <TableCell align="right">
                            {index > 0 && (
                              <Chip 
                                label={`${growth > 0 ? '+' : ''}${growth}%`}
                                color={growth > 0 ? 'success' : growth < 0 ? 'error' : 'default'}
                                size="small"
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Service Performance Tab */}
      <TabPanel value={currentTab} index={1}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Most Booked Services
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Service</TableCell>
                  <TableCell align="right">Bookings</TableCell>
                  <TableCell align="right">Revenue</TableCell>
                  <TableCell align="right">Avg. Price</TableCell>
                  <TableCell>Popularity</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mostBookedServices.map((service, index) => {
                  const maxCount = mostBookedServices[0]?.count || 1;
                  const popularity = (service.count / maxCount) * 100;
                  
                  return (
                    <TableRow key={service.name}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle2">
                            {service.name}
                          </Typography>
                          {index < 3 && (
                            <StarIcon sx={{ ml: 1, color: 'gold', fontSize: 16 }} />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          {service.count}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(service.revenue)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(service.revenue / service.count)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: 200 }}>
                          <LinearProgress
                            variant="determinate"
                            value={popularity}
                            sx={{ flex: 1, mr: 1 }}
                          />
                          <Typography variant="body2">
                            {popularity.toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      {/* Staff Performance Tab */}
      <TabPanel value={currentTab} index={2}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Staff Performance Analysis
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Staff Member</TableCell>
                  <TableCell align="right">Services</TableCell>
                  <TableCell align="right">Revenue</TableCell>
                  <TableCell align="right">Avg. Service Value</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Rating</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {staffPerformance.map((staff, index) => {
                  const maxRevenue = staffPerformance[0]?.revenue || 1;
                  const performance = (staff.revenue / maxRevenue) * 100;

                  return (
                    <TableRow key={staff.name}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {staff.name}
                          </Typography>
                          {index === 0 && (
                            <Chip
                              label="Top Performer"
                              color="success"
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2">
                          {staff.serviceCount}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          {formatCurrency(staff.revenue)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(staff.averageService)}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: 200 }}>
                          <LinearProgress
                            variant="determinate"
                            value={performance}
                            color={index === 0 ? 'success' : 'primary'}
                            sx={{ flex: 1, mr: 1 }}
                          />
                          <Typography variant="body2">
                            {performance.toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {[...Array(5)].map((_, i) => (
                            <StarIcon
                              key={i}
                              sx={{
                                fontSize: 16,
                                color: i < Math.floor(performance / 20) ? 'gold' : 'grey.300'
                              }}
                            />
                          ))}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      {/* Peak Hours Analysis Tab */}
      <TabPanel value={currentTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Peak Days Analysis
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Day</TableCell>
                      <TableCell align="right">Bookings</TableCell>
                      <TableCell>Activity</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(peakAnalysis.dayStats)
                      .sort(([,a], [,b]) => b - a)
                      .map(([day, count]) => {
                        const maxCount = Math.max(...Object.values(peakAnalysis.dayStats));
                        const activity = (count / maxCount) * 100;

                        return (
                          <TableRow key={day}>
                            <TableCell>
                              <Typography variant="subtitle2">
                                {day}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="subtitle2" fontWeight="bold">
                                {count}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', width: 150 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={activity}
                                  sx={{ flex: 1, mr: 1 }}
                                />
                                <Typography variant="body2">
                                  {activity.toFixed(0)}%
                                </Typography>
                              </Box>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Peak Hours Analysis
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Hour</TableCell>
                      <TableCell align="right">Bookings</TableCell>
                      <TableCell>Activity</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(peakAnalysis.hourStats)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 10)
                      .map(([hour, count]) => {
                        const maxCount = Math.max(...Object.values(peakAnalysis.hourStats));
                        const activity = (count / maxCount) * 100;

                        return (
                          <TableRow key={hour}>
                            <TableCell>
                              <Typography variant="subtitle2">
                                {hour}:00
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography variant="subtitle2" fontWeight="bold">
                                {count}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', width: 150 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={activity}
                                  sx={{ flex: 1, mr: 1 }}
                                />
                                <Typography variant="body2">
                                  {activity.toFixed(0)}%
                                </Typography>
                              </Box>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default BillingReports;
