import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Divider,
  Alert,
  Paper,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material';
import {
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  ContentCut as ServiceIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Notes as NotesIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useBooking } from '../contexts/BookingContext';

const BookingConfirmation = ({ onConfirm, onBack }) => {
  const {
    selectedService,
    selectedStylist,
    selectedDate,
    selectedTime,
    customerInfo,
    setCustomerInfo,
    createAppointment,
    resetBooking,
  } = useBooking();

  const [loading, setLoading] = useState(false);
  const [confirmationDialog, setConfirmationDialog] = useState(false);
  const [bookingComplete, setBookingComplete] = useState(false);
  const [newAppointment, setNewAppointment] = useState(null);
  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    // Apply field-specific restrictions
    let processedValue = value;

    if (field === 'phone') {
      // Only allow digits - remove all non-digit characters
      processedValue = value.replace(/[^\d]/g, '');

      // Limit to exactly 10 digits
      if (processedValue.length > 10) {
        processedValue = processedValue.substring(0, 10);
      }
    } else if (field === 'name') {
      // Limit name to 50 characters
      if (value.length > 50) {
        processedValue = value.substring(0, 50);
      }
    } else if (field === 'email') {
      // Remove leading/trailing spaces and limit email to 100 characters
      processedValue = value.trim();
      if (processedValue.length > 100) {
        processedValue = processedValue.substring(0, 100);
      }
    } else if (field === 'notes') {
      // Limit notes to 500 characters
      if (value.length > 500) {
        processedValue = value.substring(0, 500);
      }
    }

    setCustomerInfo({
      ...customerInfo,
      [field]: processedValue,
    });

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: '',
      });
    }
  };

  const handleConfirmBooking = () => {
    if (validateForm()) {
      setConfirmationDialog(true);
    }
  };

  const handleFinalConfirm = async () => {
    setLoading(true);
    setConfirmationDialog(false);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const appointmentData = {
        customer: customerInfo.name,
        phone: customerInfo.phone,
        email: customerInfo.email,
        service: selectedService.name,
        serviceId: selectedService.id,
        stylist: selectedStylist.name,
        stylistId: selectedStylist.id,
        date: selectedDate,
        time: selectedTime,
        duration: selectedService.duration,
        price: selectedService.price,
        notes: customerInfo.notes,
      };

      const appointment = createAppointment(appointmentData);
      setNewAppointment(appointment);
      setBookingComplete(true);

      if (onConfirm) {
        onConfirm(appointment);
      }
    } catch (error) {
      console.error('Booking failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNewBooking = () => {
    resetBooking();
    setBookingComplete(false);
    setNewAppointment(null);
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Validate form fields
  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!customerInfo.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (customerInfo.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long';
    }

    // Phone validation - only pure digits allowed
    if (!customerInfo.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else {
      // Check if phone contains only digits (no formatting allowed)
      if (!/^\d{10}$/.test(customerInfo.phone)) {
        if (!/^\d+$/.test(customerInfo.phone)) {
          newErrors.phone = 'Phone number can only contain digits (no spaces, hyphens, or other characters)';
        } else if (customerInfo.phone.length !== 10) {
          newErrors.phone = 'Phone number must be exactly 10 digits';
        }
      }
    }

    // Email validation (optional but if provided, must be valid)
    if (customerInfo.email.trim()) {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      const emailValue = customerInfo.email.trim();
      if (!emailRegex.test(emailValue)) {
        newErrors.email = 'Please enter a valid email address (e.g., <EMAIL>)';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isFormValid = () => {
    return customerInfo.name.trim() && customerInfo.phone.trim() && Object.keys(errors).length === 0;
  };

  if (bookingComplete && newAppointment) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
        <Typography variant="h4" gutterBottom color="success.main">
          Booking Confirmed!
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Your appointment has been successfully booked.
        </Typography>

        <Paper sx={{ p: 3, mb: 3, textAlign: 'left' }}>
          <Typography variant="h6" gutterBottom>
            Appointment Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Customer</Typography>
              <Typography variant="body1">{newAppointment.customer}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Phone</Typography>
              <Typography variant="body1">{newAppointment.phone}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Service</Typography>
              <Typography variant="body1">{newAppointment.service}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Stylist</Typography>
              <Typography variant="body1">{newAppointment.stylist}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Date & Time</Typography>
              <Typography variant="body1">
                {format(new Date(newAppointment.date), 'EEEE, MMMM d, yyyy')} at {newAppointment.time}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Total Price</Typography>
              <Typography variant="h6" color="success.main">${newAppointment.price}</Typography>
            </Grid>
          </Grid>
        </Paper>

        <Alert severity="info" sx={{ mb: 3 }}>
          Please arrive 10 minutes before your appointment time. You will receive a confirmation email shortly.
        </Alert>

        <Button
          variant="contained"
          size="large"
          onClick={handleNewBooking}
        >
          Book Another Appointment
        </Button>
      </Box>
    );
  }

  if (!selectedService || !selectedStylist || !selectedDate || !selectedTime) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please complete all previous steps before confirming your booking.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Confirm Your Booking
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Please review your appointment details and provide your contact information
      </Typography>

      <Grid container spacing={3}>
        {/* Appointment Summary */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" gutterBottom>
              Appointment Summary
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ServiceIcon sx={{ mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="subtitle1">{selectedService.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedService.description}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1">{selectedStylist.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Rating: {selectedStylist.rating}/5
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CalendarIcon sx={{ mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="subtitle1">
                    {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    at {selectedTime}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ScheduleIcon sx={{ mr: 2, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  {selectedService.duration} minutes
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Total Price:</Typography>
                <Typography variant="h5" color="success.main">
                  ${selectedService.price}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Customer Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Your Information
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Full Name"
                  value={customerInfo.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                  error={!!errors.name}
                  helperText={errors.name || `${customerInfo.name.length}/50 characters`}
                  InputProps={{
                    startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={customerInfo.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  required
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  error={!!errors.phone}
                  helperText={errors.phone || `Enter exactly 10 digits only (e.g., 9876543210) - ${customerInfo.phone.length}/10`}
                  InputProps={{
                    startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Email Address (Optional)"
                  type="email"
                  value={customerInfo.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  error={!!errors.email}
                  helperText={errors.email || `Optional: We'll send confirmation to this email (${customerInfo.email.length}/100)`}
                  InputProps={{
                    startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Special Notes (Optional)"
                  multiline
                  rows={3}
                  value={customerInfo.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any special requests or notes for your stylist..."
                  helperText={`${customerInfo.notes.length}/500 characters`}
                  InputProps={{
                    startAdornment: <NotesIcon sx={{ mr: 1, color: 'text.secondary', alignSelf: 'flex-start', mt: 1 }} />,
                  }}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Navigation Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="outlined"
          size="large"
          onClick={handleBack}
        >
          Back: Select Time
        </Button>
        <Button
          variant="contained"
          size="large"
          onClick={handleConfirmBooking}
          disabled={!isFormValid()}
        >
          Confirm Booking
        </Button>
      </Box>

      {/* Validation Summary */}
      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Please fix the following errors:
          </Typography>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            {Object.entries(errors).map(([field, error]) => (
              <li key={field}>
                <Typography variant="body2">{error}</Typography>
              </li>
            ))}
          </ul>
        </Alert>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={confirmationDialog} onClose={() => setConfirmationDialog(false)}>
        <DialogTitle>Confirm Your Appointment</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to book this appointment?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Service:</strong> {selectedService.name}<br />
            <strong>Stylist:</strong> {selectedStylist.name}<br />
            <strong>Date & Time:</strong> {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')} at {selectedTime}<br />
            <strong>Price:</strong> ${selectedService.price}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmationDialog(false)}>Cancel</Button>
          <Button onClick={handleFinalConfirm} variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={20} /> : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BookingConfirmation;
