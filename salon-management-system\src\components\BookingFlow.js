import React from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Typography,
} from '@mui/material';
import { useBooking } from '../contexts/BookingContext';
import ServiceSelection from './ServiceSelection';
import StylistSelection from './StylistSelection';
import TimeSlotSelection from './TimeSlotSelection';
import BookingConfirmation from './BookingConfirmation';

const steps = [
  'Select Service',
  'Choose Stylist',
  'Pick Date & Time',
  'Confirm Booking',
];

const BookingFlow = ({ onBookingComplete }) => {
  const {
    bookingStep,
    setBookingStep,
    setSelectedService,
    setStylist,
    setSelectedDate,
    setSelectedTime,
  } = useBooking();

  const handleServiceSelect = (service) => {
    setSelectedService(service);
  };

  const handleStylistSelect = (stylist) => {
    setStylist(stylist);
  };

  const handleTimeSelect = (date, time) => {
    if (date) setSelectedDate(date);
    if (time) setSelectedTime(time);
  };

  const handleNext = () => {
    setBookingStep(bookingStep + 1);
  };

  const handleBack = () => {
    setBookingStep(bookingStep - 1);
  };

  const handleBookingComplete = (appointment) => {
    if (onBookingComplete) {
      onBookingComplete(appointment);
    }
  };

  const renderStepContent = () => {
    switch (bookingStep) {
      case 0:
        return (
          <ServiceSelection
            onServiceSelect={handleServiceSelect}
            onNext={handleNext}
          />
        );
      case 1:
        return (
          <StylistSelection
            onStylistSelect={handleStylistSelect}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 2:
        return (
          <TimeSlotSelection
            onTimeSelect={handleTimeSelect}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      case 3:
        return (
          <BookingConfirmation
            onConfirm={handleBookingComplete}
            onBack={handleBack}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Progress Stepper */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Book Your Appointment
        </Typography>
        <Stepper activeStep={bookingStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* Step Content */}
      <Paper sx={{ minHeight: '600px' }}>
        {renderStepContent()}
      </Paper>
    </Box>
  );
};

export default BookingFlow;
