import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Alert,
  CircularProgress,
  Grid,
  Divider,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Event as EventIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useBooking } from '../contexts/BookingContext';

const CancelBookingDialog = ({ open, onClose, appointment, onCancel }) => {
  const { cancelAppointment } = useBooking();
  const [loading, setLoading] = useState(false);
  const [reason, setReason] = useState('');
  const [confirmed, setConfirmed] = useState(false);

  const handleCancel = async () => {
    setLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Cancel the appointment
      cancelAppointment(appointment.id);
      
      if (onCancel) {
        onCancel(appointment, reason);
      }
      
      handleClose();
    } catch (error) {
      console.error('Cancell<PERSON> failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setReason('');
    setConfirmed(false);
    setLoading(false);
    onClose();
  };

  if (!appointment) return null;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
        <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
        Cancel Appointment
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Are you sure you want to cancel this appointment? This action cannot be undone.
        </Alert>

        {/* Appointment Details */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Appointment Details
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Customer
                </Typography>
              </Box>
              <Typography variant="body1">{appointment.customer}</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Service
                </Typography>
              </Box>
              <Typography variant="body1">{appointment.service}</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Stylist
                </Typography>
              </Box>
              <Typography variant="body1">{appointment.stylist}</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Date & Time
                </Typography>
              </Box>
              <Typography variant="body1">
                {format(new Date(appointment.date), 'MMM d, yyyy')} at {appointment.time}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Price
                </Typography>
              </Box>
              <Typography variant="body1">${appointment.price}</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Duration
                </Typography>
              </Box>
              <Typography variant="body1">{appointment.duration} minutes</Typography>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Cancellation Reason */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Reason for Cancellation (Optional)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Please provide a reason for cancelling this appointment..."
            variant="outlined"
          />
        </Box>

        {/* Cancellation Policy */}
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Cancellation Policy:</strong><br />
            • Cancellations made 24+ hours in advance: Full refund<br />
            • Cancellations made 2-24 hours in advance: 50% refund<br />
            • Cancellations made less than 2 hours in advance: No refund
          </Typography>
        </Alert>

        {/* Confirmation Checkbox */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
          <input
            type="checkbox"
            id="confirm-cancel"
            checked={confirmed}
            onChange={(e) => setConfirmed(e.target.checked)}
            style={{ marginRight: 8 }}
          />
          <label htmlFor="confirm-cancel">
            <Typography variant="body2">
              I understand the cancellation policy and want to proceed with cancelling this appointment.
            </Typography>
          </label>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={loading}>
          Keep Appointment
        </Button>
        <Button
          onClick={handleCancel}
          color="error"
          variant="contained"
          disabled={!confirmed || loading}
          startIcon={loading ? <CircularProgress size={16} /> : null}
        >
          {loading ? 'Cancelling...' : 'Cancel Appointment'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CancelBookingDialog;
