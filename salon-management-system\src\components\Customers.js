import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  InputAdornment,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Rating,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Checkbox,
  OutlinedInput,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  History as HistoryIcon,
  Feedback as FeedbackIcon,
  Star as StarIcon,
  Event as EventIcon,
  Person as PersonIcon,
  ExpandMore as ExpandMoreIcon,
  Notifications as NotificationsIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Favorite as FavoriteIcon,
} from '@mui/icons-material';

const Customers = () => {
  const [customers, setCustomers] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Main St, City, State 12345',
      joinDate: '2023-06-15',
      totalVisits: 12,
      totalSpent: 1240,
      lastVisit: '2024-01-10',
      preferredStylist: 'Emma Wilson',
      preferredServices: ['Hair Cut & Style', 'Hair Color'],
      notes: 'Prefers natural hair colors',
      rating: 4.8,
      loyaltyPoints: 124,
      birthday: '1990-03-15',
      notifications: {
        sms: true,
        email: true,
        push: false
      },
      appointmentHistory: [
        { id: 1, date: '2024-01-10', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 5, feedback: 'Excellent service as always!' },
        { id: 2, date: '2023-12-15', service: 'Hair Color', stylist: 'Emma Wilson', price: 150, rating: 5, feedback: 'Love the new color!' },
        { id: 3, date: '2023-11-20', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 4, feedback: 'Great cut, very professional' }
      ]
    },
    {
      id: 2,
      name: 'Mike Davis',
      email: '<EMAIL>',
      phone: '(*************',
      address: '456 Oak Ave, City, State 12345',
      joinDate: '2023-08-22',
      totalVisits: 8,
      totalSpent: 320,
      lastVisit: '2024-01-15',
      preferredStylist: 'John Smith',
      preferredServices: ['Beard Trim', 'Hair Cut & Style'],
      notes: 'Regular beard trim every 2 weeks',
      rating: 4.5,
      loyaltyPoints: 32,
      birthday: '1985-07-22',
      notifications: {
        sms: true,
        email: false,
        push: true
      },
      appointmentHistory: [
        { id: 4, date: '2024-01-15', service: 'Beard Trim', stylist: 'John Smith', price: 35, rating: 4, feedback: 'Quick and efficient' },
        { id: 5, date: '2024-01-01', service: 'Hair Cut & Style', stylist: 'John Smith', price: 85, rating: 5, feedback: 'Perfect for the new year!' }
      ]
    },
    {
      id: 3,
      name: 'Lisa Brown',
      email: '<EMAIL>',
      phone: '(*************',
      address: '789 Pine St, City, State 12345',
      joinDate: '2023-03-10',
      totalVisits: 18,
      totalSpent: 2150,
      lastVisit: '2024-01-12',
      preferredStylist: 'Emma Wilson',
      preferredServices: ['Hair Color', 'Hair Cut & Style', 'Styling'],
      notes: 'Allergic to certain hair dyes',
      rating: 4.9,
      loyaltyPoints: 215,
      birthday: '1988-11-03',
      notifications: {
        sms: true,
        email: true,
        push: true
      },
      appointmentHistory: [
        { id: 6, date: '2024-01-12', service: 'Hair Color', stylist: 'Emma Wilson', price: 150, rating: 5, feedback: 'Amazing color transformation!' },
        { id: 7, date: '2023-12-20', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 5, feedback: 'Always satisfied with Emma\'s work' }
      ]
    },
    {
      id: 4,
      name: 'Tom Wilson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '321 Elm St, City, State 12345',
      joinDate: '2023-11-05',
      totalVisits: 5,
      totalSpent: 450,
      lastVisit: '2024-01-08',
      preferredStylist: 'Mike Johnson',
      preferredServices: ['Full Service'],
      notes: 'Prefers appointments in the afternoon',
      rating: 4.6,
      loyaltyPoints: 45,
      birthday: '1992-05-18',
      notifications: {
        sms: false,
        email: true,
        push: false
      },
      appointmentHistory: [
        { id: 8, date: '2024-01-08', service: 'Full Service', stylist: 'Mike Johnson', price: 120, rating: 5, feedback: 'Comprehensive service, very happy!' }
      ]
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerDetailOpen, setCustomerDetailOpen] = useState(false);
  const [feedbackOpen, setFeedbackOpen] = useState(false);
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [errors, setErrors] = useState({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    preferredStylist: '',
    preferredServices: [],
    notes: '',
    birthday: '',
    notifications: {
      sms: true,
      email: true,
      push: false
    }
  });

  const stylists = [
    'Emma Wilson',
    'John Smith',
    'Mike Johnson',
    'Sarah Davis',
    'Lisa Anderson',
  ];

  const services = [
    'Hair Cut & Style',
    'Hair Color',
    'Beard Trim',
    'Full Service',
    'Manicure',
    'Pedicure',
    'Styling',
    'Nail Art'
  ];

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm);

    const matchesFilter = filterBy === 'all' ||
      (filterBy === 'vip' && customer.totalSpent > 1000) ||
      (filterBy === 'new' && new Date(customer.joinDate) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)) ||
      (filterBy === 'inactive' && new Date(customer.lastVisit) < new Date(Date.now() - 90 * 24 * 60 * 60 * 1000));

    return matchesSearch && matchesFilter;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'visits':
        return b.totalVisits - a.totalVisits;
      case 'spent':
        return b.totalSpent - a.totalSpent;
      case 'rating':
        return b.rating - a.rating;
      case 'lastVisit':
        return new Date(b.lastVisit) - new Date(a.lastVisit);
      default:
        return 0;
    }
  });

  const handleOpen = (customer = null) => {
    if (customer) {
      setEditingCustomer(customer);
      setFormData({
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        preferredStylist: customer.preferredStylist,
        preferredServices: customer.preferredServices || [],
        notes: customer.notes,
        birthday: customer.birthday || '',
        notifications: customer.notifications || {
          sms: true,
          email: true,
          push: false
        }
      });
    } else {
      setEditingCustomer(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        preferredStylist: '',
        preferredServices: [],
        notes: '',
        birthday: '',
        notifications: {
          sms: true,
          email: true,
          push: false
        }
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingCustomer(null);
    setErrors({});
  };

  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    // Clean and prepare data
    const cleanedData = {
      ...formData,
      name: formData.name.trim(),
      email: formData.email.trim().toLowerCase(),
      phone: formData.phone.trim(),
      address: formData.address.trim(),
      notes: formData.notes.trim()
    };

    if (editingCustomer) {
      setCustomers(customers.map(customer =>
        customer.id === editingCustomer.id
          ? { ...customer, ...cleanedData }
          : customer
      ));
    } else {
      const newCustomer = {
        ...cleanedData,
        id: Math.max(...customers.map(c => c.id)) + 1,
        joinDate: new Date().toISOString().split('T')[0],
        totalVisits: 0,
        totalSpent: 0,
        lastVisit: 'Never',
        rating: 0,
        loyaltyPoints: 0,
        appointmentHistory: []
      };
      setCustomers([...customers, newCustomer]);
    }
    handleClose();
  };

  const handleCustomerDetail = (customer) => {
    setSelectedCustomer(customer);
    setCustomerDetailOpen(true);
  };

  const handleFeedbackOpen = (customer) => {
    setSelectedCustomer(customer);
    setFeedbackOpen(true);
  };

  const handleDeleteClick = (customer) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (customerToDelete) {
      setCustomers(customers.filter(customer => customer.id !== customerToDelete.id));
    }
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    const phoneRegex = /^[\d\s\-()\\+]+$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number';
    } else if (formData.phone.trim().length < 10) {
      newErrors.phone = 'Phone number must be at least 10 digits';
    }

    // Birthday validation (optional but if provided, should be valid)
    if (formData.birthday) {
      const birthDate = new Date(formData.birthday);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (birthDate > today) {
        newErrors.birthday = 'Birthday cannot be in the future';
      } else if (age > 120) {
        newErrors.birthday = 'Please enter a valid birth date';
      }
    }

    // Address validation (optional but if provided, should have minimum length)
    if (formData.address && formData.address.trim().length < 5) {
      newErrors.address = 'Address must be at least 5 characters if provided';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getCustomerInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCustomerStatus = (customer) => {
    const daysSinceLastVisit = Math.floor((new Date() - new Date(customer.lastVisit)) / (1000 * 60 * 60 * 24));
    if (customer.lastVisit === 'Never') return 'new';
    if (daysSinceLastVisit > 90) return 'inactive';
    if (customer.totalSpent > 1000) return 'vip';
    return 'active';
  };

  const customerStats = {
    total: customers.length,
    newThisMonth: customers.filter(c => {
      const joinDate = new Date(c.joinDate);
      const now = new Date();
      return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
    }).length,
    activeCustomers: customers.filter(c => c.lastVisit !== 'Never').length,
    vipCustomers: customers.filter(c => c.totalSpent > 1000).length,
    averageRating: customers.reduce((sum, c) => sum + (c.rating || 0), 0) / customers.length,
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0)
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Customer Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Customer
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab
            label="Customer List"
            icon={<PersonIcon />}
            iconPosition="start"
          />
          <Tab
            label="Customer Analytics"
            icon={<TrendingUpIcon />}
            iconPosition="start"
          />
        </Tabs>
      </Box>

      {currentTab === 0 && (
        <>
          {/* Customer Stats */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <PersonIcon />
                    </Avatar>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Total Customers
                      </Typography>
                      <Typography variant="h4">
                        {customerStats.total}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <TrendingUpIcon />
                    </Avatar>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        New This Month
                      </Typography>
                      <Typography variant="h4" color="primary.main">
                        {customerStats.newThisMonth}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'warning.main' }}>
                      <FavoriteIcon />
                    </Avatar>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        VIP Customers
                      </Typography>
                      <Typography variant="h4" color="warning.main">
                        {customerStats.vipCustomers}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'info.main' }}>
                      <StarIcon />
                    </Avatar>
                    <Box>
                      <Typography color="textSecondary" gutterBottom>
                        Avg Rating
                      </Typography>
                      <Typography variant="h4" color="info.main">
                        {customerStats.averageRating.toFixed(1)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Search and Filters */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search customers by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Filter By</InputLabel>
                <Select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  label="Filter By"
                >
                  <MenuItem value="all">All Customers</MenuItem>
                  <MenuItem value="vip">VIP Customers</MenuItem>
                  <MenuItem value="new">New Customers</MenuItem>
                  <MenuItem value="inactive">Inactive Customers</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  label="Sort By"
                >
                  <MenuItem value="name">Name</MenuItem>
                  <MenuItem value="visits">Total Visits</MenuItem>
                  <MenuItem value="spent">Total Spent</MenuItem>
                  <MenuItem value="rating">Rating</MenuItem>
                  <MenuItem value="lastVisit">Last Visit</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </>
      )}

      {currentTab === 0 && (
        <>
          {/* Customers Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Customer</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Stats</TableCell>
                  <TableCell>Rating</TableCell>
                  <TableCell>Preferred Stylist</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Badge
                          badgeContent={customer.loyaltyPoints}
                          color="primary"
                          max={999}
                        >
                          <Avatar sx={{ bgcolor: getCustomerStatus(customer) === 'vip' ? 'gold' : 'primary.main' }}>
                            {getCustomerInitials(customer.name)}
                          </Avatar>
                        </Badge>
                        <Box>
                          <Typography variant="subtitle2">
                            {customer.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Member since {customer.joinDate}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <EmailIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {customer.email}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <PhoneIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {customer.phone}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          <strong>{customer.totalVisits}</strong> visits
                        </Typography>
                        <Typography variant="body2" color="success.main">
                          {formatCurrency(customer.totalSpent)} spent
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Last: {customer.lastVisit}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Rating value={customer.rating} readOnly size="small" />
                        <Typography variant="body2">
                          ({customer.rating})
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {customer.preferredStylist}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {customer.preferredServices?.slice(0, 2).join(', ')}
                        {customer.preferredServices?.length > 2 && '...'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getCustomerStatus(customer)}
                        color={
                          getCustomerStatus(customer) === 'vip' ? 'warning' :
                          getCustomerStatus(customer) === 'active' ? 'success' :
                          getCustomerStatus(customer) === 'new' ? 'info' : 'default'
                        }
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View History">
                        <IconButton
                          size="small"
                          onClick={() => handleCustomerDetail(customer)}
                          color="info"
                        >
                          <HistoryIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Add Feedback">
                        <IconButton
                          size="small"
                          onClick={() => handleFeedbackOpen(customer)}
                          color="warning"
                        >
                          <FeedbackIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Customer">
                        <IconButton
                          size="small"
                          onClick={() => handleOpen(customer)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Customer">
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteClick(customer)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      {/* Customer Analytics Tab */}
      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Revenue Analytics
                </Typography>
                <Typography variant="h4" color="success.main">
                  {formatCurrency(customerStats.totalRevenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Revenue from All Customers
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Customer Satisfaction
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Rating value={customerStats.averageRating} readOnly />
                  <Typography variant="h5">
                    {customerStats.averageRating.toFixed(1)}/5
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Average Customer Rating
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Customers
                </Typography>
                <List>
                  {customers
                    .sort((a, b) => b.totalSpent - a.totalSpent)
                    .slice(0, 5)
                    .map((customer, index) => (
                      <ListItem key={customer.id}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: index === 0 ? 'gold' : 'primary.main' }}>
                            {index + 1}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={customer.name}
                          secondary={`${formatCurrency(customer.totalSpent)} • ${customer.totalVisits} visits • ${customer.rating}★`}
                        />
                        <Chip
                          label={getCustomerStatus(customer)}
                          color={getCustomerStatus(customer) === 'vip' ? 'warning' : 'primary'}
                          size="small"
                        />
                      </ListItem>
                    ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Customer Detail Dialog */}
      <Dialog
        open={customerDetailOpen}
        onClose={() => setCustomerDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar>
              {selectedCustomer && getCustomerInitials(selectedCustomer.name)}
            </Avatar>
            <Box>
              <Typography variant="h6">
                {selectedCustomer?.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Customer History & Preferences
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedCustomer && (
            <Box>
              {/* Customer Summary */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" color="primary">
                        {selectedCustomer.totalVisits}
                      </Typography>
                      <Typography variant="caption">
                        Total Visits
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" color="success.main">
                        {formatCurrency(selectedCustomer.totalSpent)}
                      </Typography>
                      <Typography variant="caption">
                        Total Spent
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" color="warning.main">
                        {selectedCustomer.loyaltyPoints}
                      </Typography>
                      <Typography variant="caption">
                        Loyalty Points
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Rating value={selectedCustomer.rating} readOnly size="small" />
                      <Typography variant="caption" display="block">
                        Average Rating
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Preferences */}
              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6">Preferences</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Preferred Stylist
                      </Typography>
                      <Chip
                        label={selectedCustomer.preferredStylist}
                        color="primary"
                        icon={<PersonIcon />}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Preferred Services
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {selectedCustomer.preferredServices?.map((service, index) => (
                          <Chip
                            key={index}
                            label={service}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>
                        Notes
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedCustomer.notes || 'No special notes'}
                      </Typography>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>

              {/* Appointment History */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6">Appointment History</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List>
                    {selectedCustomer.appointmentHistory?.map((appointment) => (
                      <ListItem key={appointment.id} divider>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <EventIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography variant="subtitle2">
                                {appointment.service}
                              </Typography>
                              <Typography variant="h6" color="success.main">
                                {formatCurrency(appointment.price)}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2">
                                {appointment.date} • {appointment.stylist}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                <Rating value={appointment.rating} readOnly size="small" />
                                <Typography variant="body2" color="text.secondary">
                                  "{appointment.feedback}"
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCustomerDetailOpen(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              setCustomerDetailOpen(false);
              handleOpen(selectedCustomer);
            }}
          >
            Edit Customer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Feedback Dialog */}
      <Dialog
        open={feedbackOpen}
        onClose={() => setFeedbackOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Feedback for {selectedCustomer?.name}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Service Rating
            </Typography>
            <Rating
              size="large"
              defaultValue={5}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Feedback Comments"
              multiline
              rows={4}
              placeholder="Share your experience with this customer's service..."
              sx={{ mb: 2 }}
            />
            <Alert severity="info">
              This feedback will be added to the customer's history and help improve service quality.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFeedbackOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={() => setFeedbackOpen(false)}>
            Submit Feedback
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                error={!!errors.email}
                helperText={errors.email}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                required
                error={!!errors.phone}
                helperText={errors.phone}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Birthday"
                type="date"
                value={formData.birthday}
                onChange={(e) => handleInputChange('birthday', e.target.value)}
                slotProps={{
                  inputLabel: { shrink: true }
                }}
                error={!!errors.birthday}
                helperText={errors.birthday}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Preferred Stylist"
                select
                value={formData.preferredStylist}
                onChange={(e) => handleInputChange('preferredStylist', e.target.value)}
              >
                <MenuItem value="">Select a stylist</MenuItem>
                {stylists.map((stylist) => (
                  <MenuItem key={stylist} value={stylist}>
                    {stylist}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Preferred Services</InputLabel>
                <Select
                  multiple
                  value={formData.preferredServices}
                  onChange={(e) => handleInputChange('preferredServices', e.target.value)}
                  input={<OutlinedInput label="Preferred Services" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {services.map((service) => (
                    <MenuItem key={service} value={service}>
                      <Checkbox checked={formData.preferredServices.indexOf(service) > -1} />
                      <ListItemText primary={service} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                multiline
                rows={2}
                error={!!errors.address}
                helperText={errors.address}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                multiline
                rows={3}
                placeholder="Any special preferences, allergies, or notes..."
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Notification Preferences
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Checkbox
                    checked={formData.notifications.sms}
                    onChange={(e) => handleInputChange('notifications', {
                      ...formData.notifications,
                      sms: e.target.checked
                    })}
                  />
                  <Typography>SMS</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Checkbox
                    checked={formData.notifications.email}
                    onChange={(e) => handleInputChange('notifications', {
                      ...formData.notifications,
                      email: e.target.checked
                    })}
                  />
                  <Typography>Email</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Checkbox
                    checked={formData.notifications.push}
                    onChange={(e) => handleInputChange('notifications', {
                      ...formData.notifications,
                      push: e.target.checked
                    })}
                  />
                  <Typography>Push Notifications</Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingCustomer ? 'Update' : 'Add Customer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: 'error.main' }}>
          Confirm Delete Customer
        </DialogTitle>
        <DialogContent>
          {customerToDelete && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Are you sure you want to delete the customer "{customerToDelete.name}"?
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                <strong>Email:</strong> {customerToDelete.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                <strong>Phone:</strong> {customerToDelete.phone}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                <strong>Total Visits:</strong> {customerToDelete.totalVisits}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Total Spent:</strong> ${customerToDelete.totalSpent}
              </Typography>
              <Typography variant="body2" color="error.main" sx={{ fontWeight: 'bold' }}>
                This action cannot be undone. All customer data, appointment history, and feedback will be permanently deleted.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            Delete Customer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Customers;
