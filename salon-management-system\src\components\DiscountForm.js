import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  InputAdornment,
  Switch,
  FormControlLabel
} from '@mui/material';
import { useBilling } from '../contexts/BillingContext';

const DiscountForm = ({ open, onClose, discount = null, mode = 'add' }) => {
  const { createDiscount, updateDiscount } = useBilling();
  
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    type: 'percentage',
    value: 0,
    maxDiscount: 0,
    validFrom: '',
    validTo: '',
    usageLimit: 100,
    description: '',
    status: 'active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (discount && mode === 'edit') {
      setFormData({
        code: discount.code || '',
        name: discount.name || '',
        type: discount.type || 'percentage',
        value: discount.value || 0,
        maxDiscount: discount.maxDiscount || 0,
        validFrom: discount.validFrom || '',
        validTo: discount.validTo || '',
        usageLimit: discount.usageLimit || 100,
        description: discount.description || '',
        status: discount.status || 'active'
      });
    } else {
      // Reset form for add mode
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      
      setFormData({
        code: '',
        name: '',
        type: 'percentage',
        value: 0,
        maxDiscount: 0,
        validFrom: today,
        validTo: nextMonth.toISOString().split('T')[0],
        usageLimit: 100,
        description: '',
        status: 'active'
      });
    }
    setErrors({});
  }, [discount, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Discount code is required';
    } else if (formData.code.length < 3) {
      newErrors.code = 'Code must be at least 3 characters';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Discount name is required';
    }

    if (formData.value <= 0) {
      newErrors.value = 'Discount value must be greater than 0';
    }

    if (formData.type === 'percentage' && formData.value > 100) {
      newErrors.value = 'Percentage cannot exceed 100%';
    }

    if (formData.minAmount < 0) {
      newErrors.minAmount = 'Minimum amount cannot be negative';
    }

    if (formData.maxDiscount < 0) {
      newErrors.maxDiscount = 'Maximum discount cannot be negative';
    }

    if (!formData.validFrom) {
      newErrors.validFrom = 'Valid from date is required';
    }

    if (!formData.validTo) {
      newErrors.validTo = 'Valid to date is required';
    }

    if (formData.validFrom && formData.validTo && formData.validFrom >= formData.validTo) {
      newErrors.validTo = 'End date must be after start date';
    }

    if (formData.usageLimit <= 0) {
      newErrors.usageLimit = 'Usage limit must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const discountData = {
        ...formData,
        code: formData.code.toUpperCase(),
        value: Number(formData.value),
        minAmount: Number(formData.minAmount),
        maxDiscount: Number(formData.maxDiscount),
        usageLimit: Number(formData.usageLimit)
      };

      if (mode === 'edit' && discount) {
        updateDiscount(discount.id, discountData);
      } else {
        createDiscount(discountData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving discount:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {mode === 'edit' ? 'Edit Discount' : 'Create New Discount'}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Discount Code"
                value={formData.code}
                onChange={handleChange('code')}
                error={!!errors.code}
                helperText={errors.code || 'e.g., SUMMER20, WELCOME10'}
                required
                inputProps={{ style: { textTransform: 'uppercase' } }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Discount Name"
                value={formData.name}
                onChange={handleChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleChange('description')}
                multiline
                rows={2}
                placeholder="Brief description of the discount"
              />
            </Grid>

            {/* Discount Configuration */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Discount Configuration
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <FormControl fullWidth error={!!errors.type} required>
                <InputLabel>Discount Type</InputLabel>
                <Select
                  value={formData.type}
                  onChange={handleChange('type')}
                  label="Discount Type"
                >
                  <MenuItem value="percentage">Percentage</MenuItem>
                  <MenuItem value="fixed">Fixed Amount</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Discount Value"
                type="number"
                value={formData.value}
                onChange={handleChange('value')}
                error={!!errors.value}
                helperText={errors.value}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {formData.type === 'percentage' ? '%' : '$'}
                    </InputAdornment>
                  ),
                }}
                inputProps={{ min: 0, step: formData.type === 'percentage' ? 1 : 0.01 }}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Maximum Discount"
                type="number"
                value={formData.maxDiscount}
                onChange={handleChange('maxDiscount')}
                error={!!errors.maxDiscount}
                helperText={errors.maxDiscount || 'Maximum discount amount (for percentage type)'}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Usage Limit"
                type="number"
                value={formData.usageLimit}
                onChange={handleChange('usageLimit')}
                error={!!errors.usageLimit}
                helperText={errors.usageLimit || 'Maximum number of times this discount can be used'}
                inputProps={{ min: 1 }}
                required
              />
            </Grid>

            {/* Validity Period */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Validity Period
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Valid From"
                type="date"
                value={formData.validFrom}
                onChange={handleChange('validFrom')}
                error={!!errors.validFrom}
                helperText={errors.validFrom}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Valid To"
                type="date"
                value={formData.validTo}
                onChange={handleChange('validTo')}
                error={!!errors.validTo}
                helperText={errors.validTo}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.status === 'active'}
                    onChange={(e) => handleChange('status')({ target: { value: e.target.checked ? 'active' : 'inactive' } })}
                  />
                }
                label="Active"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Discount' : 'Create Discount')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DiscountForm;
