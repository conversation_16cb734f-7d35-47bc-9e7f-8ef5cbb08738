import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Alert,
  AlertTitle,
  Collapse,
  Badge,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ShoppingCart as RestockIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useInventory } from '../contexts/InventoryContext';

const InventoryAlerts = ({ showInDashboard = false }) => {
  const {
    getLowStockProducts,
    getOutOfStockProducts,
    getExpiringProducts,
    addStockMovement
  } = useInventory();

  const [dismissedAlerts, setDismissedAlerts] = useState(new Set());
  const [expandedSections, setExpandedSections] = useState({
    lowStock: true,
    outOfStock: true,
    expiring: true
  });
  const [restockDialogOpen, setRestockDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [restockQuantity, setRestockQuantity] = useState('');
  const [restockReason, setRestockReason] = useState('Regular restock');

  const lowStockProducts = getLowStockProducts().filter(p => !dismissedAlerts.has(`low-${p.id}`));
  const outOfStockProducts = getOutOfStockProducts().filter(p => !dismissedAlerts.has(`out-${p.id}`));
  const expiringProducts = getExpiringProducts().filter(p => !dismissedAlerts.has(`exp-${p.id}`));

  const totalAlerts = lowStockProducts.length + outOfStockProducts.length + expiringProducts.length;

  const handleDismissAlert = (alertId) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]));
  };

  const handleToggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleRestockClick = (product) => {
    setSelectedProduct(product);
    setRestockQuantity(Math.max(product.maxStockLevel - product.currentStock, 0).toString());
    setRestockDialogOpen(true);
  };

  const handleRestockSubmit = () => {
    if (selectedProduct && restockQuantity) {
      const quantity = parseInt(restockQuantity);

      // Add stock movement
      addStockMovement({
        productId: selectedProduct.id,
        type: 'restock',
        quantity: quantity,
        reason: restockReason,
        performedBy: 'Admin', // In a real app, this would be the current user
        notes: `Restocked ${quantity} units`
      });

      // Dismiss the alert
      handleDismissAlert(`low-${selectedProduct.id}`);
      handleDismissAlert(`out-${selectedProduct.id}`);

      setRestockDialogOpen(false);
      setSelectedProduct(null);
      setRestockQuantity('');
      setRestockReason('Regular restock');
    }
  };

  const AlertSection = ({ title, items, type, icon, color, onItemAction }) => {
    const isExpanded = expandedSections[type];
    
    if (items.length === 0) return null;

    return (
      <Paper sx={{ mb: 2 }}>
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            bgcolor: `${color}.50`
          }}
          onClick={() => handleToggleSection(type)}
        >
          <Badge badgeContent={items.length} color={color}>
            {icon}
          </Badge>
          <Typography variant="h6" sx={{ ml: 2, flex: 1 }}>
            {title}
          </Typography>
          {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </Box>
        
        <Collapse in={isExpanded}>
          <List>
            {items.map((item, index) => (
              <ListItem key={`${type}-${item.id}`} divider={index < items.length - 1}>
                <ListItemIcon>
                  {React.cloneElement(icon, { color: color })}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {item.name}
                      </Typography>
                      <Chip 
                        label={item.sku} 
                        size="small" 
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        {item.brand} • {item.category}
                      </Typography>
                      {type === 'lowStock' && (
                        <Typography variant="body2" color="warning.main">
                          Current: {item.currentStock} • Min: {item.minStockLevel}
                        </Typography>
                      )}
                      {type === 'outOfStock' && (
                        <Typography variant="body2" color="error.main">
                          Out of stock • Last restocked: {item.lastRestocked}
                        </Typography>
                      )}
                      {type === 'expiring' && (
                        <Typography variant="body2" color="info.main">
                          Expires: {item.expiryDate} • Stock: {item.currentStock}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {(type === 'lowStock' || type === 'outOfStock') && (
                      <Tooltip title="Restock">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleRestockClick(item)}
                        >
                          <RestockIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Dismiss Alert">
                      <IconButton
                        size="small"
                        onClick={() => handleDismissAlert(`${type === 'lowStock' ? 'low' : type === 'outOfStock' ? 'out' : 'exp'}-${item.id}`)}
                      >
                        <CloseIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Collapse>
      </Paper>
    );
  };

  if (showInDashboard && totalAlerts === 0) {
    return (
      <Alert severity="success">
        <AlertTitle>All Good!</AlertTitle>
        No inventory alerts at this time.
      </Alert>
    );
  }

  if (showInDashboard) {
    return (
      <Box>
        {totalAlerts > 0 && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <AlertTitle>Inventory Alerts</AlertTitle>
            You have {totalAlerts} inventory alert{totalAlerts !== 1 ? 's' : ''} that need attention.
          </Alert>
        )}
        
        {lowStockProducts.slice(0, 3).map(product => (
          <Alert key={`dash-low-${product.id}`} severity="warning" sx={{ mb: 1 }}>
            <strong>{product.name}</strong> is running low ({product.currentStock} left)
          </Alert>
        ))}
        
        {outOfStockProducts.slice(0, 2).map(product => (
          <Alert key={`dash-out-${product.id}`} severity="error" sx={{ mb: 1 }}>
            <strong>{product.name}</strong> is out of stock
          </Alert>
        ))}
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold' }}>
          Inventory Alerts
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Badge badgeContent={totalAlerts} color="error">
            <NotificationsIcon />
          </Badge>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => setDismissedAlerts(new Set())}
          >
            Refresh Alerts
          </Button>
        </Box>
      </Box>

      {totalAlerts === 0 ? (
        <Alert severity="success">
          <AlertTitle>All Good!</AlertTitle>
          No inventory alerts at this time. All products are properly stocked.
        </Alert>
      ) : (
        <Box>
          <AlertSection
            title="Out of Stock Products"
            items={outOfStockProducts}
            type="outOfStock"
            icon={<ErrorIcon />}
            color="error"
          />
          
          <AlertSection
            title="Low Stock Products"
            items={lowStockProducts}
            type="lowStock"
            icon={<WarningIcon />}
            color="warning"
          />
          
          <AlertSection
            title="Expiring Products"
            items={expiringProducts}
            type="expiring"
            icon={<ScheduleIcon />}
            color="info"
          />
        </Box>
      )}

      {/* Restock Dialog */}
      <Dialog open={restockDialogOpen} onClose={() => setRestockDialogOpen(false)}>
        <DialogTitle>Restock Product</DialogTitle>
        <DialogContent>
          {selectedProduct && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {selectedProduct.name}
              </Typography>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Current Stock: {selectedProduct.currentStock} • Min Level: {selectedProduct.minStockLevel}
              </Typography>
              
              <TextField
                fullWidth
                label="Restock Quantity"
                type="number"
                value={restockQuantity}
                onChange={(e) => setRestockQuantity(e.target.value)}
                sx={{ mt: 2, mb: 2 }}
                inputProps={{ min: 1 }}
              />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Reason</InputLabel>
                <Select
                  value={restockReason}
                  onChange={(e) => setRestockReason(e.target.value)}
                  label="Reason"
                >
                  <MenuItem value="Regular restock">Regular restock</MenuItem>
                  <MenuItem value="Emergency restock">Emergency restock</MenuItem>
                  <MenuItem value="Bulk purchase">Bulk purchase</MenuItem>
                  <MenuItem value="Supplier delivery">Supplier delivery</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRestockDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleRestockSubmit} variant="contained">
            Restock
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InventoryAlerts;
