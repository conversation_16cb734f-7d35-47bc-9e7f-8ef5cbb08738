import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  InputAdornment,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import { useBilling } from '../contexts/BillingContext';

const InvoiceForm = ({ open, onClose, invoice = null, mode = 'add' }) => {
  const { createInvoice, updateInvoice, validateDiscount } = useBilling();
  
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    services: [
      {
        name: '',
        price: 0,
        quantity: 1,
        stylist: '',
        duration: 60
      }
    ],
    discountType: null,
    discountValue: 0,
    taxRate: 8.5,
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample services for autocomplete
  const availableServices = [
    { name: 'Hair Cut & Style', price: 85, duration: 60 },
    { name: 'Hair Color', price: 120, duration: 90 },
    { name: 'Hair Highlights', price: 150, duration: 120 },
    { name: 'Beard Trim', price: 35, duration: 30 },
    { name: 'Facial Treatment', price: 95, duration: 75 },
    { name: 'Manicure', price: 45, duration: 45 },
    { name: 'Pedicure', price: 55, duration: 60 },
    { name: 'Hair Wash & Blow Dry', price: 40, duration: 30 },
    { name: 'Deep Conditioning', price: 65, duration: 45 },
    { name: 'Eyebrow Shaping', price: 25, duration: 20 }
  ];

  // Sample stylists
  const availableStylists = [
    'Emma Wilson',
    'John Smith',
    'Sarah Davis',
    'Mike Johnson',
    'Lisa Brown'
  ];

  useEffect(() => {
    if (invoice && mode === 'edit') {
      setFormData({
        customerName: invoice.customerName || '',
        customerEmail: invoice.customerEmail || '',
        customerPhone: invoice.customerPhone || '',
        services: invoice.services || [{ name: '', price: 0, quantity: 1, stylist: '', duration: 60 }],
        discountType: invoice.discountType || null,
        discountValue: invoice.discountValue || 0,
        taxRate: invoice.taxRate || 8.5,
        notes: invoice.notes || ''
      });
    } else {
      // Reset form for add mode
      setFormData({
        customerName: '',
        customerEmail: '',
        customerPhone: '',
        services: [{ name: '', price: 0, quantity: 1, stylist: '', duration: 60 }],
        discountType: null,
        discountValue: 0,
        taxRate: 8.5,
        notes: ''
      });
    }
    setErrors({});
  }, [invoice, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleServiceChange = (index, field, value) => {
    const updatedServices = [...formData.services];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value
    };
    
    // Auto-fill price and duration when service is selected
    if (field === 'name') {
      const selectedService = availableServices.find(s => s.name === value);
      if (selectedService) {
        updatedServices[index].price = selectedService.price;
        updatedServices[index].duration = selectedService.duration;
      }
    }
    
    setFormData(prev => ({
      ...prev,
      services: updatedServices
    }));
  };

  const addService = () => {
    setFormData(prev => ({
      ...prev,
      services: [...prev.services, { name: '', price: 0, quantity: 1, stylist: '', duration: 60 }]
    }));
  };

  const removeService = (index) => {
    if (formData.services.length > 1) {
      setFormData(prev => ({
        ...prev,
        services: prev.services.filter((_, i) => i !== index)
      }));
    }
  };

  const calculateTotals = () => {
    const subtotal = formData.services.reduce((sum, service) => 
      sum + (service.price * service.quantity), 0
    );
    
    let discountAmount = 0;
    if (formData.discountType === 'percentage') {
      discountAmount = (subtotal * formData.discountValue) / 100;
    } else if (formData.discountType === 'fixed') {
      discountAmount = formData.discountValue;
    }
    
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = (afterDiscount * formData.taxRate) / 100;
    const total = afterDiscount + taxAmount;
    
    return {
      subtotal: parseFloat(subtotal.toFixed(2)),
      discountAmount: parseFloat(discountAmount.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2)),
      total: parseFloat(total.toFixed(2))
    };
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Customer email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Customer phone is required';
    }

    // Validate services
    formData.services.forEach((service, index) => {
      if (!service.name.trim()) {
        newErrors[`service_${index}_name`] = 'Service name is required';
      }
      if (service.price <= 0) {
        newErrors[`service_${index}_price`] = 'Service price must be greater than 0';
      }
      if (service.quantity <= 0) {
        newErrors[`service_${index}_quantity`] = 'Quantity must be greater than 0';
      }
      if (!service.stylist.trim()) {
        newErrors[`service_${index}_stylist`] = 'Stylist is required';
      }
    });

    if (formData.taxRate < 0) {
      newErrors.taxRate = 'Tax rate cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const totals = calculateTotals();
      const invoiceData = {
        ...formData,
        ...totals,
        customerId: Math.floor(Math.random() * 1000), // In real app, this would be from customer selection
        services: formData.services.map(service => ({
          ...service,
          price: Number(service.price),
          quantity: Number(service.quantity),
          duration: Number(service.duration)
        })),
        discountValue: Number(formData.discountValue),
        taxRate: Number(formData.taxRate)
      };

      if (mode === 'edit' && invoice) {
        updateInvoice(invoice.id, invoiceData);
      } else {
        createInvoice(invoiceData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving invoice:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const totals = calculateTotals();

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        {mode === 'edit' ? 'Edit Invoice' : 'Create New Invoice'}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Customer Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.customerName}
                onChange={handleChange('customerName')}
                error={!!errors.customerName}
                helperText={errors.customerName}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={formData.customerEmail}
                onChange={handleChange('customerEmail')}
                error={!!errors.customerEmail}
                helperText={errors.customerEmail}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Phone Number"
                value={formData.customerPhone}
                onChange={handleChange('customerPhone')}
                error={!!errors.customerPhone}
                helperText={errors.customerPhone}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneIcon />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>

            {/* Services */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2, mb: 2 }}>
                <Typography variant="h6">
                  Services
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={addService}
                >
                  Add Service
                </Button>
              </Box>
              
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Stylist</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Qty</TableCell>
                      <TableCell>Duration (min)</TableCell>
                      <TableCell>Total</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {formData.services.map((service, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Autocomplete
                            options={availableServices.map(s => s.name)}
                            value={service.name}
                            onChange={(event, newValue) => {
                              handleServiceChange(index, 'name', newValue || '');
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                size="small"
                                error={!!errors[`service_${index}_name`]}
                                helperText={errors[`service_${index}_name`]}
                                sx={{ minWidth: 200 }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <Autocomplete
                            options={availableStylists}
                            value={service.stylist}
                            onChange={(event, newValue) => {
                              handleServiceChange(index, 'stylist', newValue || '');
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                size="small"
                                error={!!errors[`service_${index}_stylist`]}
                                helperText={errors[`service_${index}_stylist`]}
                                sx={{ minWidth: 150 }}
                              />
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            size="small"
                            type="number"
                            value={service.price}
                            onChange={(e) => handleServiceChange(index, 'price', Number(e.target.value))}
                            error={!!errors[`service_${index}_price`]}
                            InputProps={{
                              startAdornment: <InputAdornment position="start">$</InputAdornment>,
                            }}
                            sx={{ width: 100 }}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            size="small"
                            type="number"
                            value={service.quantity}
                            onChange={(e) => handleServiceChange(index, 'quantity', Number(e.target.value))}
                            error={!!errors[`service_${index}_quantity`]}
                            sx={{ width: 80 }}
                            inputProps={{ min: 1 }}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            size="small"
                            type="number"
                            value={service.duration}
                            onChange={(e) => handleServiceChange(index, 'duration', Number(e.target.value))}
                            sx={{ width: 100 }}
                            inputProps={{ min: 1 }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {formatCurrency(service.price * service.quantity)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => removeService(index)}
                            disabled={formData.services.length === 1}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
          <Box>
            <Button onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
          </Box>
          
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="body2" color="textSecondary">
              Subtotal: {formatCurrency(totals.subtotal)}
            </Typography>
            {totals.discountAmount > 0 && (
              <Typography variant="body2" color="success.main">
                Discount: -{formatCurrency(totals.discountAmount)}
              </Typography>
            )}
            <Typography variant="body2" color="textSecondary">
              Tax ({formData.taxRate}%): {formatCurrency(totals.taxAmount)}
            </Typography>
            <Typography variant="h6" fontWeight="bold">
              Total: {formatCurrency(totals.total)}
            </Typography>
            
            <Button 
              onClick={handleSubmit}
              variant="contained"
              disabled={isSubmitting}
              sx={{ mt: 1 }}
            >
              {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Invoice' : 'Create Invoice')}
            </Button>
          </Box>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default InvoiceForm;
