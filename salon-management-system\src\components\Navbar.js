import React, { useState } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Box,
  useTheme,
  useMediaQuery,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  ContentCut as SalonIcon,
  Person,
  Logout,
  Login,
  AccountCircle,
  AdminPanelSettings,
  Badge,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user, logout, isAdmin, isStaff, isCustomer } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleNavigation = (path) => {
    navigate(path);
  };

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
    navigate('/login');
  };

  const handleProfile = () => {
    navigate('/profile');
    handleUserMenuClose();
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin':
        return <AdminPanelSettings />;
      case 'staff':
        return <Badge />;
      case 'customer':
        return <Person />;
      default:
        return <Person />;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'staff':
        return 'primary';
      case 'customer':
        return 'success';
      default:
        return 'default';
    }
  };

  // Filter menu items based on user role and permissions
  const getMenuItems = () => {
    const baseItems = [
      { text: 'Dashboard', path: '/', roles: ['admin', 'staff'] },
      { text: 'Appointments', path: '/appointments', roles: ['admin', 'staff', 'customer'] },
      { text: 'Customers', path: '/customers', roles: ['admin', 'staff'] },
      { text: 'Services', path: '/services', roles: ['admin', 'staff'] },
      { text: 'Inventory', path: '/inventory', roles: ['admin', 'staff'] },
      { text: 'Staff', path: '/staff', roles: ['admin'] },
      { text: 'Reports', path: '/reports', roles: ['admin'] },
    ];

    if (!user) return [];

    return baseItems.filter(item =>
      item.roles.includes(user.role) || user.role === 'admin'
    );
  };

  const menuItems = getMenuItems();

  return (
    <AppBar position="fixed" sx={{ zIndex: 1000 }}>
      <Toolbar sx={{ minHeight: '56px !important' }}>
        <SalonIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Salon Management System
        </Typography>

        {/* Navigation Menu - only show if user is logged in */}
        {user && (
          <>
            {!isMobile ? (
              <Box sx={{ display: 'flex', gap: 0.5, mr: 2 }}>
                {menuItems.map((item) => (
                  <Button
                    key={item.text}
                    color="inherit"
                    size="small"
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(255,255,255,0.2)',
                      },
                      textTransform: 'none',
                      fontSize: '0.875rem',
                      px: 2,
                    }}
                  >
                    {item.text}
                  </Button>
                ))}
              </Box>
            ) : (
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mr: 1 }}>
                {menuItems.slice(0, 2).map((item) => (
                  <Button
                    key={item.text}
                    color="inherit"
                    size="small"
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'rgba(255,255,255,0.2)',
                      },
                      textTransform: 'none',
                      fontSize: '0.75rem',
                      px: 1,
                      minWidth: 'auto',
                    }}
                  >
                    {item.text}
                  </Button>
                ))}
              </Box>
            )}
          </>
        )}

        {/* User Authentication Section */}
        {user ? (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {!isMobile && (
              <Chip
                icon={getRoleIcon(user.role)}
                label={user.role}
                color={getRoleColor(user.role)}
                size="small"
                variant="outlined"
                sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)' }}
              />
            )}
            <Button
              onClick={handleUserMenuOpen}
              color="inherit"
              startIcon={
                <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                  {user.name?.charAt(0).toUpperCase()}
                </Avatar>
              }
              sx={{ textTransform: 'none' }}
            >
              {!isMobile && user.name}
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleUserMenuClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem disabled>
                <ListItemIcon>
                  {getRoleIcon(user.role)}
                </ListItemIcon>
                <ListItemText
                  primary={user.name}
                  secondary={`${user.role.charAt(0).toUpperCase() + user.role.slice(1)}`}
                />
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleProfile}>
                <ListItemIcon>
                  <AccountCircle />
                </ListItemIcon>
                <ListItemText primary="Profile" />
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <Logout />
                </ListItemIcon>
                <ListItemText primary="Logout" />
              </MenuItem>
            </Menu>
          </Box>
        ) : (
          <Button
            color="inherit"
            startIcon={<Login />}
            onClick={() => navigate('/login')}
            sx={{ textTransform: 'none' }}
          >
            Login
          </Button>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
