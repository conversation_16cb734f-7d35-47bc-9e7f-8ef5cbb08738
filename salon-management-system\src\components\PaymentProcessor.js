import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Card,
  CardContent,
  Alert,
  Stepper,
  Step,
  StepLabel,
  InputAdornment,
  Chip,
  Divider
} from '@mui/material';
import {
  CreditCard as CreditCardIcon,
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
  Security as SecurityIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { useBilling } from '../contexts/BillingContext';

const PaymentProcessor = ({ open, onClose, invoice = null }) => {
  const { processPayment, validateDiscount, applyDiscount } = useBilling();
  
  const [activeStep, setActiveStep] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [discountCode, setDiscountCode] = useState('');
  const [appliedDiscount, setAppliedDiscount] = useState(null);
  const [discountError, setDiscountError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  
  const [cardDetails, setCardDetails] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: '',
    email: '',
    phone: ''
  });

  const [bankDetails, setBankDetails] = useState({
    accountNumber: '',
    routingNumber: '',
    accountType: 'checking',
    bankName: ''
  });

  const steps = ['Payment Method', 'Payment Details', 'Confirmation'];

  const handleDiscountApply = () => {
    if (!discountCode.trim()) {
      setDiscountError('Please enter a discount code');
      return;
    }

    const validation = validateDiscount(discountCode, invoice?.subtotal || 0);
    
    if (validation.valid) {
      setAppliedDiscount(validation.discount);
      setDiscountError('');
    } else {
      setDiscountError(validation.error);
      setAppliedDiscount(null);
    }
  };

  const calculateFinalAmount = () => {
    if (!invoice) return 0;
    
    let amount = invoice.subtotal;
    
    if (appliedDiscount) {
      if (appliedDiscount.type === 'percentage') {
        const discountAmount = Math.min(
          (amount * appliedDiscount.value) / 100,
          appliedDiscount.maxDiscount || Infinity
        );
        amount -= discountAmount;
      } else {
        amount -= appliedDiscount.value;
      }
    }
    
    // Add tax
    const taxAmount = (amount * (invoice.taxRate || 8.5)) / 100;
    return amount + taxAmount;
  };

  const handlePaymentProcess = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const paymentData = {
        invoiceId: invoice.id,
        amount: calculateFinalAmount(),
        method: paymentMethod,
        transactionId: `txn_${Date.now()}`,
        gateway: paymentMethod === 'card' ? 'stripe' : 'bank_transfer',
        cardLast4: paymentMethod === 'card' ? cardDetails.number.slice(-4) : null,
        cardBrand: paymentMethod === 'card' ? 'visa' : null
      };
      
      processPayment(paymentData);
      
      if (appliedDiscount) {
        applyDiscount(appliedDiscount.code);
      }
      
      setPaymentSuccess(true);
      setActiveStep(2);
    } catch (error) {
      console.error('Payment processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNext = () => {
    if (activeStep === 1) {
      handlePaymentProcess();
    } else {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleClose = () => {
    if (!isProcessing) {
      setActiveStep(0);
      setPaymentSuccess(false);
      setAppliedDiscount(null);
      setDiscountCode('');
      setDiscountError('');
      onClose();
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const renderPaymentMethodStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Select Payment Method
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              border: paymentMethod === 'card' ? 2 : 1,
              borderColor: paymentMethod === 'card' ? 'primary.main' : 'grey.300'
            }}
            onClick={() => setPaymentMethod('card')}
          >
            <CardContent sx={{ textAlign: 'center' }}>
              <CreditCardIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6">Credit/Debit Card</Typography>
              <Typography variant="body2" color="textSecondary">
                Visa, Mastercard, American Express
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              border: paymentMethod === 'bank' ? 2 : 1,
              borderColor: paymentMethod === 'bank' ? 'primary.main' : 'grey.300'
            }}
            onClick={() => setPaymentMethod('bank')}
          >
            <CardContent sx={{ textAlign: 'center' }}>
              <BankIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6">Bank Transfer</Typography>
              <Typography variant="body2" color="textSecondary">
                Direct bank account transfer
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Discount Code Section */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
        <Typography variant="subtitle1" gutterBottom>
          Have a discount code?
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={8}>
            <TextField
              fullWidth
              size="small"
              placeholder="Enter discount code"
              value={discountCode}
              onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}
              error={!!discountError}
              helperText={discountError}
            />
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleDiscountApply}
              disabled={!discountCode.trim()}
            >
              Apply
            </Button>
          </Grid>
        </Grid>
        
        {appliedDiscount && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="subtitle2">
              Discount Applied: {appliedDiscount.name}
            </Typography>
            <Typography variant="body2">
              {appliedDiscount.type === 'percentage' 
                ? `${appliedDiscount.value}% off` 
                : `${formatCurrency(appliedDiscount.value)} off`}
            </Typography>
          </Alert>
        )}
      </Box>
    </Box>
  );

  const renderPaymentDetailsStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Payment Details
      </Typography>
      
      {paymentMethod === 'card' ? (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Card Number"
              value={cardDetails.number}
              onChange={(e) => setCardDetails(prev => ({ ...prev, number: e.target.value }))}
              placeholder="1234 5678 9012 3456"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <CreditCardIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Expiry Date"
              value={cardDetails.expiry}
              onChange={(e) => setCardDetails(prev => ({ ...prev, expiry: e.target.value }))}
              placeholder="MM/YY"
            />
          </Grid>
          
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="CVV"
              value={cardDetails.cvv}
              onChange={(e) => setCardDetails(prev => ({ ...prev, cvv: e.target.value }))}
              placeholder="123"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <SecurityIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Cardholder Name"
              value={cardDetails.name}
              onChange={(e) => setCardDetails(prev => ({ ...prev, name: e.target.value }))}
            />
          </Grid>
        </Grid>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Bank Name"
              value={bankDetails.bankName}
              onChange={(e) => setBankDetails(prev => ({ ...prev, bankName: e.target.value }))}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Account Number"
              value={bankDetails.accountNumber}
              onChange={(e) => setBankDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
            />
          </Grid>
          
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Routing Number"
              value={bankDetails.routingNumber}
              onChange={(e) => setBankDetails(prev => ({ ...prev, routingNumber: e.target.value }))}
            />
          </Grid>
          
          <Grid item xs={6}>
            <FormControl fullWidth>
              <InputLabel>Account Type</InputLabel>
              <Select
                value={bankDetails.accountType}
                onChange={(e) => setBankDetails(prev => ({ ...prev, accountType: e.target.value }))}
                label="Account Type"
              >
                <MenuItem value="checking">Checking</MenuItem>
                <MenuItem value="savings">Savings</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      )}
    </Box>
  );

  const renderConfirmationStep = () => (
    <Box sx={{ textAlign: 'center' }}>
      {paymentSuccess ? (
        <>
          <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Payment Successful!
          </Typography>
          <Typography variant="body1" color="textSecondary" gutterBottom>
            Your payment has been processed successfully.
          </Typography>
          <Typography variant="h6" sx={{ mt: 2 }}>
            Amount Paid: {formatCurrency(calculateFinalAmount())}
          </Typography>
        </>
      ) : (
        <>
          <PaymentIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Processing Payment...
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Please wait while we process your payment.
          </Typography>
        </>
      )}
    </Box>
  );

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          Process Payment
          {invoice && (
            <Chip 
              label={`Invoice: ${invoice.id}`} 
              color="primary" 
              variant="outlined"
            />
          )}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Stepper activeStep={activeStep}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Payment Summary */}
        {invoice && (
          <Card sx={{ mb: 3, bgcolor: 'grey.50' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Payment Summary
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Subtotal:</Typography>
                <Typography>{formatCurrency(invoice.subtotal)}</Typography>
              </Box>
              {appliedDiscount && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography color="success.main">
                    Discount ({appliedDiscount.code}):
                  </Typography>
                  <Typography color="success.main">
                    -{formatCurrency(
                      appliedDiscount.type === 'percentage' 
                        ? Math.min((invoice.subtotal * appliedDiscount.value) / 100, appliedDiscount.maxDiscount || Infinity)
                        : appliedDiscount.value
                    )}
                  </Typography>
                </Box>
              )}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Tax ({invoice.taxRate || 8.5}%):</Typography>
                <Typography>
                  {formatCurrency((calculateFinalAmount() - (invoice.subtotal - (appliedDiscount ? 
                    (appliedDiscount.type === 'percentage' 
                      ? Math.min((invoice.subtotal * appliedDiscount.value) / 100, appliedDiscount.maxDiscount || Infinity)
                      : appliedDiscount.value) : 0))))}
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6">Total:</Typography>
                <Typography variant="h6" color="primary.main">
                  {formatCurrency(calculateFinalAmount())}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Step Content */}
        {activeStep === 0 && renderPaymentMethodStep()}
        {activeStep === 1 && renderPaymentDetailsStep()}
        {activeStep === 2 && renderConfirmationStep()}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        {!paymentSuccess && (
          <>
            <Button 
              onClick={handleClose} 
              disabled={isProcessing}
            >
              Cancel
            </Button>
            {activeStep > 0 && (
              <Button 
                onClick={handleBack}
                disabled={isProcessing}
              >
                Back
              </Button>
            )}
            {activeStep < 2 && (
              <Button 
                onClick={handleNext}
                variant="contained"
                disabled={isProcessing}
              >
                {activeStep === 1 ? (isProcessing ? 'Processing...' : 'Pay Now') : 'Next'}
              </Button>
            )}
          </>
        )}
        {paymentSuccess && (
          <Button onClick={handleClose} variant="contained">
            Close
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default PaymentProcessor;
