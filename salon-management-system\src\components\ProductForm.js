import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  InputAdornment
} from '@mui/material';
import { useInventory } from '../contexts/InventoryContext';

const ProductForm = ({ open, onClose, product = null, mode = 'add' }) => {
  const { addProduct, updateProduct, getCategories, getSuppliers } = useInventory();
  
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    brand: '',
    sku: '',
    currentStock: 0,
    minStockLevel: 0,
    maxStockLevel: 0,
    unitPrice: 0,
    supplier: '',
    description: '',
    expiryDate: '',
    location: '',
    barcode: '',
    usageRate: 0,
    status: 'active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = getCategories();
  const suppliers = getSuppliers();

  // Predefined categories for new products
  const predefinedCategories = [
    'Hair Care',
    'Hair Color',
    'Styling Products',
    'Nail Care',
    'Skincare',
    'Tools & Equipment',
    'Cleaning Supplies',
    'Other'
  ];

  const allCategories = [...new Set([...categories, ...predefinedCategories])];

  useEffect(() => {
    if (product && mode === 'edit') {
      setFormData({
        name: product.name || '',
        category: product.category || '',
        brand: product.brand || '',
        sku: product.sku || '',
        currentStock: product.currentStock || 0,
        minStockLevel: product.minStockLevel || 0,
        maxStockLevel: product.maxStockLevel || 0,
        unitPrice: product.unitPrice || 0,
        supplier: product.supplier || '',
        description: product.description || '',
        expiryDate: product.expiryDate || '',
        location: product.location || '',
        barcode: product.barcode || '',
        usageRate: product.usageRate || 0,
        status: product.status || 'active'
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: '',
        category: '',
        brand: '',
        sku: '',
        currentStock: 0,
        minStockLevel: 0,
        maxStockLevel: 0,
        unitPrice: 0,
        supplier: '',
        description: '',
        expiryDate: '',
        location: '',
        barcode: '',
        usageRate: 0,
        status: 'active'
      });
    }
    setErrors({});
  }, [product, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    if (!formData.brand.trim()) {
      newErrors.brand = 'Brand is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (formData.currentStock < 0) {
      newErrors.currentStock = 'Current stock cannot be negative';
    }

    if (formData.minStockLevel < 0) {
      newErrors.minStockLevel = 'Minimum stock level cannot be negative';
    }

    if (formData.maxStockLevel <= 0) {
      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';
    }

    if (formData.minStockLevel >= formData.maxStockLevel) {
      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';
    }

    if (formData.unitPrice <= 0) {
      newErrors.unitPrice = 'Unit price must be greater than 0';
    }

    if (!formData.supplier.trim()) {
      newErrors.supplier = 'Supplier is required';
    }

    if (formData.usageRate < 0) {
      newErrors.usageRate = 'Usage rate cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const productData = {
        ...formData,
        currentStock: Number(formData.currentStock),
        minStockLevel: Number(formData.minStockLevel),
        maxStockLevel: Number(formData.maxStockLevel),
        unitPrice: Number(formData.unitPrice),
        usageRate: Number(formData.usageRate)
      };

      if (mode === 'edit' && product) {
        updateProduct(product.id, productData);
      } else {
        addProduct(productData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving product:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {mode === 'edit' ? 'Edit Product' : 'Add New Product'}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.name}
                onChange={handleChange('name')}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Brand"
                value={formData.brand}
                onChange={handleChange('brand')}
                error={!!errors.brand}
                helperText={errors.brand}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.category} required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  onChange={handleChange('category')}
                  label="Category"
                >
                  {allCategories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
                {errors.category && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                    {errors.category}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SKU"
                value={formData.sku}
                onChange={handleChange('sku')}
                error={!!errors.sku}
                helperText={errors.sku}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleChange('description')}
                multiline
                rows={3}
              />
            </Grid>

            {/* Stock Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Stock Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Current Stock"
                type="number"
                value={formData.currentStock}
                onChange={handleChange('currentStock')}
                error={!!errors.currentStock}
                helperText={errors.currentStock}
                inputProps={{ min: 0 }}
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Minimum Stock Level"
                type="number"
                value={formData.minStockLevel}
                onChange={handleChange('minStockLevel')}
                error={!!errors.minStockLevel}
                helperText={errors.minStockLevel}
                inputProps={{ min: 0 }}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Maximum Stock Level"
                type="number"
                value={formData.maxStockLevel}
                onChange={handleChange('maxStockLevel')}
                error={!!errors.maxStockLevel}
                helperText={errors.maxStockLevel}
                inputProps={{ min: 1 }}
                required
              />
            </Grid>

            {/* Pricing and Supplier Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Pricing & Supplier
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Unit Price"
                type="number"
                value={formData.unitPrice}
                onChange={handleChange('unitPrice')}
                error={!!errors.unitPrice}
                helperText={errors.unitPrice}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                inputProps={{ min: 0, step: 0.01 }}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Supplier"
                value={formData.supplier}
                onChange={handleChange('supplier')}
                error={!!errors.supplier}
                helperText={errors.supplier}
                required
              />
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Additional Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Expiry Date"
                type="date"
                value={formData.expiryDate}
                onChange={handleChange('expiryDate')}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Storage Location"
                value={formData.location}
                onChange={handleChange('location')}
                placeholder="e.g., Storage Room A - Shelf 1"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Barcode"
                value={formData.barcode}
                onChange={handleChange('barcode')}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Usage Rate (per week)"
                type="number"
                value={formData.usageRate}
                onChange={handleChange('usageRate')}
                error={!!errors.usageRate}
                helperText={errors.usageRate}
                inputProps={{ min: 0, step: 0.1 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={handleChange('status')}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="discontinued">Discontinued</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Product' : 'Add Product')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductForm;
