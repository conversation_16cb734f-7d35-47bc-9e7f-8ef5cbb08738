import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  People,
  Event,
  Star,
  Download as DownloadIcon,
} from '@mui/icons-material';

const Reports = () => {
  const [dateRange, setDateRange] = useState('thisMonth');
  const [reportType, setReportType] = useState('overview');

  // Mock data for reports
  const overviewData = {
    revenue: {
      current: 15420,
      previous: 13850,
      change: 11.3,
    },
    appointments: {
      current: 186,
      previous: 172,
      change: 8.1,
    },
    customers: {
      current: 142,
      previous: 138,
      change: 2.9,
    },
    avgRating: {
      current: 4.7,
      previous: 4.6,
      change: 2.2,
    },
  };

  const topServices = [
    { name: 'Hair Cut & Style', bookings: 45, revenue: 3825, percentage: 24.8 },
    { name: 'Hair Color', bookings: 28, revenue: 4200, percentage: 27.2 },
    { name: 'Manicure', bookings: 38, revenue: 1710, percentage: 11.1 },
    { name: 'Facial Treatment', bookings: 22, revenue: 2640, percentage: 17.1 },
    { name: 'Pedicure', bookings: 31, revenue: 1705, percentage: 11.1 },
  ];

  const staffPerformance = [
    { name: 'Emma Wilson', appointments: 42, revenue: 4620, rating: 4.9, efficiency: 95 },
    { name: 'John Smith', appointments: 38, revenue: 2850, rating: 4.7, efficiency: 92 },
    { name: 'Mike Johnson', appointments: 35, revenue: 3920, rating: 4.8, efficiency: 88 },
    { name: 'Sarah Davis', appointments: 41, revenue: 2460, rating: 4.6, efficiency: 90 },
    { name: 'Lisa Anderson', appointments: 30, revenue: 1570, rating: 4.9, efficiency: 85 },
  ];

  const monthlyTrends = [
    { month: 'Jan', revenue: 12500, appointments: 145 },
    { month: 'Feb', revenue: 13200, appointments: 152 },
    { month: 'Mar', revenue: 14100, appointments: 168 },
    { month: 'Apr', revenue: 13850, appointments: 172 },
    { month: 'May', revenue: 15420, appointments: 186 },
  ];

  const customerInsights = [
    { metric: 'New Customers', value: 28, change: 12.5 },
    { metric: 'Returning Customers', value: 114, change: -2.1 },
    { metric: 'Customer Retention Rate', value: 78, change: 3.2 },
    { metric: 'Average Visit Value', value: 82.9, change: 5.8 },
  ];

  const dateRangeOptions = [
    { value: 'today', label: 'Today' },
    { value: 'thisWeek', label: 'This Week' },
    { value: 'thisMonth', label: 'This Month' },
    { value: 'lastMonth', label: 'Last Month' },
    { value: 'thisYear', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' },
  ];

  const reportTypeOptions = [
    { value: 'overview', label: 'Overview' },
    { value: 'revenue', label: 'Revenue Analysis' },
    { value: 'staff', label: 'Staff Performance' },
    { value: 'services', label: 'Service Analysis' },
    { value: 'customers', label: 'Customer Insights' },
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getChangeIcon = (change) => {
    return change > 0 ? <TrendingUp color="success" /> : <TrendingDown color="error" />;
  };

  const getChangeColor = (change) => {
    return change > 0 ? 'success.main' : 'error.main';
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Reports & Analytics</Typography>
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
        >
          Export Report
        </Button>
      </Box>

      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            select
            label="Date Range"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
          >
            {dateRangeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            select
            label="Report Type"
            value={reportType}
            onChange={(e) => setReportType(e.target.value)}
          >
            {reportTypeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Revenue
                  </Typography>
                  <Typography variant="h5">
                    {formatCurrency(overviewData.revenue.current)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getChangeIcon(overviewData.revenue.change)}
                    <Typography 
                      variant="body2" 
                      color={getChangeColor(overviewData.revenue.change)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(overviewData.revenue.change)}
                    </Typography>
                  </Box>
                </Box>
                <AttachMoney color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Appointments
                  </Typography>
                  <Typography variant="h5">
                    {overviewData.appointments.current}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getChangeIcon(overviewData.appointments.change)}
                    <Typography 
                      variant="body2" 
                      color={getChangeColor(overviewData.appointments.change)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(overviewData.appointments.change)}
                    </Typography>
                  </Box>
                </Box>
                <Event color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Customers
                  </Typography>
                  <Typography variant="h5">
                    {overviewData.customers.current}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getChangeIcon(overviewData.customers.change)}
                    <Typography 
                      variant="body2" 
                      color={getChangeColor(overviewData.customers.change)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(overviewData.customers.change)}
                    </Typography>
                  </Box>
                </Box>
                <People color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Avg Rating
                  </Typography>
                  <Typography variant="h5">
                    {overviewData.avgRating.current}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getChangeIcon(overviewData.avgRating.change)}
                    <Typography 
                      variant="body2" 
                      color={getChangeColor(overviewData.avgRating.change)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(overviewData.avgRating.change)}
                    </Typography>
                  </Box>
                </Box>
                <Star color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Top Services */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Top Services
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Service</TableCell>
                    <TableCell align="right">Bookings</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Share</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {topServices.map((service, index) => (
                    <TableRow key={index}>
                      <TableCell>{service.name}</TableCell>
                      <TableCell align="right">{service.bookings}</TableCell>
                      <TableCell align="right">{formatCurrency(service.revenue)}</TableCell>
                      <TableCell align="right">
                        <Chip 
                          label={`${service.percentage}%`} 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Staff Performance */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Staff Performance
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Staff Member</TableCell>
                    <TableCell align="right">Appointments</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Rating</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {staffPerformance.map((staff, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{staff.name}</Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={staff.efficiency} 
                            sx={{ mt: 0.5, height: 4 }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell align="right">{staff.appointments}</TableCell>
                      <TableCell align="right">{formatCurrency(staff.revenue)}</TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                          <Star sx={{ fontSize: 16, color: '#ffc107', mr: 0.5 }} />
                          {staff.rating}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Monthly Trends */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Monthly Trends
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Month</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Appointments</TableCell>
                    <TableCell align="right">Avg per Appointment</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {monthlyTrends.map((month, index) => (
                    <TableRow key={index}>
                      <TableCell>{month.month}</TableCell>
                      <TableCell align="right">{formatCurrency(month.revenue)}</TableCell>
                      <TableCell align="right">{month.appointments}</TableCell>
                      <TableCell align="right">
                        {formatCurrency(month.revenue / month.appointments)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Customer Insights */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Customer Insights
            </Typography>
            {customerInsights.map((insight, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {insight.metric}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="h6" sx={{ mr: 1 }}>
                      {insight.metric.includes('Rate') || insight.metric.includes('Value') 
                        ? `${insight.value}${insight.metric.includes('Rate') ? '%' : ''}`
                        : insight.value
                      }
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={getChangeColor(insight.change)}
                    >
                      {formatPercentage(insight.change)}
                    </Typography>
                  </Box>
                </Box>
                {index < customerInsights.length - 1 && <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 1 }} />}
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Reports;
