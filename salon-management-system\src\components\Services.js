import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Avatar,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  OutlinedInput,
  ListItemText,
  Checkbox,
  Divider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCut as HairIcon,
  Palette as ColorIcon,
  Face as FaceIcon,
  Spa as SpaIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Group as GroupIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Inventory as PackageIcon,
} from '@mui/icons-material';

const Services = () => {
  // Enhanced service data with comprehensive fields
  const [services, setServices] = useState([
    {
      id: 1,
      name: 'Hair Cut & Style',
      category: 'Hair',
      price: 85,
      duration: 60,
      description: 'Professional haircut with styling',
      popularity: 'High',
      icon: 'hair',
      status: 'active',
      requirements: ['Basic hair cutting skills', 'Styling techniques'],
      staffRequired: 1,
      assignedStaff: [1, 3], // Staff IDs who can perform this service
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 2,
      name: 'Hair Color',
      category: 'Hair',
      price: 150,
      duration: 120,
      description: 'Full hair coloring service',
      popularity: 'High',
      icon: 'color',
      status: 'active',
      requirements: ['Color theory', 'Chemical processing', 'Advanced techniques'],
      staffRequired: 1,
      assignedStaff: [1],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 3,
      name: 'Beard Trim',
      category: 'Hair',
      price: 35,
      duration: 30,
      description: 'Professional beard trimming and shaping',
      popularity: 'Medium',
      icon: 'hair',
      status: 'active',
      requirements: ['Beard trimming', 'Precision cutting'],
      staffRequired: 1,
      assignedStaff: [2],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 4,
      name: 'Facial Treatment',
      category: 'Skincare',
      price: 120,
      duration: 75,
      description: 'Deep cleansing facial with moisturizing',
      popularity: 'Medium',
      icon: 'face',
      status: 'active',
      requirements: ['Skincare knowledge', 'Facial techniques'],
      staffRequired: 1,
      assignedStaff: [5],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 5,
      name: 'Manicure',
      category: 'Nails',
      price: 45,
      duration: 45,
      description: 'Complete nail care and polish',
      popularity: 'High',
      icon: 'spa',
      status: 'active',
      requirements: ['Nail care techniques', 'Polish application'],
      staffRequired: 1,
      assignedStaff: [4],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 6,
      name: 'Pedicure',
      category: 'Nails',
      price: 55,
      duration: 60,
      description: 'Foot care and nail treatment',
      popularity: 'High',
      icon: 'spa',
      status: 'active',
      requirements: ['Foot care', 'Nail techniques'],
      staffRequired: 1,
      assignedStaff: [4],
      createdAt: new Date('2024-01-15').toISOString(),
    },
  ]);

  // Staff data for assignment
  const [staff] = useState([
    { id: 1, name: 'Emma Wilson', position: 'Senior Stylist', specialties: ['Hair Cut', 'Hair Color', 'Styling'] },
    { id: 2, name: 'John Smith', position: 'Barber', specialties: ['Men\'s Cuts', 'Beard Trim', 'Shaving'] },
    { id: 3, name: 'Mike Johnson', position: 'Hair Stylist', specialties: ['Hair Cut', 'Styling', 'Wedding Hair'] },
    { id: 4, name: 'Sarah Davis', position: 'Nail Technician', specialties: ['Manicure', 'Pedicure', 'Nail Art'] },
    { id: 5, name: 'Lisa Anderson', position: 'Esthetician', specialties: ['Facial', 'Skincare', 'Massage'] },
  ]);

  // Service packages
  const [packages, setPackages] = useState([
    {
      id: 1,
      name: 'Bridal Beauty Package',
      description: 'Complete bridal makeover package',
      services: [1, 2, 4], // Service IDs
      originalPrice: 355,
      packagePrice: 300,
      discount: 55,
      duration: 255,
      status: 'active',
    },
    {
      id: 2,
      name: 'Pamper Package',
      description: 'Relaxing spa day package',
      services: [4, 5, 6], // Service IDs
      originalPrice: 220,
      packagePrice: 180,
      discount: 40,
      duration: 180,
      status: 'active',
    },
  ]);

  // State management
  const [currentTab, setCurrentTab] = useState(0);
  const [open, setOpen] = useState(false);
  const [packageDialogOpen, setPackageDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState(null);
  const [editingService, setEditingService] = useState(null);
  const [editingPackage, setEditingPackage] = useState(null);
  const [errors, setErrors] = useState({});
  const [packageErrors, setPackageErrors] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    price: '',
    duration: '',
    description: '',
    popularity: 'Medium',
    icon: 'hair',
    status: 'active',
    requirements: [],
    staffRequired: 1,
    assignedStaff: [],
  });

  const [packageFormData, setPackageFormData] = useState({
    name: '',
    description: '',
    services: [],
    packagePrice: '',
    status: 'active',
  });

  const categories = ['Hair', 'Skincare', 'Nails', 'Wellness', 'Package'];
  const popularityLevels = ['Low', 'Medium', 'High'];
  const statusOptions = ['active', 'inactive'];
  const iconOptions = [
    { value: 'hair', label: 'Hair', icon: <HairIcon /> },
    { value: 'color', label: 'Color', icon: <ColorIcon /> },
    { value: 'face', label: 'Face', icon: <FaceIcon /> },
    { value: 'spa', label: 'Spa', icon: <SpaIcon /> },
  ];

  // Filtering and search functionality
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || service.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Calculate package pricing
  const calculatePackagePrice = (selectedServices) => {
    return selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.price : 0);
    }, 0);
  };

  const calculatePackageDuration = (selectedServices) => {
    return selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.duration : 0);
    }, 0);
  };

  const getServiceIcon = (iconType) => {
    switch (iconType) {
      case 'hair':
        return <HairIcon />;
      case 'color':
        return <ColorIcon />;
      case 'face':
        return <FaceIcon />;
      case 'spa':
        return <SpaIcon />;
      default:
        return <HairIcon />;
    }
  };

  const getPopularityColor = (popularity) => {
    switch (popularity) {
      case 'High':
        return 'success';
      case 'Medium':
        return 'warning';
      case 'Low':
        return 'default';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Hair':
        return '#8e24aa';
      case 'Skincare':
        return '#43a047';
      case 'Nails':
        return '#e91e63';
      case 'Wellness':
        return '#00acc1';
      case 'Package':
        return '#ff9800';
      default:
        return '#757575';
    }
  };

  const handleOpen = (service = null) => {
    if (service) {
      setEditingService(service);
      setFormData({
        name: service.name,
        category: service.category,
        price: service.price.toString(),
        duration: service.duration.toString(),
        description: service.description,
        popularity: service.popularity,
        icon: service.icon,
        status: service.status || 'active',
        requirements: service.requirements || [],
        staffRequired: service.staffRequired || 1,
        assignedStaff: service.assignedStaff || [],
      });
    } else {
      setEditingService(null);
      setFormData({
        name: '',
        category: '',
        price: '',
        duration: '',
        description: '',
        popularity: 'Medium',
        icon: 'hair',
        status: 'active',
        requirements: [],
        staffRequired: 1,
        assignedStaff: [],
      });
    }
    setOpen(true);
  };

  const handlePackageOpen = (pkg = null) => {
    if (pkg) {
      setEditingPackage(pkg);
      setPackageFormData({
        name: pkg.name,
        description: pkg.description,
        services: pkg.services,
        packagePrice: pkg.packagePrice.toString(),
        status: pkg.status,
      });
    } else {
      setEditingPackage(null);
      setPackageFormData({
        name: '',
        description: '',
        services: [],
        packagePrice: '',
        status: 'active',
      });
    }
    setPackageDialogOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingService(null);
    setErrors({});
  };

  const handlePackageClose = () => {
    setPackageDialogOpen(false);
    setEditingPackage(null);
  };

  const handleSave = () => {
    if (!validateServiceForm()) {
      return;
    }

    const serviceData = {
      ...formData,
      name: formData.name.trim(),
      description: formData.description.trim(),
      price: parseFloat(formData.price),
      duration: parseInt(formData.duration),
      staffRequired: parseInt(formData.staffRequired),
      createdAt: editingService ? editingService.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingService) {
      setServices(services.map(service =>
        service.id === editingService.id
          ? { ...serviceData, id: editingService.id }
          : service
      ));
    } else {
      const newService = {
        ...serviceData,
        id: Math.max(...services.map(s => s.id)) + 1,
      };
      setServices([...services, newService]);
    }
    handleClose();
  };

  const handlePackageSave = () => {
    const originalPrice = calculatePackagePrice(packageFormData.services);
    const packageData = {
      ...packageFormData,
      packagePrice: parseFloat(packageFormData.packagePrice),
      originalPrice,
      discount: originalPrice - parseFloat(packageFormData.packagePrice),
      duration: calculatePackageDuration(packageFormData.services),
      createdAt: editingPackage ? editingPackage.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingPackage) {
      setPackages(packages.map(pkg =>
        pkg.id === editingPackage.id
          ? { ...packageData, id: editingPackage.id }
          : pkg
      ));
    } else {
      const newPackage = {
        ...packageData,
        id: Math.max(...packages.map(p => p.id)) + 1,
      };
      setPackages([...packages, newPackage]);
    }
    handlePackageClose();
  };

  const handleDeleteClick = (service) => {
    setServiceToDelete(service);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (serviceToDelete) {
      setServices(services.filter(service => service.id !== serviceToDelete.id));
      setDeleteDialogOpen(false);
      setServiceToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setServiceToDelete(null);
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateServiceForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Service name must be at least 2 characters';
    }

    // Category validation
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    // Price validation
    const price = parseFloat(formData.price);
    if (!formData.price || isNaN(price)) {
      newErrors.price = 'Valid price is required';
    } else if (price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    } else if (price > 10000) {
      newErrors.price = 'Price seems too high (max $10,000)';
    }

    // Duration validation
    const duration = parseInt(formData.duration);
    if (!formData.duration || isNaN(duration)) {
      newErrors.duration = 'Valid duration is required';
    } else if (duration <= 0) {
      newErrors.duration = 'Duration must be greater than 0';
    } else if (duration > 480) {
      newErrors.duration = 'Duration cannot exceed 8 hours (480 minutes)';
    }

    // Description validation (optional but if provided, should have minimum length)
    if (formData.description && formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters if provided';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePackageInputChange = (field, value) => {
    setPackageFormData({ ...packageFormData, [field]: value });
  };

  // Helper functions
  const getStaffName = (staffId) => {
    const staffMember = staff.find(s => s.id === staffId);
    return staffMember ? staffMember.name : 'Unknown';
  };

  const getServiceName = (serviceId) => {
    const service = services.find(s => s.id === serviceId);
    return service ? service.name : 'Unknown';
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'default';
  };

  // Enhanced statistics
  const serviceStats = {
    total: services.length,
    active: services.filter(s => s.status === 'active').length,
    avgPrice: Math.round(services.reduce((sum, service) => sum + service.price, 0) / services.length),
    highPopularity: services.filter(s => s.popularity === 'High').length,
    totalPackages: packages.length,
    avgDuration: Math.round(services.reduce((sum, service) => sum + service.duration, 0) / services.length),
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Service Management</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<PackageIcon />}
            onClick={() => handlePackageOpen()}
          >
            Create Package
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpen()}
          >
            Add Service
          </Button>
        </Box>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Services" />
          <Tab label="Packages" />
        </Tabs>
      </Box>

      {/* Search and Filters */}
      {currentTab === 0 && (
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search services..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="Category"
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Typography variant="body2" color="text.secondary">
                {filteredServices.length} of {services.length} services
              </Typography>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Enhanced Service Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <GroupIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography color="textSecondary" variant="body2">
                  Total Services
                </Typography>
              </Box>
              <Typography variant="h4">
                {serviceStats.total}
              </Typography>
              <Typography variant="body2" color="success.main">
                {serviceStats.active} active
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MoneyIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography color="textSecondary" variant="body2">
                  Average Price
                </Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                ${serviceStats.avgPrice}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ScheduleIcon sx={{ mr: 1, color: 'info.main' }} />
                <Typography color="textSecondary" variant="body2">
                  Avg. Duration
                </Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                {serviceStats.avgDuration}m
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PackageIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography color="textSecondary" variant="body2">
                  Packages
                </Typography>
              </Box>
              <Typography variant="h4" color="secondary.main">
                {serviceStats.totalPackages}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Services Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {filteredServices.map((service) => (
            <Grid item xs={12} sm={6} md={4} key={service.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: getCategoryColor(service.category),
                        mr: 2,
                      }}
                    >
                      {getServiceIcon(service.icon)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="div">
                        {service.name}
                      </Typography>
                      <Typography color="text.secondary" variant="body2">
                        {service.category}
                      </Typography>
                    </Box>
                    <Chip
                      label={service.status}
                      color={getStatusColor(service.status)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {service.description}
                  </Typography>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h5" color="primary">
                      ${service.price}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {service.duration} min
                    </Typography>
                  </Box>

                  {/* Staff Assignment */}
                  {service.assignedStaff && service.assignedStaff.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Assigned Staff:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {service.assignedStaff.map((staffId) => (
                          <Chip
                            key={staffId}
                            label={getStaffName(staffId)}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {/* Requirements */}
                  {service.requirements && service.requirements.length > 0 && (
                    <Accordion sx={{ mt: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="body2">Requirements</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <List dense>
                          {service.requirements.map((req, index) => (
                            <ListItem key={index}>
                              <ListItemText primary={req} />
                            </ListItem>
                          ))}
                        </List>
                      </AccordionDetails>
                    </Accordion>
                  )}

                  <Box sx={{ mt: 2 }}>
                    <Chip
                      label={`${service.popularity} Popularity`}
                      color={getPopularityColor(service.popularity)}
                      size="small"
                    />
                  </Box>
                </CardContent>

                <CardActions>
                  <IconButton
                    size="small"
                    onClick={() => handleOpen(service)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteClick(service)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Packages Tab Content */}
      {currentTab === 1 && (
        <Grid container spacing={3}>
          {packages.map((pkg) => (
            <Grid item xs={12} sm={6} md={4} key={pkg.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                      <PackageIcon />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="div">
                        {pkg.name}
                      </Typography>
                      <Chip
                        label={pkg.status}
                        color={getStatusColor(pkg.status)}
                        size="small"
                      />
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {pkg.description}
                  </Typography>

                  {/* Package Services */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Included Services:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {pkg.services.map((serviceId) => (
                        <Chip
                          key={serviceId}
                          label={getServiceName(serviceId)}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Box>

                  {/* Pricing */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through' }}>
                        ${pkg.originalPrice}
                      </Typography>
                      <Typography variant="h5" color="primary">
                        ${pkg.packagePrice}
                      </Typography>
                      <Chip
                        label={`Save $${pkg.discount}`}
                        color="success"
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Duration: {pkg.duration} minutes
                    </Typography>
                  </Box>
                </CardContent>

                <CardActions>
                  <IconButton
                    size="small"
                    onClick={() => handlePackageOpen(pkg)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => {
                      if (window.confirm(`Are you sure you want to delete the package "${pkg.name}"?`)) {
                        setPackages(packages.filter(p => p.id !== pkg.id));
                      }
                    }}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Enhanced Add/Edit Service Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingService ? 'Edit Service' : 'Add New Service'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Basic Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={8}>
              <TextField
                fullWidth
                label="Service Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                select
                label="Status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                {statusOptions.map((status) => (
                  <MenuItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                required
                error={!!errors.category}
                helperText={errors.category}
              >
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Icon"
                value={formData.icon}
                onChange={(e) => handleInputChange('icon', e.target.value)}
              >
                {iconOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {option.icon}
                      {option.label}
                    </Box>
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                multiline
                rows={3}
                placeholder="Describe the service..."
                error={!!errors.description}
                helperText={errors.description}
              />
            </Grid>

            {/* Pricing and Duration */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Pricing & Duration</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Price ($)"
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                required
                error={!!errors.price}
                helperText={errors.price}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Duration (minutes)"
                type="number"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                required
                error={!!errors.duration}
                helperText={errors.duration}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                select
                label="Popularity"
                value={formData.popularity}
                onChange={(e) => handleInputChange('popularity', e.target.value)}
              >
                {popularityLevels.map((level) => (
                  <MenuItem key={level} value={level}>
                    {level}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Staff Requirements */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Staff Requirements</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Staff Required"
                type="number"
                value={formData.staffRequired}
                onChange={(e) => handleInputChange('staffRequired', e.target.value)}
                inputProps={{ min: 1 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Assigned Staff</InputLabel>
                <Select
                  multiple
                  value={formData.assignedStaff}
                  onChange={(e) => handleInputChange('assignedStaff', e.target.value)}
                  input={<OutlinedInput label="Assigned Staff" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={getStaffName(value)} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {staff.map((staffMember) => (
                    <MenuItem key={staffMember.id} value={staffMember.id}>
                      <Checkbox checked={formData.assignedStaff.indexOf(staffMember.id) > -1} />
                      <ListItemText primary={`${staffMember.name} (${staffMember.position})`} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Requirements (comma-separated)"
                value={formData.requirements.join(', ')}
                onChange={(e) => handleInputChange('requirements', e.target.value.split(', ').filter(req => req.trim()))}
                placeholder="e.g., Basic hair cutting skills, Color theory"
                helperText="Enter skill requirements separated by commas"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingService ? 'Update Service' : 'Add Service'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Package Creation/Edit Dialog */}
      <Dialog open={packageDialogOpen} onClose={handlePackageClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingPackage ? 'Edit Package' : 'Create New Package'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Package Name"
                value={packageFormData.name}
                onChange={(e) => handlePackageInputChange('name', e.target.value)}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={packageFormData.description}
                onChange={(e) => handlePackageInputChange('description', e.target.value)}
                multiline
                rows={2}
                placeholder="Describe the package..."
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Included Services</InputLabel>
                <Select
                  multiple
                  value={packageFormData.services}
                  onChange={(e) => handlePackageInputChange('services', e.target.value)}
                  input={<OutlinedInput label="Included Services" />}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={getServiceName(value)} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {services.filter(s => s.status === 'active').map((service) => (
                    <MenuItem key={service.id} value={service.id}>
                      <Checkbox checked={packageFormData.services.indexOf(service.id) > -1} />
                      <ListItemText
                        primary={service.name}
                        secondary={`$${service.price} - ${service.duration}min`}
                      />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {packageFormData.services.length > 0 && (
              <>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      <strong>Original Price:</strong> ${calculatePackagePrice(packageFormData.services)} |
                      <strong> Total Duration:</strong> {calculatePackageDuration(packageFormData.services)} minutes
                    </Typography>
                  </Alert>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Package Price ($)"
                    type="number"
                    value={packageFormData.packagePrice}
                    onChange={(e) => handlePackageInputChange('packagePrice', e.target.value)}
                    required
                    helperText={`Savings: $${Math.max(0, calculatePackagePrice(packageFormData.services) - (parseFloat(packageFormData.packagePrice) || 0))}`}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    select
                    label="Status"
                    value={packageFormData.status}
                    onChange={(e) => handlePackageInputChange('status', e.target.value)}
                  >
                    {statusOptions.map((status) => (
                      <MenuItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePackageClose}>Cancel</Button>
          <Button
            onClick={handlePackageSave}
            variant="contained"
            disabled={packageFormData.services.length === 0 || !packageFormData.packagePrice}
          >
            {editingPackage ? 'Update Package' : 'Create Package'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ color: 'error.main' }}>
          Confirm Delete Service
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to delete the service "{serviceToDelete?.name}"?
          </Typography>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone. All associated appointments and data will be affected.
          </Alert>
          {serviceToDelete && (
            <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Service Details:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Category:</strong> {serviceToDelete.category}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Price:</strong> ${serviceToDelete.price}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Duration:</strong> {serviceToDelete.duration} minutes
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            Delete Service
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Services;
