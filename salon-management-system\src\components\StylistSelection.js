import React, { useMemo } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  Rating,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
} from '@mui/material';
import {
  Person as PersonIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { useBooking } from '../contexts/BookingContext';

const StylistSelection = ({ onStylistSelect, onNext, onBack }) => {
  const { 
    selectedService, 
    selectedStylist, 
    getAvailableStylists 
  } = useBooking();

  // Get available stylists for the selected service
  const availableStylists = useMemo(() => {
    if (!selectedService) return [];
    return getAvailableStylists(selectedService.id);
  }, [selectedService, getAvailableStylists]);

  const handleStylistSelect = (stylist) => {
    onStylistSelect(stylist);
  };

  const handleNext = () => {
    if (selectedStylist && onNext) {
      onNext();
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Get working days text
  const getWorkingDaysText = (workingDays) => {
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return workingDays.map(day => dayNames[day]).join(', ');
  };

  // Generate avatar color based on stylist name
  const getAvatarColor = (name) => {
    const colors = ['primary', 'secondary', 'success', 'warning', 'info'];
    const index = name.length % colors.length;
    return colors[index];
  };

  if (!selectedService) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please select a service first.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Select a Stylist
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
        Choose from our qualified stylists for <strong>{selectedService.name}</strong>
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        All stylists shown are qualified to perform this service
      </Typography>

      {availableStylists.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No stylists are currently available for this service. Please try a different service or contact us directly.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {availableStylists.map((stylist) => (
            <Grid item xs={12} sm={6} md={4} key={stylist.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  border: selectedStylist?.id === stylist.id ? 2 : 1,
                  borderColor: selectedStylist?.id === stylist.id ? 'primary.main' : 'divider',
                  '&:hover': {
                    boxShadow: 3,
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
                onClick={() => handleStylistSelect(stylist)}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: `${getAvatarColor(stylist.name)}.main`,
                        width: 56,
                        height: 56,
                        mr: 2,
                      }}
                    >
                      <PersonIcon />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="div">
                        {stylist.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <Rating
                          value={stylist.rating}
                          precision={0.1}
                          size="small"
                          readOnly
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          ({stylist.rating})
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Specialties
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    {stylist.specialties.map((specialty, index) => (
                      <Chip
                        key={index}
                        label={specialty}
                        size="small"
                        variant="outlined"
                        color={
                          selectedService.name.toLowerCase().includes(specialty.toLowerCase()) ||
                          selectedService.category.toLowerCase().includes(specialty.toLowerCase())
                            ? 'primary'
                            : 'default'
                        }
                      />
                    ))}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <ScheduleIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      Working Hours
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {stylist.workingHours.start} - {stylist.workingHours.end}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {getWorkingDaysText(stylist.workingDays)}
                  </Typography>
                </CardContent>

                {selectedStylist?.id === stylist.id && (
                  <CardActions sx={{ justifyContent: 'center', bgcolor: 'primary.light' }}>
                    <CheckIcon sx={{ color: 'primary.contrastText', mr: 1 }} />
                    <Typography variant="body2" color="primary.contrastText">
                      Selected
                    </Typography>
                  </CardActions>
                )}
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Selected Stylist Summary */}
      {selectedStylist && (
        <Box sx={{ mt: 4, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom>
            Selected Stylist
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={8}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    bgcolor: `${getAvatarColor(selectedStylist.name)}.main`,
                    width: 48,
                    height: 48,
                    mr: 2,
                  }}
                >
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography variant="subtitle1">{selectedStylist.name}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Rating
                      value={selectedStylist.rating}
                      precision={0.1}
                      size="small"
                      readOnly
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      ({selectedStylist.rating})
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="body2" color="text.secondary">
                Working Hours: {selectedStylist.workingHours.start} - {selectedStylist.workingHours.end}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Days: {getWorkingDaysText(selectedStylist.workingDays)}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Validation Message */}
      {availableStylists.length > 0 && !selectedStylist && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Please select a stylist to continue to the next step.
        </Alert>
      )}

      {/* Navigation Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="outlined"
          size="large"
          onClick={handleBack}
        >
          Back: Select Service
        </Button>
        <Button
          variant="contained"
          size="large"
          onClick={handleNext}
          disabled={!selectedStylist}
        >
          Next: Select Date & Time
        </Button>
      </Box>
    </Box>
  );
};

export default StylistSelection;
