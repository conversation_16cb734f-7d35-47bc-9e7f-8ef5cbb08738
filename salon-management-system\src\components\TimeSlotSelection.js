import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Alert,
  Paper,
  IconButton,
  Divider,
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { format, addDays, startOfWeek, isSameDay, isToday, isBefore } from 'date-fns';
import { useBooking } from '../contexts/BookingContext';

const TimeSlotSelection = ({ onTimeSelect, onNext, onBack }) => {
  const {
    selectedService,
    selectedStylist,
    selectedDate,
    selectedTime,
    getAvailableTimeSlots,
  } = useBooking();

  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date(), { weekStartsOn: 1 })); // Start week on Monday

  // Generate week days
  const weekDays = useMemo(() => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(currentWeek, i));
    }
    return days;
  }, [currentWeek]);

  // Get available time slots for selected date
  const availableSlots = useMemo(() => {
    if (!selectedDate || !selectedStylist || !selectedService) return [];
    return getAvailableTimeSlots(selectedStylist.id, selectedDate, selectedService.duration);
  }, [selectedDate, selectedStylist, selectedService, getAvailableTimeSlots]);

  const handleDateSelect = (date) => {
    onTimeSelect(date, null);
  };

  const handleTimeSelect = (time) => {
    if (selectedDate) {
      onTimeSelect(selectedDate, time);
    }
  };

  const handleNext = () => {
    if (selectedDate && selectedTime && onNext) {
      onNext();
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  const goToPreviousWeek = () => {
    setCurrentWeek(addDays(currentWeek, -7));
  };

  const goToNextWeek = () => {
    setCurrentWeek(addDays(currentWeek, 7));
  };

  const goToCurrentWeek = () => {
    setCurrentWeek(startOfWeek(new Date(), { weekStartsOn: 1 }));
  };

  // Check if date is selectable (not in the past and stylist works on this day)
  const isDateSelectable = (date) => {
    if (isBefore(date, new Date()) && !isToday(date)) return false;
    if (!selectedStylist) return false;
    
    const dayOfWeek = date.getDay();
    return selectedStylist.workingDays.includes(dayOfWeek);
  };

  if (!selectedService || !selectedStylist) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please select a service and stylist first.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Select Date & Time
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
        Choose your preferred appointment time with <strong>{selectedStylist.name}</strong>
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Service: {selectedService.name} ({selectedService.duration} minutes)
      </Typography>

      {/* Week Navigation */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <IconButton onClick={goToPreviousWeek}>
            <ChevronLeftIcon />
          </IconButton>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6">
              {format(currentWeek, 'MMM d')} - {format(addDays(currentWeek, 6), 'MMM d, yyyy')}
            </Typography>
            <Button size="small" onClick={goToCurrentWeek}>
              Today
            </Button>
          </Box>
          
          <IconButton onClick={goToNextWeek}>
            <ChevronRightIcon />
          </IconButton>
        </Box>

        {/* Week Days */}
        <Grid container spacing={1}>
          {weekDays.map((date, index) => {
            const isSelected = selectedDate && isSameDay(date, new Date(selectedDate));
            const isSelectable = isDateSelectable(date);
            const isCurrentDay = isToday(date);

            return (
              <Grid item xs key={index}>
                <Card
                  sx={{
                    cursor: isSelectable ? 'pointer' : 'not-allowed',
                    opacity: isSelectable ? 1 : 0.5,
                    border: isSelected ? 2 : 1,
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    bgcolor: isCurrentDay ? 'primary.light' : 'background.paper',
                    '&:hover': isSelectable ? {
                      boxShadow: 2,
                    } : {},
                    transition: 'all 0.2s ease-in-out',
                  }}
                  onClick={() => isSelectable && handleDateSelect(format(date, 'yyyy-MM-dd'))}
                >
                  <CardContent sx={{ textAlign: 'center', py: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {format(date, 'EEE')}
                    </Typography>
                    <Typography variant="h6" sx={{ color: isCurrentDay ? 'primary.contrastText' : 'inherit' }}>
                      {format(date, 'd')}
                    </Typography>
                    {isSelected && (
                      <CheckIcon sx={{ fontSize: 16, color: 'primary.main', mt: 0.5 }} />
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Paper>

      {/* Time Slots */}
      {selectedDate && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">
              Available Times for {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}
            </Typography>
          </Box>

          {availableSlots.length === 0 ? (
            <Alert severity="info">
              No available time slots for this date. Please select a different date.
            </Alert>
          ) : (
            <Grid container spacing={2}>
              {availableSlots.map((slot, index) => {
                const isSelected = selectedTime === slot.time;
                
                return (
                  <Grid item xs={6} sm={4} md={3} key={index}>
                    <Button
                      fullWidth
                      variant={isSelected ? 'contained' : 'outlined'}
                      onClick={() => handleTimeSelect(slot.time)}
                      startIcon={<TimeIcon />}
                      sx={{
                        py: 1.5,
                        justifyContent: 'flex-start',
                      }}
                    >
                      {slot.time}
                    </Button>
                  </Grid>
                );
              })}
            </Grid>
          )}
        </Paper>
      )}

      {/* Selected Date & Time Summary */}
      {selectedDate && selectedTime && (
        <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider', mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Selected Appointment Time
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  {selectedTime}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  {selectedService.duration} minutes
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Validation Messages */}
      {!selectedDate && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Please select a date to see available time slots.
        </Alert>
      )}

      {selectedDate && !selectedTime && availableSlots.length > 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Please select a time slot to continue.
        </Alert>
      )}

      {/* Navigation Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="outlined"
          size="large"
          onClick={handleBack}
        >
          Back: Select Stylist
        </Button>
        <Button
          variant="contained"
          size="large"
          onClick={handleNext}
          disabled={!selectedDate || !selectedTime}
        >
          Next: Confirm Booking
        </Button>
      </Box>
    </Box>
  );
};

export default TimeSlotSelection;
