body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* React Big Calendar Styles */
.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  padding: 8px 6px;
  font-weight: 500;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.rbc-event {
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 12px;
  line-height: 1.2;
}

.rbc-time-view .rbc-time-gutter {
  background-color: #f9f9f9;
}

.rbc-time-slot {
  border-top: 1px solid #f0f0f0;
}

.rbc-today {
  background-color: rgba(142, 36, 170, 0.1);
}

.rbc-current-time-indicator {
  background-color: #8e24aa;
  height: 2px;
}
