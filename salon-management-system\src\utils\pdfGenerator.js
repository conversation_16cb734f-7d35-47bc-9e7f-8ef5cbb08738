import jsPDF from 'jspdf';

export const generateInvoicePDF = (invoice) => {
  const pdf = new jsPDF();
  
  // Set font
  pdf.setFont('helvetica');
  
  // Company Header
  pdf.setFontSize(20);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Salon Management System', 20, 30);
  
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('123 Beauty Street', 20, 40);
  pdf.text('City, State 12345', 20, 45);
  pdf.text('Phone: (*************', 20, 50);
  pdf.text('Email: <EMAIL>', 20, 55);
  
  // Invoice Title and Number
  pdf.setFontSize(24);
  pdf.setTextColor(40, 40, 40);
  pdf.text('INVOICE', 150, 30);
  
  pdf.setFontSize(14);
  pdf.setTextColor(60, 60, 60);
  pdf.text(invoice.id, 150, 40);
  
  // Invoice Details
  pdf.setFontSize(10);
  pdf.text(`Date: ${invoice.date}`, 150, 50);
  pdf.text(`Due Date: ${invoice.dueDate}`, 150, 55);
  pdf.text(`Status: ${invoice.status.toUpperCase()}`, 150, 60);
  
  // Customer Information
  pdf.setFontSize(12);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Bill To:', 20, 80);
  
  pdf.setFontSize(10);
  pdf.setTextColor(60, 60, 60);
  pdf.text(invoice.customerName, 20, 90);
  pdf.text(invoice.customerEmail, 20, 95);
  pdf.text(invoice.customerPhone, 20, 100);
  
  // Services Table Header
  const tableStartY = 120;
  pdf.setFontSize(10);
  pdf.setTextColor(40, 40, 40);
  
  // Table headers
  pdf.text('Service', 20, tableStartY);
  pdf.text('Stylist', 80, tableStartY);
  pdf.text('Price', 130, tableStartY);
  pdf.text('Duration', 160, tableStartY);
  
  // Draw header line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, tableStartY + 2, 180, tableStartY + 2);
  
  // Services Table Content
  let currentY = tableStartY + 10;
  pdf.setFontSize(9);
  pdf.setTextColor(60, 60, 60);
  
  invoice.services.forEach((service, index) => {
    pdf.text(service.name, 20, currentY);
    pdf.text(service.stylist, 80, currentY);
    pdf.text(`$${service.price.toFixed(2)}`, 130, currentY);
    pdf.text(`${service.duration} min`, 160, currentY);
    
    currentY += 8;
    
    // Add page break if needed
    if (currentY > 250) {
      pdf.addPage();
      currentY = 30;
    }
  });
  
  // Draw line before totals
  pdf.setDrawColor(200, 200, 200);
  pdf.line(130, currentY + 2, 180, currentY + 2);
  
  // Totals Section
  currentY += 15;
  pdf.setFontSize(10);
  pdf.setTextColor(60, 60, 60);
  
  // Subtotal
  pdf.text('Subtotal:', 130, currentY);
  pdf.text(`$${invoice.subtotal.toFixed(2)}`, 175, currentY);
  currentY += 8;
  
  // Discount (if applicable)
  if (invoice.discountAmount > 0) {
    pdf.setTextColor(0, 150, 0);
    pdf.text(`Discount (${invoice.discountType === 'percentage' ? `${invoice.discountValue}%` : 'Fixed'}):`, 130, currentY);
    pdf.text(`-$${invoice.discountAmount.toFixed(2)}`, 175, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
  }
  
  // Tax
  pdf.text(`Tax (${invoice.taxRate}%):`, 130, currentY);
  pdf.text(`$${invoice.taxAmount.toFixed(2)}`, 175, currentY);
  currentY += 8;
  
  // Total
  pdf.setDrawColor(40, 40, 40);
  pdf.line(130, currentY, 180, currentY);
  currentY += 8;
  
  pdf.setFontSize(12);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Total:', 130, currentY);
  pdf.text(`$${invoice.total.toFixed(2)}`, 175, currentY);
  
  // Payment Information (if paid)
  if (invoice.status === 'paid' && invoice.paymentMethod) {
    currentY += 20;
    pdf.setFontSize(10);
    pdf.setTextColor(0, 150, 0);
    pdf.text('Payment Information:', 20, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
    pdf.text(`Method: ${invoice.paymentMethod.toUpperCase()}`, 20, currentY);
    if (invoice.paymentDate) {
      currentY += 6;
      pdf.text(`Date: ${invoice.paymentDate}`, 20, currentY);
    }
    if (invoice.transactionId) {
      currentY += 6;
      pdf.text(`Transaction ID: ${invoice.transactionId}`, 20, currentY);
    }
  }
  
  // Notes (if any)
  if (invoice.notes) {
    currentY += 20;
    pdf.setFontSize(10);
    pdf.setTextColor(40, 40, 40);
    pdf.text('Notes:', 20, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
    
    // Split notes into multiple lines if too long
    const splitNotes = pdf.splitTextToSize(invoice.notes, 170);
    splitNotes.forEach((line) => {
      pdf.text(line, 20, currentY);
      currentY += 6;
    });
  }
  
  // Footer
  const pageHeight = pdf.internal.pageSize.height;
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  pdf.text('Thank you for your business!', 20, pageHeight - 20);
  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);
  
  // Save the PDF
  pdf.save(`invoice-${invoice.id}.pdf`);
};

export const generateInvoicePreview = (invoice) => {
  const pdf = new jsPDF();
  
  // Use the same generation logic as above
  // This function can be used to generate a preview without downloading
  
  // Return the PDF as a blob for preview
  return pdf.output('blob');
};

export const printInvoice = (invoice) => {
  const pdf = new jsPDF();
  
  // Generate PDF with same logic as generateInvoicePDF
  // But open in new window for printing instead of downloading
  
  const pdfUrl = pdf.output('bloburl');
  const printWindow = window.open(pdfUrl);
  
  if (printWindow) {
    printWindow.onload = () => {
      printWindow.print();
    };
  }
};
